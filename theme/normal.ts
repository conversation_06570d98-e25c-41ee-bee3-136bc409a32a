const normalTheme = {
  'font-family': '"Microsoft YaHei",-apple-system,sans-serif',
  'height-sm': '32px',
  'body-background': '#f7fafb', // 底色
  // 'component-background': '#EFF0F4', // 组件背景/画板背景
  'primary-color': '#2470ff', // 主题色
  'primary-1': '#4585FF', // 轻主题色
  'primary-2': '#1760E8', // 深主题色,
  'primary-3': '#91D5FF',
  'primary-4': '#E6F7FF',
  'processing-color': '#FF9E0F', // 进行中/等待/提醒
  'link-color': '#2470ff',
  'success-color': '#3BC243', // 成功色
  'warning-color': '#FF9E0F', // 警告色
  'error-color': '#F35159', // 错误色
  'font-size-base': '14px', // 主字号
  'heading-color': '#333', // 标题色
  'text-color': '#666', // 主文本色
  'text-color-secondary': '#999', // 次文本色
  'disabled-color': '#c1c2c1', // 失效色
  'border-radius-base': '2px', // 组件/浮层圆角
  // button
  // 'btn-border-radius-base': '2px',
  // 'btn-border-radius-sm': '2px',
  'btn-default-ghost-bg': '#EFF4FF',
  // Divider
  'divider-vertical-gutter': '4px',
  // Layout
  'layout-header-height': '60px', // layout组件header高度
  'layout-header-background': '#2470ff', // layout组件header背景,
  'layout-header-padding': '0 20px',
  'layout-footer-padding': '16px 20px',
  // Breadcrumb
  'breadcrumb-link-color-hover': '#4585FF',
  // Menu
  'menu-item-group-height': '40px',
  'menu-item-active-bg': 'rgba(#2470ff, 0.1)',
  'menu-item-active-border-width': '1px',
  'menu-horizontal-line-height': '40px',
  'menu-item-font-size': '16px',
  'menu-popup-bg': '#f7fafb',

  // Dropdown
  // Pagination
  'pagination-item-bg-active': '#2470ff',
  // Input
  'input-padding-horizontal': '12px',
  'input-placeholder-color': '#C1C2C1',
  'input-border-color': '#E9EDF0',
  'input-hover-border-color': '#2470ff',
  'input-disabled-bg': '#EFF0F4',
  // TimePicker
  // 'picker-basic-cell-hover-color': @item-hover-bg; // todo
  'picker-basic-cell-active-with-range-color': '#E6F7FF',
  'picker-basic-cell-disabled-bg': '#EFF0F4',
  // Form
  'label-required-color': '#F35159',
  'form-item-margin-bottom': '12px',
  'select-border-color': '#E9EDF0', // 下拉框边框颜色
  'border-color-base': '#E9EDF0', // 边框默认颜色
  'disabled-bg': '#EFF0F4', // 输入框/失效、禁用背景色
  'select-item-selected-color': 'rgb(36, 112, 255)',
  'select-dropdown-bg': 'rgba(255, 255, 255, 1)',
  'picker-bg': 'rgba(255, 255, 255, 1)', // 时间选择器背景
  'card-head-height': '52px', // 卡片头部高度
  'card-head-font-size': '16px', // 卡片头部字体大小
  // Steps
  'steps-icon-font-size': '14px',
  'steps-vertical-icon-width': '14px',
  'steps-vertical-tail-width': '14px',
  // Transfer
  // ---
  'transfer-disabled-bg': '#C1C2C1',
  'transfer-item-selected-hover-bg': 'rgba(##2470FF, .1)',
  // BackTop
  'back-top-bg': '#2470FF',
  'back-top-hover-bg': 'rgba(#2470FF, .2)',
};

export default normalTheme;
