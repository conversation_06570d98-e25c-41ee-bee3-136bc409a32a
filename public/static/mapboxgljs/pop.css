
.mapboxgl-popup-content{
    padding:0px;
 }
 .mapboxgl-popup-tip{
  	border:none;
 }
 .makerTop{
	padding: 10px 0 0;
}
.makerTop h2{
  margin: 0;
  font-size: 14px;
}
.markerBody{
  /* padding: 10px 0; */
  
}
.markerBody p{
  margin: 0;
}
.mapboxgl-popup-content {
  /* padding: 0 30px 0 15px; */
  padding: 0 5px 0 5px;
  background: rgba(226, 77, 29, 0.6);
  color: #fff;
}
.mapboxgl-popup-close-button{
  font-size: 18px;
  top: 5px;
}
 .measure-result {
  background-color: white;
  border-radius: 3px;
  height: 20px;
  line-height: 19px;
  padding: 0 3px;
  font-size: 16px;
  box-shadow: 0 0 0 1px #ccc;
  font-weight: bold;
  color: #000000;
  z-index: 9999;
 }
.echartBox .cont {
	display: flex;
	flex-direction: column;
	color:#fff;
	font-size:14px;
	text-align: center;
}
.customPop1{
	
} 
.customPop1 p{
	display: block;
	margin-block-start: 0px;
	margin-block-end: 0px;
	margin-inline-start: 0px;
	margin-inline-end: 0px;	
	font-size: 16px;
	line-height: 10px;
	color: white;
	margin-bottom: 20px;
}
.customPop1 .mapboxgl-popup-content {
	padding: 20px;
    position: relative;
    pointer-events: auto;
	background-image: url(../data/images/float1.png);
	background-size: 100% 150%;
	border:none;
	box-shadow:none;
	background-color: transparent;
}
.customPop1 .mapboxgl-popup-tip{
	border:none;
} 
.customPop1 .mapboxgl-popup-close-button{
	font-size: 20px;
	color: white;
	font-weight: bolder;
}

.customPop2{
	
}
.customPop2 p{
	display: block;
	margin-block-start: 0px;
	margin-block-end: 0px;
	margin-inline-start: 0px;
	margin-inline-end: 0px;	
	font-size: 16px;
	line-height: 10px;
	color: white;
	margin-bottom: 15px;
}
.customPop2 .mapboxgl-popup-content {
	padding: 20px;
    position: relative;
    pointer-events: auto;
	background-image: url(../data/images/float2.png);
	background-size: 100% 150%;
	border:none;
	box-shadow:none;
	background-color: transparent;
}
.customPop2 .mapboxgl-popup-tip{
	border:none;
}
.customPop2 .mapboxgl-popup-close-button{
	font-size: 20px;
	color: white;
	font-weight: bolder;
}

.customPop3{
	
} 
.customPop3 p{
	display: block;
	margin-block-start: 0px;
	margin-block-end: 0px;
	margin-inline-start: 0px;
	margin-inline-end: 0px;	
	font-size: 16px;
	line-height: 10px;
	color: white;
	margin-bottom: 15px;
}
.customPop3 .mapboxgl-popup-content {
	padding: 20px;
    position: relative;
    pointer-events: auto;
	background-image: url(../data/images/float3.png);
	background-size: 100% 150%;
	border:none;
	box-shadow:none;
	background-color: transparent;
} 
.customPop3 .mapboxgl-popup-tip{
	border:none;
} 
.customPop3 .mapboxgl-popup-close-button{
	font-size: 20px;
	color: white;
	font-weight: bolder;
}

