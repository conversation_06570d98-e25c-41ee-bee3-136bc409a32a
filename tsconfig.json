{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "skipLibCheck": true, "allowJs": true, "importHelpers": true, "noUnusedLocals": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "baseUrl": "./", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "/theme/*.ts", "node_modules/ant-design-vue/typings/global.d.ts"], "exclude": ["src/lib/**", "create-bundle.js"], "references": [{"path": "./tsconfig.node.json"}]}