{"name": "vue3-template", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:stats": "vue-tsc --noEmit && vite build --mode stats", "build:micro": "vue-tsc --noEmit && vite build --mode micro", "preview": "vite preview", "format": "prittier . --write", "lint": "eslint --ext .js,.vue  --fix src", "prepare": "husky install"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@codemirror/lang-javascript": "^6.0.2", "@codemirror/theme-one-dark": "^6.0.0", "@vueuse/core": "^9.0.2", "ant-design-vue": "^4.2.6", "axios": "^0.27.2", "codemirror": "^6.0.1", "dayjs": "^1.11.4", "echarts": "^5.3.3", "file-saver": "^2.0.5", "nprogress": "^0.2.0", "pinia": "^2.0.17", "qs": "^6.11.0", "vue": "^3.2.37", "vue-codemirror": "^6.0.2", "vue-request": "^1.2.4", "vue-router": "^4.1.3", "vxe-table": "^4.2.8", "xe-utils": "^3.5.6", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@iconify/json": "^2.1.86", "@iconify/tools": "^2.1.1", "@iconify/utils": "^2.0.2", "@iconify/vue": "^4.0.0", "@types/node": "^18.6.3", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "@vitejs/plugin-legacy": "^2.0.0", "@vitejs/plugin-vue": "^3.0.1", "@vitejs/plugin-vue-jsx": "^2.0.0", "autoprefixer": "^10.4.8", "eslint": "^8.0.1", "eslint-config-prettier": "^8.5.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.2.4", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^9.3.0", "husky": "^8.0.1", "less": "^4.1.3", "lint-staged": "^13.0.3", "postcss": "^8.4.16", "prettier": "^2.7.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.6.0", "sass": "^1.54.0", "stylelint": "^14.9.1", "stylelint-config-standard": "^26.0.0", "tailwindcss": "^3.1.8", "tslib": "^2.4.0", "typescript": "^4.7.4", "unplugin-auto-import": "^0.10.3", "unplugin-icons": "^0.14.8", "unplugin-vue-components": "^0.21.2", "vite": "^3.0.4", "vite-plugin-eslint": "^1.7.0", "vite-plugin-html": "^3.2.0", "vite-plugin-style-import": "^2.0.0", "vite-svg-loader": "^3.4.0", "vue-tsc": "^0.39.4"}, "lint-staged": {"*.{js,ts,jsx,vue,html}": ["prettier --write", "eslint --fix"], "*.css": "stylelint --fix"}, "vite": {}}