<template>
  <div class="app-container">
    <a-config-provider :locale="zhCN">
      <template #renderEmpty>
        <a-empty description="暂无数据">
          <template #image>
            <img src="@/assets/images/no-content.png" />
          </template>
        </a-empty>
      </template>
      <router-view />
    </a-config-provider>
  </div>
</template>
<script setup lang="ts">
import zhCN from 'ant-design-vue/es/locale/zh_CN';
</script>
<style lang="less" scoped>
.app-container {
  background: url('@/assets/images/an60.png') no-repeat center center fixed;
  background-size: cover;
}
</style>
