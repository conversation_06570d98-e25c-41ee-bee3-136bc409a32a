const typeOptiones = [
  
  // {
  //   label: '安全着装',
  //   value: '1000',
  //   options: [{
  //       value: '1001',
  //       label: '工服未穿戴报警'
  //     },
  //     {
  //       value: '1002',
  //       label: '安全帽未穿戴报警'
  //     }
  //   ]
  // },

  // {
  //   label: '烟火报警',
  //   value: '2000',
  //   options: [{
  //     value: '2001',
  //     label: '明火检测报警'
  //   }, {
  //     value: '2002',
  //     label: '烟雾检测报警'
  //   }, ]
  // },

  // {
  //   label: '人员入侵',
  //   value: '3000',
  //   options: [{
  //     value: '3001',
  //     label: '人员入侵报警'
  //   }, ]
  // },

  // {
  //   label: '危险行为',
  //   value: '4000',
  //   options: [{
  //       value: '4001',
  //       label: '人员倒地报警'
  //     },
  //     {
  //       value: '5001',
  //       label: '接打电话报警'
  //     },
  //     {
  //       value: '5002',
  //       label: '人员抽烟报警'
  //     },
  //   ]
  // },
  {
    label: '工帽穿戴',
    value: '6000',
    options: [{
        value: '6001',
        label: '穿戴工帽'
      },
      {
        value: '6002',
        label: '未穿戴工帽'
      }
    ]
  },
  {
    label: '工装穿戴',
    value: '7000',
    options: [{
        value: '7001',
        label: '穿戴工装'
      },
      {
        value: '7002',
        label: '未穿戴工装'
      }
    ]
  },
  {
    label: '明火报警',
    value: '8000',
    options: [{
        value: '8001',
        label: '检测到明火'
      },
      
    ]
  },
  {
    label: '烟雾报警',
    value: '9000',
    options: [{
        value: '9001',
        label: '检测到烟火'
      },
      
    ]
  },


]

const types = [{
    value: '',
    label: '全部'
  },
  // {
  //   value: '1000',
  //   label: '安全着装'
  // }, {
  //   value: '2000',
  //   label: '烟火报警'
  // }, {
  //   value: '3000',
  //   label: '人员入侵'
  // }, {
  //   value: '4000',
  //   label: '危险行为'
  // },
  {
    value: '6000',
    label: '工帽穿戴'
  },
  {
    value: '7000',
    label: '工装穿戴'
  },
  {
    value: '8000',
    label: '明火报警'
  },
  {
    value: '9000',
    label: '烟雾报警'
  }
]
export {
  typeOptiones,
  types
}