setHtmFontSize();
function setHtmFontSize() {
  const whdef = 100 / 1920; // 表示1920的设计图,使用100px的默认值
  // var wH = window.innerHeight;// 当前窗口的高度
  const wW = window.innerWidth; // 当前窗口的宽度
  const rem = wW * whdef; // 以默认比例值乘以当前窗口宽度,得到该宽度下的相应font-size值
  // $('html').css('font-size', rem + "px");
  document.getElementsByTagName('html')[0].style.fontSize = rem + 'px';
}
window.onresize = function () {
  let res;
  if (res) {
    clearTimeout(res);
  }
  res = setTimeout(function () {
    setHtmFontSize();
    // console.log("resize triggered");
  }, 20);
};
