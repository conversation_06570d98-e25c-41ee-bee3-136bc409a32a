// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
    font-weight: 400 !important;
}

.el-upload {
    input[type="file"] {
        display: none !important;
    }
}

.el-upload__input {
    display: none;
}

.cell {
    .el-tag {
        margin-right: 0px;
    }
}

.small-padding {
    .cell {
        padding-left: 5px;
        padding-right: 5px;
    }
}

.fixed-width {
    .el-button--mini {
        padding: 7px 10px;
        width: 60px;
    }
}

.status-col {
    .cell {
        padding: 0 10px;
        text-align: center;

        .el-tag {
            margin-right: 0px;
        }
    }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
    transform: none;
    left: 0;
    position: relative;
    margin: 0 auto;
}

// refine element ui upload
.upload-container {
    .el-upload {
        width: 100%;

        .el-upload-dragger {
            width: 100%;
            height: 200px;
        }
    }
}

// dropdown
.el-dropdown-menu {
    a {
        display: block
    }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
    display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
    box-sizing: content-box;
}

.el-message {
    font-size: 16px;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
    background-color: rgba(0, 0, 0, 0);
    border: 1px solid rgba(24, 255, 249, 0.4);
    border-radius: 0;
    color: rgba(24, 255, 249, 0.4);
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: rgba(24, 255, 249, 0.59);
    color: #ffffff;
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: rgba(24, 255, 249, 0.6);
}

.el-pagination.is-background .btn-next.disabled,
.el-pagination.is-background .btn-next:disabled,
.el-pagination.is-background .btn-prev.disabled,
.el-pagination.is-background .btn-prev:disabled,
.el-pagination.is-background .el-pager li.disabled {
    color: rgba(24, 255, 249, 0.2);
    border-color: rgba(24, 255, 249, 0.2);
}



.el-slider__button {
    border: 2px solid aqua;
}

.el-slider__bar {
    background-color: aqua;

}

.el-slider__marks-text {

    font-size: .12rem;

}

.el-carousel__arrow {
    height: .5rem;
    width: .5rem;
    font-size: .22rem;
}

// 查询按钮
.el-button--small,
.el-button--primary {
    &.global-query-btn {
        // background-image: linear-gradient(to right, rgba(9, 43, 129), rgba(91, 195, 237), rgba(9, 43, 129));
        border: 0;

        // &:hover {
        //     background-image: linear-gradient(to right, rgba(9, 45, 131), rgba(84, 197, 255), rgba(14, 53, 137));
        // }

        // &:active {
        //     background-image: linear-gradient(to right, rgba(7, 40, 127), rgba(34, 95, 172), rgba(7, 40, 127));
        // }
    }
}

// 自定义公共样式
.common-el-style {
    color: #fff;
    background-color: #073aa9;
    border: 0;
    box-shadow: 0 0 0.05rem rgba(134, 165, 231, 1) inset;
}

.common-el-date {
    background-color: #18FFF9;
    color: #000;
}

// 下拉框样式重置
.el-select .el-input .el-input__inner {
    &:extend(.common-el-style);
}

.el-select .el-select__tags {
    .el-tag.el-tag--info {
        // background: transparent;
        color: #000;
    }
}


.el-select-dropdown {
    background-color: #073AA9;
    border: 0;
    color: #fff;

    .el-scrollbar {
        ::-webkit-scrollbar {
            visibility: hidden !important;
        }

        .el-select-dropdown__item {
            color: #fff;

            &.hover,
            &.selected {
                background: linear-gradient(to right, rgba(24, 255, 249, 0.2), rgba(24, 255, 249, 0.02));
            }

            &.selected {
                color: #18FFF9;
            }
        }
    }
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
    background: linear-gradient(to right, rgba(24, 255, 249, 0.2), rgba(24, 255, 249, 0.02));
}

// 自定义 日期组件 样式
.el-date-editor .el-input__inner {
    &:extend(.common-el-style);
}

.el-range-editor {
    &:extend(.common-el-style);

    .el-range-input,
    .el-range-separator {
        background: none;
        color: inherit;
    }
}

.el-picker-panel {
    &:extend(.common-el-style);

    .el-date-picker__header button,
    span,
    .el-date-range-picker__header button,
    span,
    .el-picker-panel__content table th {
        color: inherit;
    }

    .el-date-table {
        td {
            &.in-range div {
                background: rgba(134, 165, 231, 0.5);
            }

            &:hover {
                color: #18FFF9;
            }

            &.current:not(.disabled) span,
            &.start-date span,
            &.end-date span {
                &:extend(.common-el-date);
            }
        }
    }

    .el-date-table.is-week-mode .el-date-table__row:hover,
    .el-date-table.is-week-mode .el-date-table__row.current {
        div {
            color: #18FFF9;
            background: rgba(134, 165, 231, 0.5);
        }
    }

    .el-year-table td,
    .el-month-table td {
        a {
            color: #fff;

            &:hover {
                color: #18FFF9;
            }
        }

        // 被选中状态
        &.current:not(.disabled) {
            background: rgba(134, 165, 231, 0.5);

            .cell {
                color: #18FFF9;
            }
        }

    }
}

// 自定义分页样式
.pagination {
    height: 0.3rem;
    display: flex;
    justify-content: flex-end;
    margin-top: 0.2rem;

    // 修改分页样式
    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
        background-color: rgba(0, 0, 0, 0);
        border: 1px solid rgba(24, 255, 249, 0.4);
        border-radius: 0;
        color: rgba(24, 255, 249, 0.4);
    }

    .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: rgba(24, 255, 249, 0.59);
        color: #ffffff;
    }

    .el-pagination.is-background .el-pager li:not(.disabled):hover {
        color: rgba(24, 255, 249, 0.6);
    }

    .el-pagination.is-background .btn-next.disabled,
    .el-pagination.is-background .btn-next:disabled,
    .el-pagination.is-background .btn-prev.disabled,
    .el-pagination.is-background .btn-prev:disabled,
    .el-pagination.is-background .el-pager li.disabled {
        color: rgba(24, 255, 249, 0.2);
        border-color: rgba(24, 255, 249, 0.2);
    }

    .el-pagination__total {
        color: #fff;
    }

    .el-pagination__jump {
        color: #fff;

        .el-input {
            margin: 0 10px;

            input {
                border-color: rgba(24, 255, 249, 0.2);
                background: none;
                color: #fff;
            }
        }
    }
}

// 自定义table样式
.custom-table {
    width: 99%;
    background: none;

    &::before {
        height: 0;
        display: none;
    }

    .el-table__header-wrapper {
        max-width: 100%;

        table {
            max-width: 100%;

            tr {
                background: none !important;

                th {
                    background: rgba(24, 255, 249, 0.1) !important;
                    color: #d2f0ff;
                    text-align: center;

                    .cell {

                        display: flex;
                        align-items: center;
                        justify-content: center;

                        &>div {
                            display: block;
                        }

                        // 排序图标修改
                        .caret-wrapper {

                            i {
                                font-size: 16px;
                                left: 6px;
                            }

                            .sort-caret {
                                width: 0;
                                height: 0;
                                border: 6px solid transparent;
                                position: absolute;
                            }

                            .ascending {
                                top: 2px;
                                border-bottom-color: #C0C4CC;
                            }

                            .descending {
                                bottom: 2px;
                                border-top-color: #C0C4CC;
                            }

                        }

                    }

                    &.is-leaf {
                        border: 0;
                        background: rgba(24, 255, 249, 0.1) !important;
                        color: #d2f0ff;
                        text-align: center;
                    }
                }


                .ascending .sort-caret.ascending {
                    border-bottom-color: #5dfaf8 !important;
                }

                .descending .sort-caret.descending {
                    border-top-color: #5dfaf8 !important;
                }
            }
        }
    }

    .el-table__body-wrapper {
        height: 100%;
        max-width: 100%;

        &::-webkit-scrollbar {
            height: 10px;
        }

        table {
            max-width: 100%;

            tr {
                background: none;


                &:hover {

                    td {
                        background: linear-gradient(to bottom, rgba(24, 255, 249, 0.2), rgba(24, 255, 249, 0.01)) !important;
                        color: #fff;
                    }
                }

                td {
                    background: none;
                    padding: 0;
                    border: 0;
                    height: 0.36rem;

                    .cell {
                        height: 100%;
                        padding: 0;
                        color: #d2f0ff;
                        font-size: 0.16rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;


                        div {
                            width: 100%;
                            height: 100%;
                            color: #d2f0ff;
                            font-size: 0.16rem;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            span {
                                width: 100%;
                                display: inline-block;
                                color: #d2f0ff;
                                font-size: 0.16rem;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                text-align: center;
                            }

                            // 查看按钮
                            .view-btn {
                                color: #18fff9;
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
        }
    }
}

.el-tabs {
    .el-tabs__header {
        margin: 0;

        .el-tabs__nav-wrap {
            &::after {
                background: rgba(134, 165, 231, 0.5);
            }

            .el-tabs__nav-scroll {
                .el-tabs__nav {
                    div {
                        color: #fff;

                        &.is-active {
                            font-size: 0.16rem;
                            box-shadow: none !important;
                        }
                    }

                    .el-tabs__active-bar {
                        background: #18FFF9;
                        box-shadow: none !important;
                    }
                }
            }
        }
    }

    .el-tabs__content {
        width: 0;
        height: 0;
        display: none;
    }
}


.el-tooltip__popper {
    max-width: 3rem;
}

// 自定义搜索form
.common-search-form {
    .el-form-item {
        .el-form-item__label {
            color: #fff;
            font-weight: normal;
        }

        .el-form-item__content {
            .el-input {
                input {
                    &:extend(.common-el-style);
                }
            }
        }
    }
}

.el-loading-spinner {
    font-size: 0.4rem;
}

// 日期 默认样式
.el-picker-panel.default-date-picker {
    color: #333;
    background: #fff !important;
    box-shadow: none;


    .el-date-table {
        td {
            &.in-range div {
                background: rgba(134, 165, 231, 0.5);
            }

            &:hover {
                color: #409eff;
            }
        }
    }
}

.default-date-picker>.el-input__inner {
    background-color: #fff;
    color: #333;
    box-shadow: 0 0 0.05rem #dcdfe6 inset
}

// 下拉框 默认样式
.default-select {
    z-index: 9999 !important;
}

.default-select.el-select-dropdown {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    color: #606266;

    .el-scrollbar {
        ::-webkit-scrollbar {
            // visibility: hidden !important;
        }

        .el-select-dropdown__item {
            color: #606266;

            &.hover,
            &.selected {
                background: #f5f7fa;
                // background: linear-gradient(to right, rgba(24, 255, 249, 0.2), rgba(24, 255, 249, 0.02));
            }

            &.selected {
                color: #409eff;
                ;
            }
        }
    }
}

.el-select.default-select {


    .el-input .el-input__inner {
        color: #606266;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
        box-shadow: none;
    }

}

.zIndex {
    z-index: 9999 !important;
}