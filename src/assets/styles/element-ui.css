.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}
.el-upload input[type="file"] {
  display: none !important;
}
.el-upload__input {
  display: none;
}
.cell .el-tag {
  margin-right: 0px;
}
.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}
.fixed-width .el-button--mini {
  padding: 7px 10px;
  width: 60px;
}
.status-col .cell {
  padding: 0 10px;
  text-align: center;
}
.status-col .cell .el-tag {
  margin-right: 0px;
}
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}
.upload-container .el-upload {
  width: 100%;
}
.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}
.el-dropdown-menu a {
  display: block;
}
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}
.el-range-separator {
  box-sizing: content-box;
}
.el-message {
  font-size: 16px;
}
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid rgba(24, 255, 249, 0.4);
  border-radius: 0;
  color: rgba(24, 255, 249, 0.4);
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: rgba(24, 255, 249, 0.59);
  color: #ffffff;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: rgba(24, 255, 249, 0.6);
}
.el-pagination.is-background .btn-next.disabled,
.el-pagination.is-background .btn-next:disabled,
.el-pagination.is-background .btn-prev.disabled,
.el-pagination.is-background .btn-prev:disabled,
.el-pagination.is-background .el-pager li.disabled {
  color: rgba(24, 255, 249, 0.2);
  border-color: rgba(24, 255, 249, 0.2);
}
.el-slider__button {
  border: 2px solid aqua;
}
.el-slider__bar {
  background-color: aqua;
}
.el-slider__marks-text {
  font-size: 0.12rem;
}
.el-carousel__arrow {
  height: 0.5rem;
  width: 0.5rem;
  font-size: 0.22rem;
}
.el-button--small.global-query-btn,
.el-button--primary.global-query-btn {
  border: 0;
}
.common-el-style,
.el-select .el-input .el-input__inner,
.el-date-editor .el-input__inner,
.el-range-editor,
.el-picker-panel,
.common-search-form .el-form-item .el-form-item__content .el-input input {
  color: #fff;
  background-color: #073aa9;
  border: 0;
  box-shadow: 0 0 0.05rem #86a5e7 inset;
}
.common-el-date,
.el-picker-panel .el-date-table td.current:not(.disabled) span,
.el-picker-panel .el-date-table td.start-date span,
.el-picker-panel .el-date-table td.end-date span {
  background-color: #18FFF9;
  color: #000;
}
.el-select-dropdown {
  background-color: #073AA9;
  border: 0;
  color: #fff;
}
.el-select-dropdown .el-scrollbar ::-webkit-scrollbar {
  visibility: hidden !important;
}
.el-select-dropdown .el-scrollbar .el-select-dropdown__item {
  color: #fff;
}
.el-select-dropdown .el-scrollbar .el-select-dropdown__item.hover,
.el-select-dropdown .el-scrollbar .el-select-dropdown__item.selected {
  background: #01609f;
  background: linear-gradient(to right, rgba(24, 255, 249, 0.2), rgba(24, 255, 249, 0.02));
}
.el-select-dropdown .el-scrollbar .el-select-dropdown__item.selected {
  color: #18FFF9;
}
.el-range-editor .el-range-input,
.el-range-editor .el-range-separator {
  background: none;
  color: inherit;
}
.el-picker-panel .el-date-picker__header button,
.el-picker-panel span,
.el-picker-panel .el-date-range-picker__header button,
.el-picker-panel span,
.el-picker-panel .el-picker-panel__content table th {
  color: inherit;
}
.el-picker-panel .el-date-table td.in-range div {
  background: rgba(134, 165, 231, 0.5);
}
.el-picker-panel .el-date-table td:hover {
  color: #18FFF9;
}
.el-picker-panel .el-date-table.is-week-mode .el-date-table__row:hover div,
.el-picker-panel .el-date-table.is-week-mode .el-date-table__row.current div {
  color: #18FFF9;
  background: rgba(134, 165, 231, 0.5);
}
.el-picker-panel .el-year-table td a,
.el-picker-panel .el-month-table td a {
  color: #fff;
}
.el-picker-panel .el-year-table td a:hover,
.el-picker-panel .el-month-table td a:hover {
  color: #18FFF9;
}
.el-picker-panel .el-year-table td.current:not(.disabled),
.el-picker-panel .el-month-table td.current:not(.disabled) {
  background: rgba(134, 165, 231, 0.5);
}
.el-picker-panel .el-year-table td.current:not(.disabled) .cell,
.el-picker-panel .el-month-table td.current:not(.disabled) .cell {
  color: #18FFF9;
}
.pagination {
  height: 0.3rem;
  display: flex;
  justify-content: flex-end;
  margin-top: 0.2rem;
}
.pagination .el-pagination.is-background .btn-next,
.pagination .el-pagination.is-background .btn-prev,
.pagination .el-pagination.is-background .el-pager li {
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid rgba(24, 255, 249, 0.4);
  border-radius: 0;
  color: rgba(24, 255, 249, 0.4);
}
.pagination .el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: rgba(24, 255, 249, 0.59);
  color: #ffffff;
}
.pagination .el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: rgba(24, 255, 249, 0.6);
}
.pagination .el-pagination.is-background .btn-next.disabled,
.pagination .el-pagination.is-background .btn-next:disabled,
.pagination .el-pagination.is-background .btn-prev.disabled,
.pagination .el-pagination.is-background .btn-prev:disabled,
.pagination .el-pagination.is-background .el-pager li.disabled {
  color: rgba(24, 255, 249, 0.2);
  border-color: rgba(24, 255, 249, 0.2);
}
.pagination .el-pagination__total {
  color: #fff;
}
.pagination .el-pagination__jump {
  color: #fff;
}
.pagination .el-pagination__jump .el-input {
  margin: 0 10px;
}
.pagination .el-pagination__jump .el-input input {
  border-color: rgba(24, 255, 249, 0.2);
  background: none;
  color: #fff;
}
.custom-table {
  width: 99%;
  background: none;
}
.custom-table::before {
  height: 0;
  display: none;
}
.custom-table .el-table__header-wrapper {
  max-width: 100%;
}
.custom-table .el-table__header-wrapper table {
  max-width: 100%;
}
.custom-table .el-table__header-wrapper table tr {
  background: none !important;
}
.custom-table .el-table__header-wrapper table tr th {
  background: rgba(24, 255, 249, 0.1) !important;
  color: #d2f0ff;
  text-align: center;
}
.custom-table .el-table__header-wrapper table tr th .cell {
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-table .el-table__header-wrapper table tr th .cell > div {
  display: block;
}
.custom-table .el-table__header-wrapper table tr th .cell .caret-wrapper i {
  font-size: 16px;
  left: 6px;
}
.custom-table .el-table__header-wrapper table tr th .cell .caret-wrapper .sort-caret {
  width: 0;
  height: 0;
  border: 6px solid transparent;
  position: absolute;
}
.custom-table .el-table__header-wrapper table tr th .cell .caret-wrapper .ascending {
  top: 2px;
  border-bottom-color: #C0C4CC;
}
.custom-table .el-table__header-wrapper table tr th .cell .caret-wrapper .descending {
  bottom: 2px;
  border-top-color: #C0C4CC;
}
.custom-table .el-table__header-wrapper table tr th.is-leaf {
  border: 0;
  background: rgba(24, 255, 249, 0.1) !important;
  color: #d2f0ff;
  text-align: center;
}
.custom-table .el-table__header-wrapper table tr .ascending .sort-caret.ascending {
  border-bottom-color: #5dfaf8 !important;
}
.custom-table .el-table__header-wrapper table tr .descending .sort-caret.descending {
  border-top-color: #5dfaf8 !important;
}
.custom-table .el-table__body-wrapper {
  height: 100%;
  max-width: 100%;
}
.custom-table .el-table__body-wrapper::-webkit-scrollbar {
  height: 10px;
}
.custom-table .el-table__body-wrapper table {
  max-width: 100%;
}
.custom-table .el-table__body-wrapper table tr {
  background: none;
}
.custom-table .el-table__body-wrapper table tr:hover td {
  background: linear-gradient(to bottom, rgba(24, 255, 249, 0.2), rgba(24, 255, 249, 0.01)) !important;
  color: #fff;
}
.custom-table .el-table__body-wrapper table tr td {
  background: none;
  padding: 0;
  border: 0;
  height: 0.36rem;
}
.custom-table .el-table__body-wrapper table tr td .cell {
  height: 100%;
  padding: 0;
  color: #d2f0ff;
  font-size: 0.16rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-table .el-table__body-wrapper table tr td .cell div {
  width: 100%;
  height: 100%;
  color: #d2f0ff;
  font-size: 0.16rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-table .el-table__body-wrapper table tr td .cell div span {
  width: 100%;
  display: inline-block;
  color: #d2f0ff;
  font-size: 0.16rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}
.custom-table .el-table__body-wrapper table tr td .cell div .view-btn {
  color: #18fff9;
  cursor: pointer;
}
.el-tabs .el-tabs__header {
  margin: 0;
}
.el-tabs .el-tabs__header .el-tabs__nav-wrap::after {
  background: rgba(134, 165, 231, 0.5);
}
.el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav div {
  color: #fff;
}
.el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav div.is-active {
  font-size: 0.16rem;
  box-shadow: none !important;
}
.el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__active-bar {
  background: #18FFF9;
  box-shadow: none !important;
}
.el-tabs .el-tabs__content {
  width: 0;
  height: 0;
  display: none;
}
.el-tooltip__popper {
  max-width: 3rem;
}
.common-search-form .el-form-item .el-form-item__label {
  color: #fff;
  font-weight: normal;
}
.el-loading-spinner {
  font-size: 0.4rem;
}
.el-picker-panel.default-date-picker {
  color: #333;
  background: #fff !important;
  box-shadow: none;
}
.el-picker-panel.default-date-picker .el-date-table td.in-range div {
  background: rgba(134, 165, 231, 0.5);
}
.el-picker-panel.default-date-picker .el-date-table td:hover {
  color: #409eff;
}
.default-date-picker > .el-input__inner {
  background-color: #fff;
  color: #333;
  box-shadow: 0 0 0.05rem #dcdfe6 inset;
}
.default-select {
  z-index: 9999 !important;
}
.default-select.el-select-dropdown {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  color: #606266;
}
.default-select.el-select-dropdown .el-scrollbar .el-select-dropdown__item {
  color: #606266;
}
.default-select.el-select-dropdown .el-scrollbar .el-select-dropdown__item.hover,
.default-select.el-select-dropdown .el-scrollbar .el-select-dropdown__item.selected {
  background: #f5f7fa;
}
.default-select.el-select-dropdown .el-scrollbar .el-select-dropdown__item.selected {
  color: #409eff;
}
.el-select.default-select .el-input .el-input__inner {
  color: #606266;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  box-shadow: none;
}
