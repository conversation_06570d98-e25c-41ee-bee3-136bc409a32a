/* Element UI 图标字体修复 - 微前端环境 */

/* 重新定义 Element UI 图标字体路径 - 使用内网路径 */
@font-face {
  font-family: "element-icons";
  src: url('http://localhost:1234/static/fonts/element-icons.woff') format('woff'),
       url('http://localhost:1234/static/fonts/element-icons.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 确保图标显示 */
[class^="el-icon-"], [class*=" el-icon-"] {
  font-family: "element-icons" !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 修复微前端环境下可能的样式冲突 */
.el-icon-menu:before {
  content: "\e798";
}

.el-icon-search:before {
  content: "\e773";
}

.el-icon-loading:before {
  content: "\e6cf";
}

.el-icon-close:before {
  content: "\e6db";
}

.el-icon-caret-right:before {
  content: "\e6e0";
}

.el-icon-s-promotion:before {
  content: "\e791";
}

/* 确保图标在微前端环境下正确显示 */
.qiankun-container [class^="el-icon-"], 
.qiankun-container [class*=" el-icon-"] {
  font-family: "element-icons" !important;
} 