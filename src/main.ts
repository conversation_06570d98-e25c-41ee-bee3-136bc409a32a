import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';

import './lib/icons-bundle';
import { createPinia } from 'pinia';
import AppComponent from './App.vue';
import router from './router';
import 'nprogress/nprogress.css';
import 'ant-design-vue/dist/reset.css';
import '@/styles/index.css';
import '@/styles/reset.ant.normal.less';
import VxeUITable from 'vxe-table';
import 'vxe-table/lib/style.css';

// 配置 dayjs 插件
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);
dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.locale('zh-cn');

const app = createApp(AppComponent);
app.use(router).use(createPinia()).use(VxeUITable).mount('#app');
