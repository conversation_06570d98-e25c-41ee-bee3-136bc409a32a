// 环保监控一张图 相关接口

import instance from "./request";
import base from "./base";

// 预警分布数据：
export function initAlarmStatistics(data) {
    return instance({
        url: `${base.url}/picture/initAlarmStatistics`,
        method: "post",
        data
    });
}

// 预警分布数据-报警时长：
export function initAlarmTimeStatistics(data) {
    return instance({
        url: `${base.url}/picture/initAlarmTimeStatistics`,
        method: "post",
        data
    });
}

// 预警分布数据-报警时长-弹出框列表：
export function initAlarmList(data) {
    return instance({
        url: `${base.url}/picture/initAlarmList`,
        method: "post",
        data
    });
}

// 报警趋势数据：
export function initAlarmTrendStatistics(data) {
    return instance({
        url: `${base.url}/picture/initAlarmTrendStatistics`,
        method: "post",
        data
    });
}

// 设备地图：
export function initDeviceMap(data) {
    return instance({
        url: `${base.url}/picture/getPointList`,
        method: "post",
        data
    });
}

// 根据id获取设备信息
export function getPointInfo(data) {
    return instance({
        url: `${base.url}/picture/getPointInfo`,
        method: "post",
        data
    });
}

// 污染源在线监测设备分布
export function initDeviceStatistics(data) {
    return instance({
        url: `${base.url}/picture/initDeviceStatistics`,
        method: "post",
        data
    });
}

// 污染源在线管理模块废水
export function initMonitWastewaterList(data) {
    return instance({
        url: `${base.url}/picture/initMonitWastewaterList`,
        method: "post",
        data
    });
}

// 污染源在线管理废气
export function initMonitWastegasList(data) {
    return instance({
        url: `${base.url}/picture/initMonitWasteGasList`,
        method: "post",
        data
    });
}

// 报警情况
export function initEnvinfoWarnList(data) {
    return instance({
        url: `${base.url}/picture/initEnvinfoWarnList`,
        method: "post",
        data
    });
}

// 获取甲烷模块组织机构
export function getJoinDept(params) {
    return instance({
        url: `${base.url3}/external-laserCloud/getJoinDept`,
        method: "get",
        params
    });
}

// 获取甲烷模块组织机构下点位
export function getDeptPoint(params) {
    return instance({
        url: `${base.url3}/external-laserCloud/getDeptPoint`,
        method: "get",
        params
    });
}

// 获取甲烷模块组织机构下点位下实时数据
export function getRealTimeData(params) {
    return instance({
        url: `${base.url3}/external-laserCloud/getRealTimeData`,
        method: "get",
        params
    });
}

// 获取甲烷模块组织机构下点位下历史数据
export function getHistoryData(params) {
    return instance({
        url: `${base.url3}/external-laserCloud/getHistoryData`,
        method: "get",
        params
    });
}

// -------- htx --------
// 树节点
export function getTreeNode(params) {
    return instance({
        url: `${base.url}/W58/getTreeNode`,
        method: "get",
        params
    });
}

// 摄像头列表
export function getCameraList(params) {
    return instance({
        url: `${base.url}/W14/pullCameraList`,
        method: "get",
        params
    });
}

// 摄像头视频流
export function getCameraStream(params) {
    return instance({
        url: `${base.url}/W14/getCameraStream`,
        method: "get",
        params
    });
}

// 检测点信息
export function getMonitorInfo(params) {
    return instance({
        url: `${base.url}/W14/getMonitorInfo`,
        method: "get",
        params
    });
}

// 实时监测结论
export function getLatestRecord(params) {
    return instance({
        url: `${base.url}/W41/getLatestRecord`,
        method: "get",
        params
    });
}

// 实时监测结论
export function getMonitorInfoById(params) {
    return instance({
        url: `${base.url}/W14/getMonitorInfoById`,
        method: "get",
        params
    });
}

// 监测点信息列表
export function getMonitorInfoList(params) {
    return instance({
        url: `${base.url}/W14/getMonitorInfoList`,
        method: "get",
        params
    });
}

// 根据上级id获取排放口列表
export function getSiteList(id) {
    return instance({
        url: `${base.url}/W14/getSiteList/` + id,
        method: "get",
    });
}

// 获取详情
export function getSiteInfo(id) {
    return instance({
        url: `${base.url}/W14/getSiteInfo/` + id,
        method: "get",
    });
}

// 获取近3小时数据
export function getMonitor3Hour(params) {
    return instance({
        url: `${base.url}/W14/getMonitor3Hour`,
        method: "get",
        params
    });
}

// 获取气体泄漏报警统计数据
export function getAlarmPage(data) {
    return instance({
        url: `${base.url}/alarm/page`,
        method: "post",
        data
    });
}

/**
 * 获取-监测点信息统计
 * @param params
 * @returns {*}
 */
export function getMonitorStatistics(params) {
    return instance({
        url: `${base.url}/W14/getMonitorStatistics`,
        method: "get",
        params
    });
}


/**
 * 获取-废水/废气实时监测
 * @param params
 * @returns {*}
 */
export function getMonitorList(params) {
    return instance({
        url: `${base.url}/W14/getMonitorList`,
        method: "get",
        params
    });
}



