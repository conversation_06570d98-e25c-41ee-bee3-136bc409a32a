export default{
    /**
     * @param {*} len 为8，16任意数字， 随机生成数字和字母组合字符串
     * @returns 
     */
    randomNum(len) {
        len = len || 32;
        var $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        var maxPos = $chars.length;
        var pwd = '';
        for (var i = 0; i < len; i++) {
            pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return pwd;
    },

    /**
     * 获取地址栏参数
     * @param {*} name 是要获取的字符
     * @returns 
     */
    getParam: function (name) {
        return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ""])[1].replace(/\+/g, '%20')) || null
    },

    /**
   *  转换时间格式
   * @param {*} unixTime 
   */
    getTime(unixTime) {
        unixTime = parseInt(unixTime);
        if (unixTime == null) {
        return '--';
        }
        if (unixTime.length > 10) {
        unixTime = unixTime.substring(0, 10);
        }
        var now = new Date(unixTime);
        var year = now.getFullYear() + '-';
        var month = (now.getMonth() + 1) + '-';
        if (now.getMonth() < 9) {
        month = '0' + (now.getMonth() + 1) + '-';
        }
        var date = now.getDate() + ' ';
        if (now.getDate() < 10) {
        date = '0' + now.getDate() + ' ';
        }
        var hours = now.getHours();
        if (hours < 10) {
        hours = '0' + hours;
        }
        var mins = now.getMinutes();
        if (mins < 10) {
        mins = '0' + mins;
        }
        var sec = now.getSeconds();
        if (sec < 10) {
        sec = '0' + sec;
        }
        var time = year + month + date + hours + ':' + mins + ':' + sec;
        return time;
    },
}