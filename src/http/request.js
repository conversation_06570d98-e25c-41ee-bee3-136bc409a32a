/**
 * axios封装
 * 请求拦截、响应拦截、错误统一处理
 */
import axios from 'axios';
import router from '../router';
import { message, Modal } from 'ant-design-vue';
import base from './base';

/**
 * 提示函数
 * 禁止点击蒙层、显示一秒后关闭
 */

// 变量记录只在第一次token过期才提醒
const TokenInvalidCounter = 0;

/**
 * 跳转登录页
 * 携带当前页面路由，以期在登录页面完成登录后返回当前页面
 */
const toLogin = () => {
  router.replace({
    path: '/',
    query: {
      redirect: router.currentRoute.fullPath,
    },
  });
};

// 请求超时限制
const TIMEOUT = 20 * 1000;

/**
 * 创建axios实例
 */
const instance = axios.create({
  timeout: TIMEOUT,
});

// 设置post请求头
instance.defaults.headers['Content-Type'] = 'application/json';

let currenturl = '';

/**
 * 请求拦截器
 * 每次请求前，如果存在token则在请求头中携带token
 */
instance.interceptors.request.use(
  config => {
    currenturl = config.url;
    // console.log('currenturl',currenturl)
    // 登录流程控制中，根据本地是否存在token判断用户的登录情况
    // 但是即使token存在，也有可能token是过期的，所以在每次的请求头中携带token
    // 后台根据携带的token判断用户的登录情况，并返回给我们对应的状态码
    // 而后我们可以在响应拦截器中，根据状态码进行一些统一的操作。
    //  debugger;暂时注释
    // if (!config.donNotShowLoading) {
    //   showLoading();
    // }
    //  config.data = JSON.stringify(config.data);
    let token = '';
    if (/^\/api/.test(currenturl)) {
      // console.log('api')
      token = 'Bearer fed2a9ec-abea-4b4d-947a-2adfed0e3f66';
    } else {
      // token = localStorage.getItem('localToken2') || ''
    }

    token && (config.headers.Authorization = token);
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      // debugger;
      let url = config.url + '?';
      for (const propName of Object.keys(config.params)) {
        const value = config.params[propName];
        const part = encodeURIComponent(propName) + '=';
        if (value !== null && typeof value !== 'undefined') {
          if (typeof value === 'object') {
            for (const key of Object.keys(value)) {
              const params = propName + '[' + key + ']';
              const subPart = encodeURIComponent(params) + '=';
              url += subPart + encodeURIComponent(value[key]) + '&';
            }
          } else {
            url += part + encodeURIComponent(value) + '&';
          }
        }
      }
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    return config;
  },
  error => {
    message.error(error);
    return error;
  }
);

/**
 * 响应拦截器
 */
instance.interceptors.response.use(
  // 请求成功
  res => {
    // 当响应码是 200 的情况, 进入这里
    // closeLoading();
    const code = res.code || 200;
    const msg = res.msg;
    if (res.data.ssuccess === false && !/^\/api/.test(res.config.url)) {
      console.log('cnpchse111');
      localStorage.setItem('localToken2', '');
    }
    if (code === 401) {
      if (/^\/api/.test(res.config.url)) {
        console.log('api_401');
        Modal.confirm({
          title: '系统提示',
          content: '登录状态已过期，您可以继续留在该页面，或者重新登录',
          okText: '重新登录',
          cancelText: '取消',
          onOk() {},
          onCancel() {},
        });
      } else {
        localStorage.setItem('localToken2', '');
      }

      // if (TokenInvalidCounter == 0) {
      //     Message({
      //         showClose: true,
      //         message: "登录过期，请重新登录",
      //         type: "warning"
      //     });
      // }
      // TokenInvalidCounter = 1;
      // setTimeout(() => {
      //     toLogin();
      // }, 1000);
    } else if (code === 500) {
      message.warning(msg);
    } else if (code !== 200 && res.data.code !== 0) {
      message.warning(msg);
    } else {
      return res.data;
    }
  },
  // 请求失败
  error => {
    // closeLoading();
    // const {
    //   status
    // } = error.response;
    // errorHandle(status, error.response);
    // 判断《已审核报警》分页列表
    // if (currenturl.includes("getAnyanEdgeStationInfoListHasAudit")) {
    // console.log("《已审核报警》 分页列表响应", currenturl);
    // const { message } = error;
    // console.log("网络错误：", message);
    // if (message === "Network Error" || message.includes(`timeout of ${TIMEOUT}ms exceeded`)) {
    //     Message({
    //         showClose: true,
    //         message: "网络异常，请刷新重试！",
    //         type: "error",
    //         duration: 6000
    //     });
    // }
    // }
    // return error;
    return Promise.reject(error);
  }
);

// 通用下载方法
export function download(url, params, filename) {
  return instance
    .post(base.url + url, params, {
      // transformRequest: [
      //     (params) => {
      //         return tansParams(params);
      //     }
      // ],
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      responseType: 'blob',
    })
    .then(data => {
      const content = data;
      const blob = new Blob([content]);
      if ('download' in document.createElement('a')) {
        const elink = document.createElement('a');
        elink.download = filename;
        elink.style.display = 'none';
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href);
        document.body.removeChild(elink);
      } else {
        navigator.msSaveBlob(blob, filename);
      }
    })
    .catch(r => {
      console.error(r);
    });
}

export default instance;
