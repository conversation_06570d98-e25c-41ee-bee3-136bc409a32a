import instance from "./request";
import base from "./base";

// 2级部门
export function getTowLevelDept() {
    return instance({
        url: `${base.url}/deptJy/getTowLevelDept`,
        method: "get",
    });
}

// 下级部门
export function getDeptList(deptId) {
    return instance({
        url: `${base.url}/deptJy/getDeptList/` + deptId,
        method: "get",
    });
}

// 字典
export function getDictList(dictType) {
    return instance({
        url: `${base.url}/dict/getDictList/` + dictType,
        method: "get",
    });
}

// 树结构
export function getTreeNodesWithoutOutlets() {
    return instance({
        url: `${base.url}/W58/getTreeNodesWithoutOutlets/`,
        method: "get",
    });
}

// -----基础信息-开始------
// 基础信息-查询
export function getMonitorPlanPage(params) {
    return instance({
        url: `${base.url}/monitor/getMonitorPlanPage`,
        method: "get",
        params
    });
}

// 基础信息-新增
export function addMonitorPlan(data) {
    return instance({
        url: `${base.url}/monitor/addMonitorPlan`,
        method: "post",
        data
    });
}

// 基础信息-删除
export function deleteMonitorPlan(id) {
    return instance({
        url: `${base.url}/monitor/deleteMonitorPlan/` + id,
        method: "delete",
    });
}

// 基础信息-详情
export function monitorPlanInfo(id) {
    return instance({
        url: `${base.url}/monitor/monitorPlanInfo/` + id,
        method: "get",
    });
}

// -----基础信息-结束------

// -----自行监测方案-开始------
// 新增
export function addMonitorResult(data) {
    return instance({
        url: `${base.url}/monitor/addMonitorResult`,
        method: "post",
        data
    });
}

// 查询
export function monitorResultPage(params) {
    return instance({
        url: `${base.url}/monitor/monitorResultPage`,
        method: "get",
        params
    });
}

// 详情
export function getMonitorResultInfo(id) {
    return instance({
        url: `${base.url}/monitor/getMonitorResultInfo/` + id,
        method: "get",
    });
}

// -----自行监测方案-结束------

// -----方案审批-开始------
//审批
export function auditMonitorResult(data) {
    return instance({
        url: `${base.url}/monitor/auditMonitorResult`,
        method: "post",
        data
    });
}


// -----方案审批-结束------

// -----统计-开始-----
export function monitorStatistics1(params) {
    return instance({
        url: `${base.url}/monitor/monitorStatistics1`,
        method: "get",
        params
    });
}

export function monitorStatistics2(params) {
    return instance({
        url: `${base.url}/monitor/monitorStatistics2`,
        method: "get",
        params
    });
}

export function monitorStatistics3(params) {
    return instance({
        url: `${base.url}/monitor/monitorStatistics3`,
        method: "get",
        params
    });
}

// -----统计-结束-----

// 自行监测结果-原因说明
export function addUnfinishedReason(data) {
    return instance({
        url: `${base.url}/monitor/addUnfinishedReason`,
        method: "post",
        data
    });
}

// 填报监测结果-填报
export function monitorResultChildFill(data) {
    return instance({
        url: `${base.url}/monitor/monitorResultChildFill`,
        method: "post",
        data
    });
}


// 方案详情-整改
export function monitorResultChildInfoFill(data) {
    return instance({
        url: `${base.url}/monitor/monitorResultChildInfoFill`,
        method: "post",
        data
    });
}

// 方案详情-详情
export function monitorResultChildInfo(id) {
    return instance({
        url: `${base.url}/monitor/monitorResultChildInfo/` + id,
        method: "get",
    });
}

// 计划详情-删除
export function deleteMonitorResult(id) {
    return instance({
        url: `${base.url}/monitor/deleteMonitorResult/` + id,
        method: "delete",
    });
}

// 计划详情-变更方案
export function updateMonitorPlan(data) {
    return instance({
        url: `${base.url}/monitor/updateMonitorPlan`,
        method: "post",
        data
    });
}


// 导入基础数据
export function importPlan(url) {
    return instance({
        url: `${base.url}/monitor/importPlan`,
        method: "get",
        params: {
            'filePath': url
        }
    });
}

// 导入基础数据
export function importResult(url) {
    return instance({
        url: `${base.url}/monitor/importResult`,
        method: "get",
        params: {
            'filePath': url
        }
    });
}


// 导入基础数据
export function monitorResultChildCount() {
    return instance({
        url: `${base.url}/monitor/monitorResultChildCount`,
        method: "post",
    });
}





