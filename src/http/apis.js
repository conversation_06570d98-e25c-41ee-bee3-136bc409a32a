import instance from "./request";
import base from "./base";

/** 用户登录 */
//1.登录方法
export function userLogin(data) {
    return instance({
        url: `${base.url}/auth/login`,
        method: "post",
        data: data
    });
}

// 2.获取验证码
export function getCodeImg() {
    return instance({
        url: `${base.url}/code`,
        method: "get"
    });
}

//3. 获取用户详细信息
export function getInfo() {
    return instance({
        url: `${base.url}/user/getInfo`,
        method: "get"
    });
}

// 4.退出系统
export function logout() {
    return instance({
        url: `${base.url}/auth/logout`,
        method: "delete"
    });
}
// 查询部门下拉树结构
export function treeselect() {
    return instance({
        url: `${base.url}/picture/initOrganizationTree`,
        method: "post"
    });
}

//视频监控左侧组织树
export function qhseTreeSelect() {
    return instance({
        url: `${base.url}/dept/qhseTreeSelect`,
        method: "get"
    });
}

/**智能分析模块 */

//1.获取报警信息数据
// export function getAlarmInformationData(data, pageparams) {
//   return instance({
//     url: `${base.url}/anyanEdgeStation/getAnyanEdgeStationInfoList?${pageparams}`,
//     method: 'post',
//     data: data
//   })
// }
export function getAlarmInformationData(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAnyanEdgeStationInfoList`,
        method: "get",
        params: data
    });
}

export function delAnyanEdgeStationInfoList(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/delAnyanEdgeStationInfoList`,
        method: "post",
        data: data
    });
}

export function deleteAlarmByIds(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/deleteAlarmByIds`,
        method: "post",
        data: data
    });
}

//2.获取智能分析数据

export function getAnalysisData(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAnyanEdgeStationReportForm`,
        method: "post",
        data: data
    });
}

// 3.获取报警类型字典表
export function getAlarmTypeDictArray() {
    return instance({
        url: `${base.url}/dict/data/type/alarm`,
        method: "get"
    });
}

// 报警审批结果字典列表
export function getAlarmApproval() {
    return instance({
        url: `${base.url}/dict/data/type/alarm_audit_type`,
        method: "get"
    });
}

//4.获取图片url接口地址
export function getalarmUrl(data) {
    return instance({
        url: `${base.url}/minio/getPresignedObjectUrl`,
        method: "post",
        data: data
    });
}

/**个人中心模块 */

// 1.查询用户个人信息
export function getUserProfile() {
    return instance({
        url: `${base.url}/user/profile`,
        method: "get"
    });
}

// 2.修改用户个人信息
export function updateUserProfile(data) {
    return instance({
        url: `${base.url}/user/profile`,
        method: "put",
        data: data
    });
}

// 3.用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
    const data = {
        oldPassword,
        newPassword
    };
    return instance({
        url: `${base.url}/user/profile/updatePwd`,
        method: "put",
        params: data
    });
}

// 4.用户头像上传
export function uploadAvatar(data) {
    return instance({
        url: `${base.url}/user/profile/avatar`,
        method: "post",
        data: data
    });
}

//5. 查询授权角色
export function getAuthRole(userId) {
    return instance({
        url: `${base.url}/user/authRole/` + userId,
        method: "get"
    });
}

//6.保存授权角色
export function updateAuthRole(data) {
    return instance({
        url: `${base.url}/user/authRole`,
        method: "put",
        params: data
    });
}

/**报警审核*/
//1.待审核列表
export function toBeReviewed(data, page) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAnyanEdgeStationInfoListForAudit?pageSize=${page.pageSize}&pageNum=${page.pageNum}`,
        method: "post",
        data: data
    });
}

//2.报警审核-已审核列表
export function reviewed(data, page) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAnyanEdgeStationInfoListHasAudit?pageSize=${page.pageSize}&pageNum=${page.pageNum}`,
        method: "post",
        data: data
    });
}

//3.报警审核-初审
export function firstAudit(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/firstAudit`,
        method: "post",
        data: data
    });
}

//4.报警审核-复审
export function secondAudit(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/secondAudit`,
        method: "post",
        data: data
    });
}
// 5.报警审核--误报列表(业务)
export function getErrorInfo(data, page) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAlarmErrorInfoList?pageSize=${page.pageSize}&pageNum=${page.pageNum}`,
        method: "post",
        data: data
    });
}

// 报警审核-- 算法误报
export function getAlgError(data, page) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAlgoAlarmErrorInfoList?pageSize=${page.pageSize}&pageNum=${page.pageNum}`,
        method: "post",
        data: data
    });
}
// 报警审核--超时处理
export function getOverTime(data, page) {
    return instance({
        url: `${base.url}/anyanAlarmManagerTimeout/list?pageSize=${page.pageSize}&pageNum=${page.pageNum}`,
        method: "post",
        data
    });
}

//6.承包商黑名单报警列表
export function getBlockInfo(page) {
    return instance({
        url: `${base.url}/consBlack/alarmList?pageSize=${page.pageSize}&pageNum=${page.pageNum}`,
        method: "get"
    });
}
//7.风险作业告警列表
export function getRiskalarm(query) {
    return instance({
        url: `${base.url}/riskLimit/alarms`,
        method: "get",
        params: query
    });
}

//8.获取隐藏的告警类型
export function getHideAlarm() {
    return instance({
        url: `${base.url}/anyanEdgeStation/getHideAlarm`,
        method: "get"
    });
}

//9.关键人员信息告警列表
export function getKeyperson(query) {
    return instance({
        url: `${base.url}/workPermitScore/keyPerson`,
        method: "get",
        params: query
    });
}
//10.承包商履职工作提示
export function getTipsList(query, page) {
    return instance({
        url: `${base.url}/constructorProbTips/getTipsList?pageSize=${page.pageSize}&pageNum=${page.pageNum}`,
        method: "post",
        data: query
    });
}
/**设备管理*/
//1. 设备列表
export function anyanDevice(data) {
    return instance({
        url: `${base.url}/anyanDevice/list`,
        method: "get",
        params: data
    });
}

//2. 设备推流开始
export function startTest(data) {
    return instance({
        url: `${base.url}/anyanDevice/startTest`,
        method: "post",
        data: data
    });
}

//3. 下载测试文件
export function downloadDeviceResult(params) {
    return instance({
        url: `${base.url}/anyanDevice/getSysFileList`,
        method: "get",
        params,
        responseType: "blob"
    });
}

/**现场实况 */

// 1.获取视频直播流
export function getcameraurl(data) {
    return instance({
        // url: `${base.url}/video/v2/cameras/previewURLs`,
        url: `${base.url}/hikPlatform/previewURLs`, // 新
        method: "post",
        data: data
    });
}

// 2.控制云台
export function controlcamera(data) {
    return instance({
        // url: `${base.url}/video/v1/ptzs/controlling`,
        url: `${base.url}/hikPlatform/controlling`, // 新
        method: "post",
        data: data
    });
}

//3.视频监控机构设备树
export function getvideoTree(cameraType, isAgencies = 0) {
    return instance({
        url: `${base.url}/dept/deptAndDevTree?cameraType=${cameraType}&isAgencies=${isAgencies}`,
        method: "get"
    });
}

//4.获取视频在线状态
export function getVideoStatus(data) {
    return instance({
        // url: `${base.url}/getCamera`,
        url: `${base.url}/hikPlatform/getCamera`, // 新
        method: "post",
        data: data
    });
}
//首页-ai实时预警信息，暂弃用
export function alarmHistoryDada(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/alarmHistoryData`,
        method: "post",
        data: data
    });
}

//首页查询监督人员信息
export function getHseForname(name) {
    return instance({
        url: `${base.url}/hseweb/getHseSupervisorByName?userName=${name}`,
        method: "get"
    });
}
/**风险作业 */

// 1.获取作业许可证
export function getWorkLicensesPage(data, pageNum, pageSize) {
    return instance({
        url: `${base.url}/work/getWorkLicensesPage?pageSize=${pageSize}&pageNum=${pageNum}`,
        method: "post",
        data: data
    });
}

//2.获取培训授权清单
export function getworkPermitScore(data, pageNum, pageSize) {
    return instance({
        url: `${base.url}/workPermitScore/page?pageSize=${pageSize}&pageNum=${pageNum}`,
        method: "post",
        data: data
    });
}

// 3.风险作业目录
export function getworkcontrol(data, pageNum, pageSize) {
    return instance({
        url: `${base.url}/workPermitScore/workSceneControlPage?pageSize=${pageSize}&pageNum=${pageNum}`,
        method: "post",
        data: data
    });
}

//4.公示公开
export function getriskpublick(data, pageNum, pageSize) {
    return instance({
        url: `${base.url}/workPermitScore/riskPublicityPage?pageSize=${pageSize}&pageNum=${pageNum}`,
        method: "post",
        data: data
    });
}

//5.一级公示开工图表，饼图及柱状图
export function getriskActivitySecond(data) {
    return instance({
        url: `${base.url}/workPermitScore/riskActivitySecond`,
        method: "post",
        data: data
    });
}
//6.一级公示开工图表，月周日
export function getriskActivityAnalyse(data) {
    return instance({
        url: `${base.url}/workPermitScore/riskActivityAnalyse`,
        method: "post",
        data: data
    });
}

//7.二级及三级公示图表

export function getriskActivityByName(data) {
    return instance({
        url: `${base.url}/workPermitScore/riskActivityByName`,
        method: "post",
        data: data
    });
}

//字典表
export function getType(data) {
    return instance({
        url: `${base.url}/dict/data/type/${data}`,
        method: "get"
    });
}

//作业许可证组织机构
export function getWorkTree(query) {
    return instance({
        url: `${base.url}/dept/workOrgTreeSelect`,
        method: "post",
        data: query
    });
}

//风险作业隐藏组织机构
export function riskworkOrgTree() {
    return instance({
        url: `${base.url}/dept/treeselectEx?isAgencies=0`,
        method: "get"
    });
}
/**报监项目*/
// 获取单位扇图和基本信息
export function getProblemStatisticTotal(query) {
    return instance({
        url: `${base.url}/hseProject/getProblemStatisticTotal`,
        method: "post",
        data: query
    });
}

//获取二级单位下三级单位的报监项目数量表
export function getProblemStatisticForOrgName(query) {
    return instance({
        url: `${base.url}/hseProject/getProblemStatisticForOrgName `,
        method: "post",
        data: query
    });
}

//获取三级单位的项目列表
export function getProblemStatistic({ pageSize = 10, pageNum = 1 }, query) {
    return instance({
        url: `${base.url}/hseProject/getProblemStatistic?pageSize=${pageSize}&pageNum=${pageNum}`,
        method: "post",
        data: query
    });
}

//获取三级单位的问题列表
export function getProblemPage({ pageSize = 10, pageNum = 1 }, query) {
    return instance({
        url: `${base.url}/hseProject/getProblemPage?pageSize=${pageSize}&pageNum=${pageNum}`,
        method: "post",
        data: query
    });
}

/**统计分析 */
// C监督工作执行
// 1.监督机构及监督人员树接口
export function getQhseOrgTree() {
    return instance({
        url: `${base.url}/dept/qhseOrgTreeSelect`,
        method: "get"
    });
}
//D智能分析
//1.//查询告警饼图
export function AlarmData(query) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAlarmData`,
        method: "get",
        params: query
    });
}
//查询告警趋势图
export function AlarmTrend(query) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAlarmTrend`,
        method: "get",
        params: query
    });
}
// 查询设备状态(边缘小站在线/离线数量)
export function getEquipStatus() {
    return instance({
        url: `${base.url}/anyanDevice/getDeviceOnlineInfo`,
        method: "get"
    });
}
// 风险作业--作业许可证统计接口
export function getWorkPermitStatic(data) {
    return instance({
        url: `${base.url}/work/getWorkLicensesStatistic`,
        method: "post",
        data: data
    });
}
//  获取首页综合统计数据
export function getHomeAllData() {
    return instance({
        url: `${base.url}/home/<USER>
        method: "get"
    });
}

//现场检查问题
export function getSceneProblem(query) {
    return instance({
        url: `${base.url}/home/<USER>
        method: "post",
        data: query
    });
}

//问题汇总
export function getProblemSummaryInfo(query) {
    return instance({
        url: `${base.url}/home/<USER>
        method: "post",
        data: query
    });
}

//获取人员定位人员数据
export function getHsePersonnelLocation() {
    return instance({
        url: `${base.url}/home/<USER>
        method: "get"
    });
}

//根据场站名称获取场站基本信息
export function getStationInfoByName(name) {
    return instance({
        url: `${base.url}/hseweb/getStationInfoByName?name=${name}`,
        method: "get"
    });
}

//首页-风险作业接口
export function getRiskActivity(name) {
    return instance({
        url: `${base.url}/home/<USER>
        method: "get"
    });
}

//告警分类统计数量
export function getAlarmCount(query) {
    return instance({
        url: `${base.url}/anyanEdgeStation/getAlarmCount`,
        method: "get",
        params: query
    });
}

// 视频回放
export function videoPlayBack(data) {
    return instance({
        // url: `${base.url}/video/v2/cameras/playbackURLs`,
        url: `${base.url}/hikPlatform/playbackURLs`, // 新
        method: "post",
        data: data
    });
}

// 风险作业公示--组织机构
export function getRiskWorkOrg(query) {
    return instance({
        url: `${base.url}/dept/treeselectRisk`,
        method: "get",
        params: query
    });
}
// 部门类型字典表
export function getDeptDic() {
    return instance({
        url: `${base.url}/dict/data/type/sys_dept_type`,
        method: "get"
    });
}

// 摄像头类型字典表
export function getVideoTypeDict() {
    return instance({
        url: `${base.url}/dict/data/type/camera_type`,
        method: "get"
    });
}
// 视频播放心跳
export function sendPing(data) {
    return instance({
        url: `${base.url}/hikPlatform/viewPing`,
        method: "post",
        data: data
    });
}

// 回退告警(HSE监督中心)
export function rollbackAlarm(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/rollBackToFirstAudit`,
        method: "post",
        data: data
    });
}
// 回退告警(二级单位审核员)
export function rollbackSecond(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/secondRollBackToFirstAudit`,
        method: "post",
        data: data
    });
}

// 告警列表获取视频回放流地址
export function playbackURLsForAlarm(data) {
    return instance({
        url: `${base.url}/hikPlatform/playbackURLsForAlarm`,
        method: "post",
        data: data
    });
}

// hse监督人员查看报警时请求接口
export function hseViewAlarm(data) {
    return instance({
        url: `${base.url}/anyanEdgeStation/hseViewAlarm`,
        method: "post",
        data: data
    });
}

// 作业许可审核：新增
export function workPermitCheckAdd(data) {
    return instance({
        url: `${base.url}/workPermitAudit`,
        method: "post",
        data
    });
}

// 作业许可审核：修改
export function workPermitCheckChange(data) {
    return instance({
        url: `${base.url}/workPermitAudit`,
        method: "put",
        data
    });
}

// 作业许可审核：获取审批信息
export function getWorkPermitInfoById(id) {
    return instance({
        url: `${base.url}/workPermitAudit/${id}`,
        method: "get"
    });
}

// 分级审核时间配置
export function getFenJiCheckTimeConfig() {
    return instance({
        url: `${base.url}/anyanAlarmManagerSuperviseConfig/list`,
        method: "get"
    });
}
