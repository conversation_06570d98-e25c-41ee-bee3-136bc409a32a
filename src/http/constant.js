// 废水
export const waterCode = [
  {
    label: "COD",
    value: "cwater011",
  },
  {
    label: "COD排放量",
    value: "cwater011All",
  },
  {
    label: "氨氮",
    value: "cwater060",
  },
  {
    label: "氨氮",
    value: "cwater060",
  },
  {
    label: "氨氮排放量",
    value: "cwater060All",
  },
  {
    label: "石油类",
    value: "cwater080",
  },
  {
    label: "石油排放量",
    value: "cwater080All",
  },
  {
    label: "悬浮物",
    value: "cwater003",
  },
  {
    label: "悬浮物排放量",
    value: "cwater003All",
  },
  {
    label: "BOD",
    value: "cwater010",
  },
  {
    label: "BOD排放量",
    value: "cwater010All",
  },
  {
    label: "总氮",
    value: "cwater065",
  },
  {
    label: "总氮排放量",
    value: "cwater065All",
  },
  {
    label: "PH",
    value: "cwater001",
  },
]

// 废气
export const gasCode = [
  {
    label: "SO₂实测",
    value: "cair02R",
  },
  {
    label: "SO₂折算",
    value: "cair02",
  },
  {
    label: "NOX实测",
    value: "cair03R",
  },
  {
    label: "NOX折算",
    value: "cair03",
  },
  {
    label: "NOX排放量",
    value: "cair03All",
  },
  {
    label: "烟尘实测",
    value: "cair01R",
  },
  {
    label: "烟尘折算",
    value: "cair01",
  },
  {
    label: "烟尘排放量",
    value: "cair01All",
  },
  {
    label: "温度",
    value: "cairS03",
  },
  {
    label: "压力",
    value: "cairS08",
  },
  {
    label: "湿度",
    value: "cairS05",
  },
  {
    label: "氧含量",
    value: "cairS01",
  },
]

// 异常类型
export const excetptionTypeCode = [
  {
    value: "1",
    label: "报警下限",
  },
  {
    value: "2",
    label: "报警上限",
  },
  {
    value: "3",
    label: "异常下限",
  },
  {
    value: "4",
    label: "异常",
  },
]

// 监测点类型
export const monitorTypeCode = [
  {
    value: "10",
    label: "废水",
  },
  {
    value: "20",
    label: "废气",
  },
]

// 实时监测结论
export const latestRecordCode = [
  {
    label: "监测点名称",
    value: "monitorName",
  },
  {
    label: "监测点类型",
    value: "monitorType",
  },
  {
    label: "站点名称",
    value: "site",
  },
  {
    label: "检测项目名称",
    value: "paramEName",
  },
  {
    label: "检测值",
    value: "mvalue",
  },
  {
    label: "超标原因",
    value: "abnormalReason",
  },
  {
    label: "异常类型",
    value: "exceptionType",
  },
  {
    label: "异常上限",
    value: "maxValue",
  },
  {
    label: "异常下限",
    value: "minValue",
  },
  {
    label: "超标上限",
    value: "upperValue",
  },
  {
    label: "超标下限",
    value: "lowerValue",
  },
]
