// 监测文件 相关接口
import instance from "./request"
import base from "./base"

/**
 * 查询
 * @returns {*}
 */
export function getPage(params) {
    return instance({
        url: `${base.url}/qhMonitorFile/getPage`, method: "get", params
    });
}

/**
 * 新增
 * @returns {*}
 */
export function addOrUpdate(data) {
    return instance({
        url: `${base.url}/qhMonitorFile/addOrUpdate`, method: "post", data
    });
}

/**
 * 启动or停用
 * @returns {*}
 */
export function updateStartType(params) {
    return instance({
        url: `${base.url}/qhMonitorFile/updateStartType`, method: "get", params
    });
}

/**
 * 删除
 * @param ids
 * @returns {*}
 */
export function deleteData(ids) {
    return instance({
        url: `${base.url}/qhMonitorFile/delete/` + ids, method: 'delete'
    })
}

/**
 * 获取所有文件列表数据
 * @returns {*}
 */
export function getList(params) {
    return instance({
        url: `${base.url}/qhMonitorFile/getList`, method: "get", params
    });
}


/**
 * 绑定
 * @returns {*}
 */
export function bindData(data) {
    return instance({
        url: `${base.url}/monitorAuditUser/bindData`, method: "post", data
    });
}


/**
 * 绑定文件
 * @returns {*}
 */
export function getBindList(params) {
    return instance({
        url: `${base.url}/monitorAuditUser/getBindList`, method: "get", params
    });
}
