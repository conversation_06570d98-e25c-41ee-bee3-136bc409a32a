// 报警信息查询及处置 相关接口

import instance from "./request"
import base from "./base"

// 报警数据处置列表
export function getAlarmInfoPage(data) {
    return instance({
        url: `${base.url}/W41/alarmInfoPage`,
        method: "post",
        data,
    })
}

// 报警数据处置列表
export function overMonitorList(params) {
    return instance({
        url: `${base.url}/W41/overMonitorList`,
        method: "get",
        params,
    })
}

// 报警处置
export function handle(data) {
    return instance({
        url: `${base.url}/W41/handle`,
        method: "post",
        data,
    })
}


// 报警超标信息查看-废水、废气超标数据(分页)
export function overMonitorPage(params) {
    return instance({
        url: `${base.url}/W41/overMonitorPage`,
        method: "get",
        params,
    })
}

// 报警超标信息查看-处置
export function handleNew(data) {
    return instance({
        url: `${base.url}/W41/handleNew`,
        method: "post",
        data,
    })
}

// 报警超标信息查看-废水、废气超标统计分析
export function overMonitorStatistics(params) {
    return instance({
        url: `${base.url}/W41/overMonitorStatistics`,
        method: "get",
        params,
    })
}

// 报警超标信息查看-废水、废气超标统计分析
export function overMonitorColumnStatistics(params) {
    return instance({
        url: `${base.url}/W41/overMonitorColumnStatistics`,
        method: "get",
        params,
    })
}

