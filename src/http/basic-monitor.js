import instance from "./request";
import base from "./base";

// 基础信息管理-监测点信息(分页)
export function monitorPage(params) {
    return instance({
        url: `${base.url}/W14/monitorPage`,
        method: "get",
        params
    });
}

// 基础信息管理-监测项目信息(分页)
export function paramPage(params) {
    return instance({
        url: `${base.url}/W11/paramPage`,
        method: "get",
        params
    });
}

// 基础信息管理-监测项目信息(分页)
export function equipmentPage(params) {
    return instance({
        url: `${base.url}/W12/equipmentPage`,
        method: "get",
        params
    });
}

// 基础信息管理-数采仪信息(分页)
export function collectPage(params) {
    return instance({
        url: `${base.url}/W13/collectPage`,
        method: "get",
        params
    });
}

// 基础信息管理-运维单位
export function getAmdAllList(params) {
    return instance({
        url: `${base.url}/W15/getAmdAllList`,
        method: "get",
        params
    });
}
