// 推送记录 相关接口
import instance from "./request"
import base from "./base"

/**
 * 查询配置信息
 * @returns {*}
 */
export function pushConfigInfo(params) {
    return instance({
        url: `${base.url}/pushConfig/pushConfigInfo`, method: "get", params
    });
}

/**
 * 新增 or 修改配置信息
 * @returns {*}
 */
export function addOrUpdateConfig(data) {
    return instance({
        url: `${base.url}/pushConfig/addOrUpdateConfig`, method: "post", data
    });
}

/**
 * 查询推送人员
 * @returns {*}
 */
export function pushUserPage(params) {
    return instance({
        url: `${base.url}/pushUser/pushUserPage`, method: "get", params
    });
}

/**
 * 新增推送人员
 * @returns {*}
 */
export function addOrUpdatePushUser(data) {
    return instance({
        url: `${base.url}/pushUser/addOrUpdatePushUser`, method: "post", data
    });
}

/**
 * 启动or停用
 * @returns {*}
 */
export function updateStartType(params) {
    return instance({
        url: `${base.url}/pushUser/updateStartType`, method: "get", params
    });
}

/**
 * 删除
 * @param userIds
 * @returns {*}
 */
export function deleteUser(userIds) {
    return instance({
        url: `${base.url}/pushUser/delete/` + userIds, method: 'delete'
    })
}


/**
 * 启动or停用
 * @returns {*}
 */
export function pushResultPage(params) {
    return instance({
        url: `${base.url}/pushResult/pushResultPage`, method: "get", params
    });
}



