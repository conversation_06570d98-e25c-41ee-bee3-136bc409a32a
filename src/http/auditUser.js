// 审批权限 相关接口
import instance from "./request"
import base from "./base"

/**
 * 获取审批权限用户
 * @returns {*}
 */
export function auditUserList(params) {
    return instance({
        url: `${base.url}/user/environmentalControlAuditUserList`, method: "get", params
    });
}


/**
 * 获取列表审批人分页
 * @returns {*}
 */
export function getAuditUserPage(params) {
    return instance({
        url: `${base.url}/monitorAuditUser/getAuditUserPage`, method: "get", params
    });
}

/**
 * 新增 or 修改
 * @returns {*}
 */
export function addOrUpdateAuditUser(data) {
    return instance({
        url: `${base.url}/monitorAuditUser/addOrUpdateAuditUser`, method: "post", data
    });
}

// 获取审批权限
export function getAuditPrivilege(params) {
    return instance({
        url: `${base.url}/monitorAuditUser/getAuditPrivilege`,
        method: "get",
        params
    });
}





