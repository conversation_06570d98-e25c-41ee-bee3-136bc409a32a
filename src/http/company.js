// 无异味企业 相关接口
import instance from "./request"
import base from "./base"

/**
 * 无异味企业
 * @returns {*}
 */
export function companyPage(params) {
    return instance({
        url: `${base.url}/mPeculiarSmellCompany/companyPage`, method: "get", params
    });
}

/**
 * 新增修改无异味企业
 * @returns {*}
 */
export function addOrUpdatePeculiarSmellCompany(data) {
    return instance({
        url: `${base.url}/mPeculiarSmellCompany/addOrUpdatePeculiarSmellCompany`, method: "post", data
    });
}

/**
 * 启动or停用
 * @returns {*}
 */
export function updateStartType(params) {
    return instance({
        url: `${base.url}/mPeculiarSmellCompany/updateStartType`, method: "get", params
    });
}

/**
 * 删除
 * @returns {*}
 */
export function deleteCompany(ids) {
    return instance({
        url: `${base.url}/mPeculiarSmellCompany/delete/` + ids,
        method: "delete"
    });
}