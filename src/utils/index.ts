import dayjs from 'dayjs';
import type { VxeColumnPropTypes } from 'vxe-table';
// 时间格式化
export const formatDate: VxeColumnPropTypes.Formatter = ({ cellValue }) =>
  cellValue ? dayjs(new Date(cellValue)).format('YYYY-MM-DD') : '-';
// 下拉选项筛选方法
export const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 时分秒类型日期格式化
export const formatDateTime: VxeColumnPropTypes.Formatter = ({ cellValue }) =>
  cellValue ? dayjs(new Date(cellValue)).format('YYYY-MM-DD HH:mm:ss') : '-';
// 从url中获取指定参数
export const getUrlParam = (key: string) => {
  // 获取查询参数
  const search = location.search.slice(1);
  // 获取指定参数开始的字符串下标
  const keyIndex = search.indexOf(key + '=');
  // 如果没找到，返回空字符串
  if (keyIndex === -1) {
    return '';
  }
  // 截取指定参数之后的字符串
  let result = search.slice(key.length + keyIndex + 1);
  // 获取下一个参数的起始下标
  const nextPar = result.indexOf('&');
  if (nextPar !== -1) {
    // 如果有下一个参数，从=号之后到下一个参数开始之前的字符串就是我们要找的
    result = result.slice(0, nextPar);
  }
  return result;
};

export const isExternal = (path: string) =>
  /^(https?:|mailto:|tel:)/.test(path);

export const isObject = (val: unknown) =>
  val !== null && typeof val === 'object';

export const isFunction = (val: unknown) => typeof val === 'function';

export const isPromise = <T = any>(val: any): val is Promise<T> => {
  return isObject(val) && isFunction(val.then) && isFunction(val.catch);
};
