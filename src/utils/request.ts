import qs from 'qs';
import axios, { AxiosRequestConfig } from 'axios';
import { message } from 'ant-design-vue';

const service = axios.create({
  baseURL: '/api',
  timeout: 10000,
  withCredentials: true,
});

// 请求拦截器
service.interceptors.request.use(config => {
  config.paramsSerializer = params =>
    qs.stringify(params, { arrayFormat: 'repeat' });
  return config;
});

// 响应拦截器
service.interceptors.response.use(
  response => {
    if (response.status === 200) {
      const { msg, code } = response.data;
      if (code === '401') {
        message.error(`用户未登录！`);
      } else if (code === '-1') {
        message.error(msg);
      }
      return response.data;
    } else {
      return Promise.reject(new Error('出错了'));
    }
  },
  error => {
    const { response } = error;
    if (response) {
      // const msg = '';
    } else {
      if (!window.navigator.onLine) {
        message.error('网络出问题了，请检查一下网络！');
        return Promise.reject(error);
        // 断网
      } else {
        if (error.message.includes('timeout')) {
          message.error('请求超时，请稍后再试');
          return Promise.reject(error);
        }
      }
      return Promise.reject(error);
    }
  }
);

const request = async <T>(config: AxiosRequestConfig): Promise<T> => {
  const response = await service.request<T>(config);
  return response.data;
};
export default request;
