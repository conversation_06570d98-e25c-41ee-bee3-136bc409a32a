export interface ProTableProps {
  columns: any[];
}
export enum ValueTypeEnum {
  Select,
  Input,
  TextArea,
  DatePicker,
  TimePicker,
  RangePicker,
}
export type ValueType = keyof typeof ValueTypeEnum;

export interface ValueEnum {
  label: string;
  value: string | number;
}

export interface Column {
  valueType?: ValueType;
  valueEnum?: Record<string | number, ValueEnum>;
  showSearch?: boolean;
  title?: string;
  field?: string;
  type?: string;
  slots?: Record<string, string>;
  componentProps?: Record<string, any>;
  showInTable?: boolean;
  isRequired?: boolean;
  width?: string | number | { span: number };
  dataIndex?: string;
  fixed?: 'left' | 'right' | boolean;
}
