import { defineStore } from 'pinia';
import { getUserInfoBytoken } from '@/apis/user';

const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: {
      deptBm: '',
      deptId: '',
      deptName: '',
      loginId: '',
      loginName: '',
      userId: '',
    },
  }),
  actions: {
    async setUserData(token: string) {
      const result = await getUserInfoBytoken(token);
      this.userInfo = result;
    },
  },
});

export default useUserStore;
