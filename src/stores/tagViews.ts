import { RouteLocationNormalizedLoaded } from 'vue-router';

const useTagViewStore = defineStore('tagView', {
  state: () => ({
    visitedViews: [] as TagView.Tag[],
    cachedViews: [] as string[],
  }),
  actions: {
    addVisitedView(view: TagView.Tag) {
      if (this.visitedViews.some(v => v.path === view.path)) return;
      this.visitedViews.push(
        Object.assign({}, view, {
          title: view.meta.title! || 'no-name',
        })
      );
    },
    addCachedView(view: TagView.Tag) {
      if (this.cachedViews.includes(view.meta.title!)) return;
      if (!view.meta?.noCache) {
        this.cachedViews.push(view.meta.title!);
      }
    },
    delVisitedView(view: TagView.Tag) {
      for (const [i, v] of this.visitedViews.entries()) {
        if (v.path === view.path) {
          this.visitedViews.splice(i, 1);
          break;
        }
      }
    },
    delCachedView(view: TagView.Tag) {
      const index = this.cachedViews.indexOf(view.meta.title!);
      index > -1 && this.cachedViews.splice(index, 1);
    },
    delOtherVisitedViews(view: TagView.Tag) {
      this.visitedViews = this.visitedViews.filter(v => {
        return v.meta?.affix || v.path === view.path;
      });
    },
    delOtherCachedViews(view: TagView.Tag) {
      const index = this.cachedViews.indexOf(view.meta.title!);
      if (index > -1) {
        this.cachedViews = this.cachedViews.slice(index, index + 1);
      } else {
        this.cachedViews = [];
      }
    },
    delAllVisitedViews() {
      const affixTags = this.visitedViews.filter(tag => tag.meta?.affix);
      this.visitedViews = affixTags;
    },
    delAllCachedViews() {
      this.cachedViews = [];
    },

    updateVisitedViews(view: RouteLocationNormalizedLoaded) {
      for (let v of this.visitedViews) {
        if (v.path === view.path) {
          v = Object.assign(v, view);
          break;
        }
      }
    },
  },
});

export default useTagViewStore;
