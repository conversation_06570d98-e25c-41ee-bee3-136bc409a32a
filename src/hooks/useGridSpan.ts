import { useDebounceFn } from '@vueuse/core';

export default () => {
  const span = ref(4);
  const visibleFormItemNumber = ref(2);
  const setSpanValue = () => {
    const { clientWidth } = document.documentElement;
    if (clientWidth < 1400) {
      span.value = 6;
      visibleFormItemNumber.value = 1;
    } else if (clientWidth > 1600) {
      span.value = 4;
      visibleFormItemNumber.value = 2;
    } else if (clientWidth > 2000) {
      span.value = 4;
      visibleFormItemNumber.value = 3;
    }
  };
  setSpanValue();
  window.addEventListener('resize', useDebounceFn(setSpanValue, 300));
  return {
    span,
    visibleFormItemNumber,
  };
};
