import { createRouter, createWebHistory } from 'vue-router';
import type {
  RouteRecordRaw,
  RouteLocationNormalized,
  NavigationGuardNext,
} from 'vue-router';
import NProgress from 'nprogress';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/home/<USER>',
  },
  {
    path: '/home',
    component: () => import('@/layouts/index.vue'),
    name: 'home',
    meta: {
      showInMenu: true,
      title: 'home',
      icon: 'ant-design:dashboard-outlined',
    },
    children: [
      {
        path: 'queryPage',
        name: 'queryPage',
        component: () => import('@/views/QueryPage.vue'),
        meta: {
          title: '基础查询表格页',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'test',
        name: 'test',
        component: () => import('@/views/test.vue'),
        meta: {
          title: 'test',
          icon: 'ant-design:tool-outlined',
        },
      },
    ],
  },
  // 《风险作业》
  {
    path: '/riskOpera',
    name: 'riskOpera',
    component: () => import('@/layouts/index.vue'),
    redirect: '/riskOpera/environmentalOverview',
    meta: {
      showInMenu: true,
      title: '风险作业',
      icon: 'ant-design:tool-outlined',
    },
    children: [
      {
        path: 'environmentalOverview', // 环保监控一张图
        component: () => import('@/views/environmental-overview/index.vue'),
        meta: {
          title: '环保监控一张图',
          icon: 'ant-design:tool-outlined',
        },
      },
      // {
      //   path: 'environmentalReport', // 环保检测报告
      //   component: () => import('@/views/environmental-report/index.vue'),
      //   meta: {
      //     title: '环保检测报告',
      //     icon: 'ant-design:tool-outlined',
      //   },
      // },
      // {
      //   path: 'environmentalReport/details', // 环保检测报告
      //   component: () => import('@/views/environmental-report/details.vue'),
      //   meta: {
      //     title: '环保检测报告详情',
      //     icon: 'ant-design:tool-outlined',
      //   },
      // },
      {
        path: 'monitoringDataQuery', // 监测数据查询
        component: () => import('@/views/monitoring-data-query/index.vue'),
        meta: {
          title: '监测数据查询',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'alarmQuery', // 报警信息查询及处置
        component: () => import('@/views/alarm-query/index.vue'),
        meta: {
          title: '报警信息查询及处置',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'basicInformationManagement', // 基础信息管理
        component: () =>
          import('@/views/basic-information-management/index.vue'),
        meta: {
          title: '基础信息管理',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'operationManagement', // 设备运维管理
        component: () => import('@/views/operation-management/index.vue'),
        meta: {
          title: '设备运维管理',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'backlogResult', // 推送记录
        component: () => import('@/views/backlog-result/index.vue'),
        meta: {
          title: '推送记录',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'backlogSetting', // 推送配置
        component: () => import('@/views/backlog-setting/index.vue'),
        meta: {
          title: '推送配置',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'company', // 异常企业
        component: () => import('@/views/company/index.vue'),
        meta: {
          title: '异常企业',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'statistics', // 统计
        component: () => import('@/views/statistics/index.vue'),
        meta: {
          title: '统计',
          icon: 'ant-design:tool-outlined',
        },
      },
    ],
  },
  // 《统计分析》--hse环保监督问题
  {
    path: '/hb',
    name: 'hb',
    component: () => import('@/layouts/index.vue'),
    meta: {
      showInMenu: true,
      title: '环保监督问题',
      icon: 'ant-design:tool-outlined',
    },
    children: [
      {
        path: 'monitor',
        name: 'monitor',
        component: () => import('../views/monitor/index.vue'),
        meta: {
          title: '自行监测管理',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'basicInformationMonitor',
        name: 'basicInformationMonitor',
        component: () => import('../views/basic-information-monitor/index.vue'),
        meta: {
          title: '基础信息管理',
          icon: 'ant-design:tool-outlined',
        },
      },
      {
        path: 'monitorFile',
        name: 'monitorFile',
        component: () => import('../views/monitor-file/index.vue'),
        meta: {
          title: '监测报告管理',
          icon: 'ant-design:tool-outlined',
        },
      },
    ],
  },
  {
    path: '/401',
    component: () => import('@/views/401.vue'),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach(
  async (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) => {
    const token = localStorage.getItem('token');
    if (!NProgress.isStarted()) {
      NProgress.start();
    }
    // 如果是访问401页面，直接通过
    if (to.path.includes('401')) {
      next();
      return;
    }

    // 根据token存在与否进行跳转
    if (token) {
      // 如果已经是首页，则直接通过，避免无限重定向
      if (to.path === '/') {
        next('/');
      } else {
        next();
      }
    } else {
      if (to.path === '/') {
        next('/home/<USER>');
      } else {
        next();
      }
    }
  }
);

router.afterEach(
  (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    NProgress.done();
  }
);

export default router;
