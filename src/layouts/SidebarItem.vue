<template>
  <a-menu-item v-if="!item.children" :key="resolvePath(item.path)">
    <template v-if="item.meta?.icon" #icon>
      <iconify-icon :name="item.meta?.icon" />
    </template>
    <Link :to="resolvePath(item.path)">
      {{ item.meta?.title }}
    </Link>
  </a-menu-item>
  <a-sub-menu v-else :key="item.path" :title="item.meta?.title">
    <template #icon>
      <iconify-icon :name="item.meta?.icon" />
    </template>
    <sidebar-item
      v-for="child in item.children"
      :key="child.path"
      :item="child"
      :base-path="resolvePath(child.path)"
    />
  </a-sub-menu>
</template>
<script lang="ts" setup>
import type { RouteRecordRaw } from 'vue-router';
import { isExternal } from '@/utils';
import Link from './Link.vue';
/**
 * @description 递归生成菜单
 * @props item: 当前路由信息 basePath: 递归前的路由path
 */
// 定义props
const props = defineProps<{
  item: RouteRecordRaw;
  basePath: string;
}>();
// 解构出basePath
const { basePath } = toRefs(props);
// 根据basePath 生成每个item真实的全路由
const resolvePath = (routePath: string) => {
  // 如果是外部链接，直接返回
  if (isExternal(routePath)) return routePath;
  if (isExternal(basePath.value)) return basePath.value;
  // 如果basePath跟现在path一致，随便返回一个
  if (basePath.value === routePath) return routePath;
  // 如果basePath中已经包含了routePath,返回basePath
  if (basePath.value.includes(routePath)) return basePath.value;
  const result = `${basePath.value}${
    routePath.startsWith('/') ? '' : '/'
  }${routePath}`;
  return result;
};
</script>
