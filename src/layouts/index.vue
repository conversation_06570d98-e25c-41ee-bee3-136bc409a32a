<template>
  <a-layout>
    <a-layout-header v-if="!qiankun">
      <div class="header-wrapper">
        <div class="header-left">
          <router-link to="/">
            <img
              class="header-logo"
              height="48"
              src="@/assets/images/logo.png"
            />
          </router-link>
        </div>
        <a-dropdown>
          <span class="ant-dropdown-link" @click.prevent>
            您好，{{ userInfo.loginName }}
          </span>
        </a-dropdown>
      </div>
    </a-layout-header>
    <a-layout>
      <a-layout-sider
        v-if="!qiankun"
        v-model:collapsed="collapsed"
        collapsible
        theme="light"
      >
        <a-menu
          v-model:selected-keys="selectedKeys"
          theme="light"
          mode="inline"
        >
          <sidebar-item
            v-for="item in menus"
            :key="item.path"
            :item="item"
            :base-path="item.path"
          />
        </a-menu>
      </a-layout-sider>
      <a-layout :class="['content', { 'qiankun-content': qiankun }]">
        <!-- <tags-view /> -->
        <div v-if="!qiankun" class="breadcrumb-nav">
          <!-- <div
            class="ant-layout-sider__trigger"
            @click="collapsed = !collapsed"
          >
            <MenuFoldOutlined v-if="collapsed" title="展开" />
            <MenuUnfoldOutlined v-else title="收起" />
          </div> -->
          <a-breadcrumb>
            <a-breadcrumb-item v-for="item in matched" :key="item.path">
              {{ item.name }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <a-layout-content>
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script lang="ts" setup>
import useUserStore from '@/stores/user';
import SidebarItem from './SidebarItem.vue';

const collapsed = ref(false);
const { userInfo } = useUserStore();
const router = useRouter();
const { routes } = router.options;
const matched = computed(() => router.currentRoute.value.matched || []);
const path =
  location.pathname === '/'
    ? 'home'
    : location.pathname.slice(location.pathname.slice(1).indexOf('/') + 2);
const selectedKeys = ref<string[]>([path]);
// 修改菜单生成逻辑，包含所有标记为showInMenu的路由
const menus = routes.filter(route => route.meta?.showInMenu);
const qiankun = computed(() => {
  if (localStorage.getItem('qiankun')) {
    return true;
  }
  return false;
});
</script>
<style lang="less">
.ant-layout-header {
  padding: 0 20px;
}
.header-wrapper {
  display: flex;
  justify-content: space-between;
  height: 100%;
  color: #fff;
  .header-left {
    display: flex;
    align-items: center;
    &-title {
      color: inherit;
      margin: 0;
      padding: 0 10px;
    }
  }
}
.breadcrumb-nav {
  display: flex;
  align-items: center;
}
.ant-layout-sider__trigger {
  width: 20px;
  height: 20px;
  line-height: 20px;
  cursor: pointer;
}
.content {
  height: calc(100vh - 80px);
  padding: 0 16px 16px;
  background-color: #eff0f4;
  overflow-y: auto;
  & > .ant-breadcrumb {
    padding: 5px 0;
  }
  &.qiankun-content {
    height: calc(100vh - 16px);
    padding: 16px;
    overflow: hidden;
  }
}
</style>
