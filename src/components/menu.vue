<template>
  <div class="tree-wrapper">
    <a-row :gutter="16">
      <a-col :span="12" style="text-align: center">
        <img
          :src="
            bo1
              ? '/src/assets/slices/image1.png'
              : '/src/assets/slices/image1-1.png'
          "
          alt=""
          class="imageClass"
          @click="jumpHandle('/riskOpera/environmentalOverview')"
        />
        <br />
        污染源分布图
      </a-col>
      <a-col :span="12" style="text-align: center">
        <img
          src="../assets/slices/image2-1.png"
          alt=""
          class="imageClass"
          @click="jumpUrlHandle('http://139.224.246.62/permission/login')"
        />
        <br />
        甲烷泄露检测与修复
      </a-col>
    </a-row>

    <a-row :gutter="16" style="margin-top: 30px">
      <a-col :span="12" style="text-align: center">
        <img
          :src="
            bo3
              ? '/src/assets/slices/image3.png'
              : '/src/assets/slices/image3-1.png'
          "
          alt=""
          class="imageClass"
          @click="jumpHandle('/riskOpera/company')"
        />
        <br />
        无异味企业
      </a-col>
      <a-col :span="12" style="text-align: center">
        <!-- 预留位置 -->
      </a-col>
    </a-row>

    <a-divider
      orientation="left"
      style="color: #fff; border-color: rgba(34, 75, 183, 0.7)"
    >
      <span style="color: #fff; font-weight: bold">在线监测</span>
    </a-divider>

    <a-row :gutter="[8, 8]">
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo5 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/riskOpera/monitoringDataQuery')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon1-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              监测数据查询
            </a-col>
          </a-row>
        </div>
      </a-col>
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo6 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/riskOpera/alarmQuery')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon2-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              报警信息查询及处置
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>

    <a-row :gutter="[8, 8]">
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo7 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/riskOpera/operationManagement')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon3-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              设备运营管理
            </a-col>
          </a-row>
        </div>
      </a-col>
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo8 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/riskOpera/basicInformationManagement')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon4-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              基础信息管理
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>

    <a-divider
      orientation="left"
      style="color: #fff; border-color: rgba(34, 75, 183, 0.7)"
    >
      <span style="color: #fff; font-weight: bold">自行监测</span>
    </a-divider>

    <a-row :gutter="[8, 8]">
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo9 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/hb/monitor')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon5-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              自行监测管理
            </a-col>
          </a-row>
        </div>
      </a-col>
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo10 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/hb/monitorFile')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon6-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              监测报告管理
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>

    <a-row :gutter="[8, 8]">
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo11 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/hb/basicInformationMonitor')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon4-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              基础信息管理
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>

    <a-divider
      orientation="left"
      style="color: #fff; border-color: rgba(34, 75, 183, 0.7)"
    >
      <span style="color: #fff; font-weight: bold">即时通送推送</span>
    </a-divider>

    <a-row :gutter="[8, 8]">
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo12 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/riskOpera/backlogResult')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon7-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              推送数据
            </a-col>
          </a-row>
        </div>
      </a-col>
      <a-col :span="12" style="text-align: center">
        <div
          :class="bo13 ? 'level1' : 'level1-1'"
          @click="jumpHandle('/riskOpera/backlogSetting')"
        >
          <a-row style="width: 100%" align="middle">
            <a-col :span="6">
              <img
                src="../assets/slices/icon8-1.png"
                alt=""
                class="imageSmallClass"
              />
            </a-col>
            <a-col :span="18" style="line-height: 1.5; text-align: left">
              推送配置
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 响应式状态
const bo1 = ref(false);
const bo2 = ref(false);
const bo3 = ref(false);
const bo4 = ref(false);
const bo5 = ref(false);
const bo6 = ref(false);
const bo7 = ref(false);
const bo8 = ref(false);
const bo9 = ref(false);
const bo10 = ref(false);
const bo11 = ref(false);
const bo12 = ref(false);
const bo13 = ref(false);

// 初始化高亮状态
const initHighlight = () => {
  const currentUrl = window.location.href;
  console.log('currentUrl', currentUrl);

  // 重置所有状态
  const bos = [
    bo1,
    bo2,
    bo3,
    bo4,
    bo5,
    bo6,
    bo7,
    bo8,
    bo9,
    bo10,
    bo11,
    bo12,
    bo13,
  ];
  bos.forEach(bo => (bo.value = false));

  // 根据当前URL设置高亮状态
  if (
    currentUrl.includes('overview') ||
    currentUrl.includes('environmentalOverview')
  ) {
    bo1.value = true;
  } else if (currentUrl.includes('company')) {
    bo3.value = true;
  } else if (currentUrl.includes('monitoringDataQuery')) {
    bo5.value = true;
  } else if (currentUrl.includes('alarmQuery')) {
    bo6.value = true;
  } else if (currentUrl.includes('operationManagement')) {
    bo7.value = true;
  } else if (currentUrl.includes('basicInformationManagement')) {
    bo8.value = true;
  } else if (currentUrl.includes('monitorFile')) {
    bo10.value = true;
  } else if (
    currentUrl.includes('riskOpera/monitor') ||
    currentUrl.includes('hb/monitor')
  ) {
    bo9.value = true;
  } else if (currentUrl.includes('/hb/basicInformationMonitor')) {
    bo11.value = true;
  } else if (currentUrl.includes('backlogResult')) {
    bo12.value = true;
  } else if (currentUrl.includes('backlogSetting')) {
    bo13.value = true;
  }
};

// 跳转方法
const jumpHandle = (url: string) => {
  router.push({
    path: url,
  });
};

// 外部链接跳转
const jumpUrlHandle = (url: string) => {
  window.open(url, '_blank');
};

onMounted(() => {
  initHighlight();
});
</script>

<style scoped>
.tree-wrapper {
  height: calc(100% - 52px);
  overflow: auto;
  margin-top: 20px;
  padding: 16px;
}

.imageSmallClass {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
}

.imageClass {
  width: calc(50% - 2px);
  height: calc(50% - 2px);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.imageClass:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.level1 {
  background-size: 150px 52px;
  background-image: url('~@/assets/slices/juxing1.png');
  background-repeat: no-repeat;
  width: 154px;
  height: 52px;
  background-position: center;
  text-align: center;
  font-size: 12px;
  line-height: 1.2;
  overflow: hidden;
  white-space: normal;
  word-wrap: break-word;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px auto;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #fff;
}

.level1-1 {
  background-size: 150px 52px;
  background-image: url('~@/assets/slices/juxing1-1.png');
  background-repeat: no-repeat;
  width: 154px;
  height: 52px;
  background-position: center;
  text-align: center;
  font-size: 12px;
  line-height: 1.2;
  overflow: hidden;
  white-space: normal;
  word-wrap: break-word;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px auto;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #fff;
}

.level1:hover,
.level1-1:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Ant Design 组件样式覆盖 */
:deep(.ant-divider) {
  border-color: rgba(34, 75, 183, 0.7);
  margin: 16px 0;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left::before) {
  border-top-color: rgba(34, 75, 183, 0.7);
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left::after) {
  border-top-color: rgba(34, 75, 183, 0.7);
}

:deep(.ant-row) {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .level1,
  .level1-1 {
    width: 120px;
    height: 40px;
    font-size: 10px;
  }

  .imageClass {
    width: 60px;
    height: 60px;
  }

  .imageSmallClass {
    width: 16px;
    height: 16px;
  }
}
</style>
