<template>
  <div v-if="dialogVisible">
    <a-modal v-model:open="dialogVisible" :title="title" width="1000px">
      <div>
        <slot></slot>
      </div>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '详情',
});

const dialogVisible = ref(false);

const openDialog = () => {
  dialogVisible.value = true;
};

// 暴露方法给父组件使用
defineExpose({
  openDialog,
});
</script>
<style lang="less" scoped>
:deep(.ant-modal-header) {
  background: #073aa9;
}
:deep(.ant-modal-body) {
  background: #073aa9;
  color: #ffffff;
}
:deep(.ant-modal-title) {
  color: #ffffff;
}
:deep(.ant-modal-close) {
  color: #ffffff;
}
:deep(.ant-form-inline .ant-form-item) {
  width: calc(50% - 20px);
}
:deep(.ant-form-item-control) {
  width: calc(100% - 140px);
}
</style>
