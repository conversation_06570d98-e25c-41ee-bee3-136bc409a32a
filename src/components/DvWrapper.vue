<template>
  <dv-border-box-11 :title="title" :color="['#4fd2dd', '#235fa7']">
    <div class="dv-content">
      <slot></slot>
    </div>
  </dv-border-box-11>
</template>
<script setup lang="ts">
interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});
</script>
<style lang="less">
.dv-content {
  padding: 62px 24px 20px;
  height: 100%;
  width: 100%;
}
</style>
