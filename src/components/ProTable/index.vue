<template>
  <a-card>
    <div class="flex justify-between">
      <a-form ref="formRef" :model="model" class="search-form flex-1">
        <a-row :gutter="24">
          <a-col
            v-for="(item, index) in formItems"
            :key="item.field || index"
            :span="span"
          >
            <a-form-item :label="item.title" :name="item.field">
              <component
                :is="componentMap[item.valueType || 'Input']"
                v-model:value="model[item.field!]"
                :options="getItemOptions(item)"
                :placeholder="getPlaceholder(item)"
                v-bind="item.componentProps"
              />
            </a-form-item>
          </a-col>
          <a-col :span="span">
            <a-form-item no-style>
              <a-space align="center">
                <a-button @click="handleReset">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
                <a-button type="primary" @click="handleSearch">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  查询
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <slot name="extra"></slot>
    </div>
    <a-spin :spinning="loading">
      <vxe-grid
        :columns="(tableColumns as any)"
        :data="data"
        :height="tableHeight"
        stripe
      >
        <template v-for="(_, name) in $slots" #[name]="slotProps">
          <slot :name="name" v-bind="slotProps"></slot>
        </template>
        <template #pager>
          <vxe-pager
            v-model:current-page="model.current"
            v-model:page-size="model.size"
            size="mini"
            :layouts="[
              'Total',
              'PrevJump',
              'PrevPage',
              'Number',
              'NextPage',
              'NextJump',
              'FullJump',
              'Sizes',
            ]"
            :total="data?.total || 0"
            @page-change="handlePageChange"
          >
          </vxe-pager>
        </template>
      </vxe-grid>
    </a-spin>
  </a-card>
</template>
<script lang="ts" setup>
import type { VxePagerEvents } from 'vxe-table';
import type { Column, ValueType } from '@/types/ProTable';
import {
  Input,
  Select,
  DatePicker,
  TimePicker,
  RangePicker,
  FormInstance,
  message,
} from 'ant-design-vue';
import {
  ref,
  computed,
  reactive,
  watch,
  nextTick,
  toRefs,
  onMounted,
} from 'vue';
import useGridSpan from '@/hooks/useGridSpan';
import {
  ReloadOutlined,
  SearchOutlined,
  //   DownOutlined,
  //   UpOutlined,
} from '@ant-design/icons-vue';
import useTableHeight from './useTableHeight';
import { isPromise } from '@/utils';

const props = defineProps<{
  columns: Column[];
  request: (...args: any) => Promise<any>;
  paramsSerializer?: (...args: any) => any;
}>();

const emits = defineEmits(['collapsed', 'search', 'reset']);

const { columns } = toRefs(props);

const total = ref(0);

const data = ref<any>();

const loading = ref(false);

const model = reactive<Record<string, any>>({
  current: 1,
  size: 10,
});

onMounted(() => {
  handleSearch();
});

const formRef = ref<FormInstance>();

const { span } = useGridSpan();
const { tableHeight } = useTableHeight();

const collapsed = ref(true);

const formItems = computed(() =>
  columns.value.filter(k => {
    if (k.type) return false;
    if (!k.field) return false;
    return typeof k.showSearch === 'undefined' ? true : k.showSearch;
  })
);

const tableColumns = computed(() =>
  columns.value.filter(r =>
    typeof r.showInTable === 'undefined' ? true : r.showInTable
  )
);

const getItemOptions = (item: Column) =>
  item.valueEnum ? Object.values(item.valueEnum) : [];

const componentMap: Record<ValueType, InstanceType<any>> = {
  Input,
  Select,
  TextArea: Input.TextArea,
  DatePicker,
  TimePicker,
  RangePicker,
};

const getPlaceholder = (item: Column) => {
  if (item.componentProps?.placeholder) return item.componentProps.placeholder;
  if (item.valueType === 'RangePicker') {
    return undefined;
  }
  return item.valueType === 'Select' ? '请选择' : '请输入';
};

watch(collapsed, () => {
  nextTick(() => {
    emits('collapsed');
  });
});

const handleSearch = async () => {
  try {
    if (!isPromise(props.request())) return;
    loading.value = true;
    const result = props.paramsSerializer
      ? props.paramsSerializer(model)
      : model;
    const response = await props.request(result);
    total.value = response.total;
    data.value = response.records;
    emits('search', model);
    loading.value = false;
  } catch (e: any) {
    loading.value = false;
    message.error(e.message);
  }
};

const handlePageChange: VxePagerEvents.PageChange = ({
  currentPage,
  pageSize,
}) => {
  model.current = currentPage;
  model.size = pageSize;
  handleSearch();
};

const handleReset = () => {
  formRef.value?.resetFields();
  emits('reset');
};

// const handleExpand = () => {
//   collapsed.value = !collapsed.value;
// };
</script>
<style lang="less">
.search-form {
  .ant-form-item {
    margin-bottom: 0;
  }
}
</style>
