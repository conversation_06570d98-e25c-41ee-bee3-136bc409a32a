export default () => {
  const clientHeight = ref<number>(document.documentElement.clientHeight || 0); // 获取浏览器可视区域高度
  const barHeight = ref<number>(0); // 获取bar可视区域高度

  const tableHeight = computed(
    () => clientHeight.value - barHeight.value - 280
  );
  const setBarHeight = () => {
    const el = document.querySelector('.search-form');
    if (document.documentElement && el) {
      clientHeight.value = document.documentElement.clientHeight;
      barHeight.value = el.clientHeight;
    }
  };

  nextTick(() => {
    const barTop = document.querySelector('.search-form');
    if (barTop) {
      barHeight.value = barTop.clientHeight;
    }
  });

  onBeforeMount(() => {
    window.addEventListener('resize', setBarHeight);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', setBarHeight);
  });

  return { tableHeight, setBarHeight };
};
