<template>
  <a-button v-bind="$attrs" :class="[`ant-btn-${props.status}`]">
    <slot></slot>
  </a-button>
</template>
<script lang="ts" setup>
import type { ButtonProps } from 'ant-design-vue';
interface MyButtonProps extends Omit<ButtonProps, 'type'> {
  status?: 'success' | 'warning';
}
const props = defineProps<MyButtonProps>();
</script>
<style lang="less">
.ant-btn {
  &-success {
    background-color: #3bc243;
    border-color: #3bc243;
    &:hover,
    &:focus {
      background-color: #3fd148;
    }
  }
  &-warning {
    background-color: #ff9e0f;
    border-color: #ff9e0f;
    color: #fff;
    &:hover,
    &:focus {
      background-color: #fff7eb; // todo
      border-color: #fff7eb; // todo
      color: #fff;
    }
  }
}
.ant-btn-success {
  background-color: #3fd148;
  border-color: #3fd148;
}
.ant-btn-background-ghost.ant-btn-success {
  background-color: #ecfbed;
  border-color: #3bc243;
  color: #3bc243;
}
.ant-btn-background-ghost.ant-btn-warning {
  border-color: #ff9e0f;
  background-color: #fff7eb;
  color: #ff9e0f;
}
</style>
