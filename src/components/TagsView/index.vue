<template>
  <div id="tag-view-container">
    <ul class="tag-view">
      <router-link
        v-for="(tag, index) in visitedViews"
        ref="tag"
        :key="tag.path + index"
        v-slot="{ navigate }"
        custom
        :to="{ path: tag.path }"
      >
        <li
          class="tag-view-item"
          :class="isActive(tag) ? 'active' : ''"
          @click.middle="!isAffix(tag) ? closeSelectedTag(tag) : ''"
          @click="navigate"
          @contextmenu.prevent="openMenu(tag, $event)"
        >
          {{ tag.meta?.title }}
          <span
            v-if="!isAffix(tag)"
            @click.prevent.stop="closeSelectedTag(tag)"
          >
            <CloseOutlined />
          </span>
        </li>
      </router-link>
    </ul>
    <ul
      v-show="visible"
      :style="{ left: position.left + 'px', top: position.top + 'px' }"
      class="tag-view-contextmenu"
    >
      <li @click="refreshSelectedTag(selectedTag!)">刷新</li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag!)">
        关闭
      </li>
      <li @click="closeOthersTags">关闭其他</li>
      <li @click="closeAllTags(selectedTag!)">关闭所有</li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
import { CloseOutlined } from '@ant-design/icons-vue';
import { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';
import useTagViewStore from '@/stores/tagViews';

const {
  visitedViews,
  updateVisitedViews,
  addVisitedView,
  addCachedView,
  delVisitedView,
  delCachedView,
  delAllCachedViews,
  delAllVisitedViews,
  delOtherVisitedViews,
  delOtherCachedViews,
} = useTagViewStore();
const router = useRouter();
const route = useRoute();
const visible = ref(false);
const position = reactive({ top: 0, left: 0 });
const selectedTag = ref<TagView.Tag>();
const affixTags = ref<TagView.Tag[]>([]);
const instance = getCurrentInstance();
const { routes } = router.options;

const isActive = (current: TagView.Tag) => current.path === route.path;

const isAffix = (tag?: TagView.Tag) => tag?.meta?.affix;

const filterAffixTags = (routes: RouteRecordRaw[]) => {
  let tags: TagView.Tag[] = [];
  routes.forEach(route => {
    if (route.meta?.affix) {
      tags.push({
        fullPath: route.path,
        path: route.path,
        name: route.name as string,
        meta: { ...(route.meta as any) },
      });
    }
    if (route.children) {
      const tempTags = filterAffixTags(route.children);
      if (tempTags.length >= 1) {
        tags = [...tags, ...tempTags];
      }
    }
  });
  return tags;
};

const initTags = () => {
  // @ts-ignore
  const affixed = (affixTags.value = filterAffixTags(routes));
  for (const tag of affixed) {
    if (tag.name) {
      addVisitedView(tag);
    }
  }
};

const addTags = () => {
  const { name } = route;
  const tag = {
    fullPath: route.fullPath,
    path: route.path,
    name: route.name as string,
    meta: { ...(route.meta as any) },
  };
  if (name) {
    addVisitedView(tag);
    addCachedView(tag);
  }
  return false;
};

const moveToCurrentTag = () => {
  const tags = (instance?.proxy?.$refs.tag || []) as {
    to: RouteLocationNormalized;
  }[];
  nextTick(() => {
    for (const tag of tags) {
      if (tag.to.path === route.path) {
        updateVisitedViews(route);
        break;
      }
    }
  });
};

const refreshSelectedTag = (view: TagView.Tag) => {
  delCachedView(view);
  const { fullPath } = view;
  nextTick(() => {
    router.replace({
      path: 'redirect' + fullPath,
    });
  });
};

const closeSelectedTag = (view: TagView.Tag) => {
  if (visitedViews.length === 1 && visitedViews[0].name === view.name) return;
  delVisitedView(view);
  delCachedView(view);
  if (isActive(view)) {
    toLastView(visitedViews, view);
  }
};

const toLastView = (visitedViews: TagView.Tag[], view: TagView.Tag) => {
  const latestView = visitedViews.slice(-1)[0];
  if (latestView) {
    router.push(latestView.fullPath);
  } else {
    router.push('/');
    // if (view.name === 'dashboard') {
    // }
  }
};

const closeOthersTags = () => {
  if (selectedTag.value) {
    router.push(selectedTag.value);
    delOtherVisitedViews(selectedTag.value);
    delOtherCachedViews(selectedTag.value);
    moveToCurrentTag();
  }
};

const closeAllTags = (view: TagView.Tag) => {
  delAllVisitedViews();
  delAllCachedViews();
  if (affixTags.value.some(tag => tag.path === view.path)) {
    return;
  }
  toLastView(visitedViews, view);
};
const openMenu = (tag: TagView.Tag, e: any) => {
  position.left = e.clientX - 10;
  position.top = e.clientY + 15;
  visible.value = true;
  selectedTag.value = tag;
};
const closeMenu = () => {
  visible.value = false;
};

onMounted(() => {
  initTags();
  addTags();
});

watch(
  () => route.meta?.title,
  value => {
    addTags();
    moveToCurrentTag();
  }
);

watch(
  () => visible.value,
  value => {
    if (value) {
      document.body.addEventListener('click', closeMenu);
    } else {
      document.body.removeEventListener('click', closeMenu);
    }
  }
);
</script>
<style lang="less" scoped>
.tag-view {
  height: 34px;
  width: 100%;
  padding: 0;
  margin: 0;
  line-height: 34px;
  // background: #fff;
  // border-bottom: 1px solid #d8dce5;
  // border-radius: 4px;
  // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  .tag-view-item {
    display: inline-block;
    position: relative;
    cursor: pointer;
    height: 26px;
    line-height: 23px;
    border: 1px solid #d8dce5;
    border-radius: 4px;
    color: #495060;
    background: #fff;
    padding: 0 8px;
    // font-size: 12px;
    margin-left: 5px;
    &.active {
      background-color: #2470ff;
      color: #fff;
      border-color: #2470ff;
      &::before {
        content: '';
        background: #fff;
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: relative;
        margin-right: 2px;
      }
    }
  }
  &-contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    // font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>
