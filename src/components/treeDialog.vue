<template>
  <div>
    <a-modal v-model:open="dialogVisible" title="单位选择" width="800px">
      <div>
        <a-input placeholder="输入关键字进行过滤" v-model:value="filterText" />

        <a-tree
          class="filter-tree"
          :tree-data="filteredOrgTree"
          :field-names="fieldNames"
          checkable
          ref="treeRef"
          @check="onCheck"
        />
      </div>
      <template #footer>
        <a-button @click="cancel">取 消</a-button>
        <a-button type="primary" @click="submit">确 定</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue';
import type { TreeProps, DataNode } from 'ant-design-vue/es/tree';
import { treeselect } from '@/http/apis';

interface TreeData extends DataNode {
  id: string;
  key: string;
  title: string;
  label: string;
  orgName: string;
  children?: TreeData[];
}

interface Emits {
  (e: 'change', keys: string[]): void;
}

const emit = defineEmits<Emits>();

const dialogVisible = ref(false);
const filterText = ref('');
const treeRef = ref();
const checkedKeys = ref<string[]>([]);
const fieldNames: TreeProps['fieldNames'] = {
  children: 'children',
  title: 'label',
  key: 'id',
};
const orgTree = ref<TreeData[]>([]);

// 获取树形数据
treeselect().then((res: any) => {
  if (res.data) {
    // 转换数据格式以符合 Ant Design Vue 的要求
    const transformData = (data: any[]): TreeData[] => {
      return data.map(item => ({
        ...item,
        key: item.id,
        title: item.label,
      }));
    };
    orgTree.value = transformData(res.data);
  }
});

// 过滤后的树数据
const filteredOrgTree = computed(() => {
  if (!filterText.value) return orgTree.value;

  const filterTreeData = (data: TreeData[]): TreeData[] => {
    return data.filter(item => {
      if (item.orgName && item.orgName.indexOf(filterText.value) !== -1) {
        return true;
      }
      if (item.children) {
        const filteredChildren = filterTreeData(item.children);
        if (filteredChildren.length > 0) {
          item.children = filteredChildren;
          return true;
        }
      }
      return false;
    });
  };

  return filterTreeData(orgTree.value);
});

const onCheck = (checkedKeysValue: any) => {
  const keys = Array.isArray(checkedKeysValue)
    ? checkedKeysValue
    : checkedKeysValue.checked;
  checkedKeys.value = keys.map((key: any) => String(key));
};

const open = () => {
  dialogVisible.value = true;
};

const submit = () => {
  emit('change', checkedKeys.value);
  cancel();
};

const cancel = () => {
  dialogVisible.value = false;
};

// 暴露方法给父组件使用
defineExpose({
  open,
});
</script>
<style lang="less" scoped>
:deep(.ant-modal-header) {
  background: #073aa9;
}
:deep(.ant-modal-body) {
  background: #073aa9;
  color: #ffffff;
}
:deep(.ant-modal-title) {
  color: #ffffff;
}
:deep(.ant-modal-close) {
  color: #ffffff;
}
:deep(.ant-form-inline .ant-form-item) {
  width: calc(50% - 20px);
}
:deep(.ant-form-item-control) {
  width: calc(100% - 140px);
}
:deep(.ant-modal-footer) {
  background: #073aa9;
}
:deep(.ant-tree) {
  background: #073aa9;
  height: 400px;
  overflow: auto;
}
:deep(.ant-tree-title) {
  color: #ffffff;
}
:deep(.ant-tree) {
  background: transparent;
  color: white;
}
:deep(.ant-tree-node-selected) {
  background: transparent;
  .ant-tree-title {
    color: rgba(11, 196, 255, 1);
  }
}
:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: transparent !important;
}
:deep(.ant-tree-node-content-wrapper:focus) {
  background: transparent !important;
}
</style>
