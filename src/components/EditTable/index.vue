<template>
  <a-card>
    <template #title>
      <a-row>
        <a-col
          v-for="item in columns"
          :key="item.field"
          :span="getTitleSpan(item)"
        >
          <span v-if="item.isRequired" class="color-red">*</span>
          {{ item.title }}
        </a-col>
      </a-row>
    </template>
    <a-form ref="formRef" :model="model" layout="inline">
      <div class="table-box">
        <a-row
          v-for="(item, index) in model"
          :key="`edit-table-${index}`"
          :gutter="5"
        >
          <a-col
            v-for="(el, num) in columns"
            :key="el.field"
            :span="getComponentSpan(el, num)"
          >
            <a-form-item
              :name="[index, el.field!]"
              :rules="[
                { required: el.isRequired, message: getPlaceholder(el) },
              ]"
            >
              <component
                :is="componentMap[el.valueType || 'Input']"
                v-model:value="model[index][el.field!]"
                :options="getItemOptions(el)"
                :placeholder="getPlaceholder(el)"
                v-bind="item.componentProps"
                class="w-full"
              />
            </a-form-item>
          </a-col>
          <a-col :span="1" class="text-center">
            <span
              class="del-btn iconfont icon-times-circle"
              title="删除"
              @click="handleDelete(index)"
            ></span>
          </a-col>
        </a-row>
        <a-button type="dashed" danger class="w-full" @click="handleAdd">
          添加
        </a-button>
      </div>
    </a-form>
  </a-card>
</template>
<script lang="ts" setup>
import type { Column, ValueType } from '@/types/ProTable';
import {
  Input,
  Select,
  DatePicker,
  TimePicker,
  RangePicker,
  message,
  FormInstance,
} from 'ant-design-vue';

const props = defineProps<{
  columns: Column[];
  request: () => Promise<any>;
}>();

const { columns } = toRefs(props);

const model = ref<Record<string, any>[]>([]);

const getTitleSpan = (item: Column) =>
  typeof item.width === 'object'
    ? item.width.span
    : Math.floor(24 / props.columns.length);

const getComponentSpan = (item: Column, index: number) => {
  const span = getTitleSpan(item);
  if (index === columns.value.length - 1) {
    return span - 1;
  }
  return span;
};

const formRef = ref<FormInstance>();
// 添加
const handleAdd = () => {
  const row = columns.value.reduce((prev, current) => {
    prev[current.field!] = undefined;
    return prev;
  }, {} as any);
  model.value.push(row);
};
// 删除
const handleDelete = (index: number) => {
  model.value.splice(index, 1);
};

// 获取列表

const getItemOptions = (item: Column) =>
  item.valueEnum ? Object.values(item.valueEnum) : [];

const componentMap: Record<ValueType, InstanceType<any>> = {
  Input,
  Select,
  TextArea: Input.TextArea,
  DatePicker,
  TimePicker,
  RangePicker,
};

const getPlaceholder = (item: Column) => {
  if (item.componentProps?.placeholder) return item.componentProps.placeholder;
  if (item.valueType === 'RangePicker') {
    return undefined;
  }
  return item.valueType === 'Select'
    ? `请选择${item.title}`
    : `请输入${item.title}`;
};

onMounted(async () => {
  try {
    const data = await props.request();
    model.value = data;
  } catch (e: any) {
    message.error(e.message);
  }
});

defineExpose({
  model,
  formRef,
});
</script>
<style scoped lang="less">
.table-box {
  padding: 8px 10px;
  width: 100%;
  overflow: hidden;
  & > div {
    padding-bottom: 8px;
  }
  .del-btn {
    display: inline-block;
    margin-top: 3px;
    color: #f35159;
    cursor: pointer;
  }
}
</style>
