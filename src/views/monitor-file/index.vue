<template>
  <div class="environmental-alarm-page">
    <!-- 菜单图标 -->
    <div
      ref="menu"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>
    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog />
    </div>

    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <div class="search">
        <div class="content1">
          <a-form :model="formData" layout="inline" class="demo-form-inline">
            <a-form-item label="文件名称：">
              <a-input
                v-model:value="formData.fileName"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="上传时间:">
              <a-range-picker
                v-model:value="formData.timeArr"
                :placeholder="['开始日期', '结束日期']"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
            <a-form-item class="ant-form-item-bottom">
              <a-space>
                <a-button
                  type="primary"
                  style="margin-left: 100px"
                  @click="search"
                  >查询</a-button
                >
                <a-button type="primary" @click="resetForm">重置</a-button>
                <a-button type="primary" @click="addUserHandle">上传</a-button>
                <a-button type="primary" @click="batchDeleteHandle"
                  >批量删除</a-button
                >
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </div>
      <div class="table-scrollbar">
        <div class="table-wrapper">
          <a-table
            v-if="name === '0'"
            :columns="columns"
            :data-source="tableData1"
            :pagination="false"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: handleSelectionChange,
            }"
            row-key="id"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-if="column.key === 'format'">
                {{ record.fileOriginalName.split('.')[1] }}
              </template>
              <template v-if="column.key === 'bindState'">
                <a-switch
                  v-model:checked="record.bindStateChecked"
                  @change="switchHandle(index, record)"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="downloadHandle(record)"
                  >下载</a-button
                >
                <a-button type="link" @click="deleteHandle(record)"
                  >删除</a-button
                >
              </template>
            </template>
          </a-table>
        </div>
        <a-pagination
          v-model:current="formData.pageNum"
          v-model:page-size="formData.pageSize"
          :total="total"
          :show-size-changer="true"
          :page-size-options="['20', '40', '60', '80', '100']"
          show-quick-jumper
          style="margin-top: 16px; text-align: right"
          @change="handleCurrentChange"
          @show-size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 新增弹窗 -->
    <a-modal
      v-model:open="addDialog"
      title="上传文件"
      width="50%"
      @ok="addOkHandle"
      @cancel="addDialog = false"
    >
      <a-form
        ref="userFormRef"
        :model="fileForm"
        :rules="fileFormRules"
        :label-col="{ span: 6 }"
      >
        <a-form-item label="文件名称:" name="fileName">
          <a-input
            v-model:value="fileForm.fileName"
            placeholder="请输入文件名称"
          />
        </a-form-item>
        <a-form-item label="监测方案:" name="fileUrl">
          <a-upload
            v-model:file-list="fileList"
            :action="`${baseUrl}/api/file/upload`"
            :headers="{ Authorization: token }"
            :before-upload="beforeUpload"
            :max-count="1"
            @change="handleUploadChange"
          >
            <a-button type="primary">
              <UploadOutlined />
              点击上传附件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { MenuOutlined, UploadOutlined } from '@ant-design/icons-vue';
import type {
  UploadChangeParam,
  UploadFile,
  TableColumnsType,
} from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';
import {
  getPage,
  addOrUpdate,
  updateStartType,
  deleteData,
} from '@/http/monitor-file.js';
import menuDialog from '@/components/menu.vue';

// 类型定义
interface FileItem {
  id: string;
  fileName: string;
  fileOriginalName: string;
  fileUrl: string;
  createDate: string;
  bindState: number;
  bindStateChecked: boolean;
}

interface FormData {
  timeArr: [Dayjs, Dayjs] | null;
  fileName: string;
  pageNum: number;
  pageSize: number;
}

interface FileForm {
  fileName: string;
  fileUrl: string;
  fileOriginalName: string;
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const isCollapsed = ref(true);
const selectedRowKeys = ref<string[]>([]);
const name = ref('0');
const total = ref(0);
const addDialog = ref(false);
const baseUrl = ref(import.meta.env.VITE_API_BASE_URL || '');

const formData = reactive<FormData>({
  timeArr: null,
  fileName: '',
  pageNum: 1,
  pageSize: 20,
});

const fileForm = reactive<FileForm>({
  fileName: '',
  fileUrl: '',
  fileOriginalName: '',
});

const fileFormRules = {
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    {
      min: 1,
      max: 50,
      message: '文件名称长度应在1-50个字符之间',
      trigger: 'blur',
    },
  ],
  fileUrl: [{ required: true, message: '请上传附件', trigger: 'change' }],
};

const tableData1 = ref<FileItem[]>([]);
const fileList = ref<UploadFile[]>([]);
const userFormRef = ref();

// 从store获取token
const token = computed(() => {
  // 这里需要根据你的状态管理来获取token
  return (
    localStorage.getItem('token') ||
    'Bearer e0739356-87f8-48a9-b533-6243f895286f'
  );
});

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
    align: 'center',
  },
  {
    title: '文件格式',
    key: 'format',
    align: 'center',
  },
  {
    title: '上传时间',
    dataIndex: 'createDate',
    key: 'createDate',
    align: 'center',
  },
  {
    title: '绑定状态',
    key: 'bindState',
    align: 'center',
    width: 80,
  },
  {
    title: '操作',
    key: 'action',
    align: 'center',
    width: 120,
  },
];

// 方法定义
const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const menu = (
    document.querySelector('.draggable-menu') as HTMLElement
  )?.getBoundingClientRect();
  if (!menu) return;

  const onMouseMove = (e: MouseEvent) => {
    const newX = e.clientX - menu.width / 2;
    const newY = e.clientY - menu.height / 2;

    // 边界限制
    x.value = Math.max(0, Math.min(newX, window.innerWidth - menu.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menu.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const addUserHandle = () => {
  addDialog.value = true;
  Object.assign(fileForm, {
    fileName: '',
    fileUrl: '',
    fileOriginalName: '',
  });
  fileList.value = [];
  // 重置表单验证状态
  setTimeout(() => {
    userFormRef.value?.resetFields();
  }, 100);
};

const addOkHandle = async () => {
  try {
    // 表单验证
    await userFormRef.value?.validate();

    // 验证文件是否已上传
    if (!fileForm.fileUrl) {
      message.error('请上传附件');
      return;
    }

    const res = await addOrUpdate(fileForm);
    if (res.code === 200) {
      message.success('新增成功!');
      search();
      addDialog.value = false;
      // 重置表单
      userFormRef.value?.resetFields();
    } else {
      message.error(res.msg);
    }
  } catch (error: any) {
    if (error?.errorFields) {
      message.error('请检查表单填写是否正确');
    } else {
      message.error('操作失败');
    }
  }
};

const search = () => {
  const form: any = {
    fileName: formData.fileName,
    pageNum: formData.pageNum,
    pageSize: formData.pageSize,
  };

  if (formData.timeArr) {
    form.startDate = formData.timeArr[0].format('YYYY-MM-DD');
    form.endDate = formData.timeArr[1].format('YYYY-MM-DD');
  }

  getTableData1(form);
};

const getTableData1 = async (form: any) => {
  try {
    const res = await getPage(form);
    tableData1.value = res.data.records.map((item: FileItem) => ({
      ...item,
      bindStateChecked: item.bindState === 0,
    }));
    total.value = res.data.total;
  } catch (error) {
    message.error('获取数据失败');
  }
};

const batchDeleteHandle = () => {
  if (selectedRowKeys.value.length === 0) {
    message.info('请选择删除对象');
    return;
  }
  deleteHandle({ id: '' });
};

const downloadHandle = (row: FileItem) => {
  const link = document.createElement('a');
  link.href = row.fileUrl;
  link.download = row.fileName + '.' + row.fileOriginalName.split('.')[1];
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const deleteHandle = (row: FileItem) => {
  const userIds = row.id || selectedRowKeys.value.join(',');
  Modal.confirm({
    title: '删除确认',
    content: '是否确认删除选中的数据项?',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      return deleteData(userIds)
        .then((res: any) => {
          if (res.code === 200) {
            message.success('删除成功!');
          }
          search();
        })
        .catch(() => {
          message.error('删除失败');
        });
    },
  });
};

const handleSelectionChange = (selectedKeys: string[]) => {
  selectedRowKeys.value = selectedKeys;
};

const handleSizeChange = (current: number, size: number) => {
  formData.pageSize = size;
  search();
};

const handleCurrentChange = (page: number) => {
  formData.pageNum = page;
  search();
};

const resetForm = () => {
  Object.assign(formData, {
    timeArr: null,
    fileName: '',
    pageNum: 1,
    pageSize: 20,
  });
  search();
};

const beforeUpload = (file: UploadFile) => {
  const isLt10M = file.size! / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!');
  }
  return isLt10M;
};

const handleUploadChange = (info: UploadChangeParam) => {
  if (info.file.status === 'done') {
    if (info.file.response && info.file.response.data) {
      fileForm.fileUrl = info.file.response.data.url;
      fileForm.fileOriginalName = info.file.response.data.name;
      message.success('上传成功');
      // 清除文件上传字段的验证错误
      userFormRef.value?.clearValidate('fileUrl');
    }
  } else if (info.file.status === 'error') {
    message.error('上传失败');
    fileForm.fileUrl = '';
    fileForm.fileOriginalName = '';
  }
};

const switchHandle = async (editIndex: number, row: FileItem) => {
  const newBindState = row.bindStateChecked ? 0 : 1;
  try {
    const res = await updateStartType({ id: row.id, bindState: newBindState });
    if (res.code !== 200) {
      message.error(res.msg);
      // 恢复原状态
      row.bindStateChecked = !row.bindStateChecked;
    } else {
      row.bindState = newBindState;
    }
  } catch (error) {
    message.error('操作失败');
    row.bindStateChecked = !row.bindStateChecked;
  }
};

onMounted(() => {
  search();
});
</script>

<style lang="less" scoped>
//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;
}

.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
}

.main-body-s {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}
// 滚动条隐藏
div::-webkit-scrollbar {
  display: none;
}

.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  color: #ffffff;
  display: block;

  .search {
    width: 100%;

    .title {
      margin-bottom: 10px;
    }
  }
}

.table-scrollbar {
  width: 100%;
  height: calc(100% - 200px);

  .title {
    margin-bottom: 10px;
  }

  .table-div {
    width: 100%;
    height: calc(100% - 50px);
    background: transparent;
  }
}

:deep(.ant-modal) {
  .ant-modal-header {
    background-color: #073aa9;
    border-bottom: 1px solid #86a5e7;
  }

  .ant-modal-title {
    color: #ffffff;
  }

  .ant-modal-body {
    background-color: #073aa9;
    color: #ffffff;
  }

  .ant-modal-footer {
    background-color: #073aa9;
    border-top: 1px solid #86a5e7;
  }
}
.table-scrollbar {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}
// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
