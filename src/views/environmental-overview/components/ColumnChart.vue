<template>
  <div ref="chartRef" :style="`width: ${width}px; height: ${height}px;`"></div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

interface Props {
  chartData: number[][];
  timeData: string[];
  title?: string;
  height: number;
  width?: number;
}

const props = withDefaults(defineProps<Props>(), {
  chartData: () => [],
  timeData: () => [],
  title: '',
  height: 300,
  width: 800,
});

const chartRef = ref<HTMLElement>();
let myChart: echarts.ECharts | null = null;

const initChart = () => {
  if (!chartRef.value) return;
  
  // 基于准备好的dom，初始化echarts实例
  const chartDom = chartRef.value;
  myChart = echarts.init(chartDom);

  // 指定图表的配置项和数据
  const option = {
    title: {
      text: props.title,
      textStyle: {
        color: "#fff",
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow' // 鼠标悬停时显示阴影
      }
    },
    legend: {
      data: [], // 根据实际系列名称修改
      bottom: "5%",
      left: "center",
      icon: "circle",
      textStyle: {
        color: "#fff",
      },
    },
    xAxis: {
      type: 'category',
      data: props.chartData[0] || [], // x轴数据
      axisLabel: {
        color: "#99b7d2", // x轴字体颜色
        interval: 0,
        rotate: 25
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: "#99b7d2", // y轴字体颜色
      },
    },
    series: [
      {
        name: '废气超标量', // 系列名称
        type: 'bar', // 柱状图
        data: props.chartData[1] || [], // 系列1的数据
        itemStyle: {
          color: 'rgba(83, 240, 233, 0.8)', // 系列1的颜色
        },
        barWidth: '20%', // 柱状图宽度
      },
      {
        name: '废水超标量', // 系列名称
        type: 'bar', // 柱状图
        data: props.chartData[2] || [], // 系列2的数据
        itemStyle: {
          color: 'rgba(255, 183, 77, 0.8)', // 系列2的颜色
        },
        barWidth: '20%', // 柱状图宽度
      },
    ]
  };
  
  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(option);
};

const handleResize = () => {
  // 窗口变化时保持容器宽度固定，禁止图表缩放
  if (chartRef.value) {
    chartRef.value.style.width = '800px';
  }
};

// 监听数据变化
watch(() => props.chartData, () => {
  initChart();
}, { deep: true });

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style scoped>
/* 你可以在这里添加一些样式 */
</style>