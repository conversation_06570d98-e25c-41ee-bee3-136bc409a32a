<template>
  <div
      class="environmental-overview-page"
      v-loading.fullscreen.lock="fullscreenLoading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div id="screen-map" class="screen-map"></div>
    <div class="main-background"></div>
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <el-input
          v-model.trim="filterText"
          class="search"
          placeholder="输入关键词"
          clearable
          size="small"
          prefix-icon="el-icon-search"
      />
      <div class="tree-wrapper">
        <el-tree
            v-if="mainModel === '0'"
            ref="monitorList"
            :data="treeData"
            node-key="vldSiteId"
            :props="defaultProps"
            highlight-current
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
        >
        </el-tree>
        <el-tree
            v-if="mainModel === '1'"
            ref="monitorList"
            :data="treeDataJy"
            node-key="id"
            :props="defaultProps"
            highlight-current
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
        >
          <div slot-scope="{ data }" class="custom-tree-node">
            <span>{{ data.label }}</span>
          </div>
        </el-tree>
      </div>
    </div>
    <img
        src="@/assets/images/shouqi.png"
        alt=""
        :class="['left-img', isCollapsed ? 'left-img-s' : '']"
        @click="isCollapsed = !isCollapsed"
    />
    <img
        src="@/assets/images/shouqiicon.png"
        alt=""
        :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
        @click="isCollapsed = !isCollapsed"
    />
    <!-- 菜单图标 -->
    <div
        class="draggable-menu"
        ref="menu"
        :style="{ left: `${x}px`, top: `${y}px` }"
        @mousedown="startDrag"
        @click="iconHandle"
        :class="{ 'active': isDragging }"
    >
      <div class="menu-icon">
        <i class="el-icon-menu"></i>
      </div>
    </div>
    <div :class="['main-tree']" v-if="isMenu" style="background-color:rgba(11, 32, 87, 0.6);">
      <menuDialog></menuDialog>
    </div>

    <div :class="['control', isCollapsed ? 'control-s' : '']">
      <div class="control-div">
        <img
            class="img"
            src="@/assets/images/control1.png"
            alt=""
            @click="refreshMap"
        />
        <img class="img img1" src="@/assets//images/control3.png" alt=""/>
        <div class="imgClick">
          <div @click="mapCale(0.5)"></div>
          <div @click="mapCale(-0.5)"></div>
        </div>
      </div>
    </div>
    <div :class="['select', (isCollapsed && !isMenu) ? 'select-s' : '']">
      <el-select v-model="mainModel" placeholder="请选择" @change="mainChange">
        <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        >
        </el-option>
      </el-select>
    </div>
    <div class="card" v-if="mainModel === '0' ">
      <div class="card-top">
        <cardCollapse :title="'数据统计'" key="1" :height="'400px'" @child="childHandle">
          <template #content>
            <div class="select-jy select-jy-jy" style="width: 800px">
              <el-form
                  :inline="true"
                  :model="environmentalData"
              >
                <el-form-item label="日期">
                  <el-date-picker
                      v-model="environmentalData.day"
                      type="date"
                      placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="queryHandle()">查询</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="content-jy">
              <column-chart :chart-data="columnDate" v-if="chartShow"></column-chart>
            </div>
          </template>
        </cardCollapse>
      </div>
    </div>
    <div class="card" v-if="!isCollapsed1 && this.mainModel==='0'" style="margin-top: 80px;">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>废水实时监测</span>
          <el-select v-show="show1" style="margin-bottom: 8px" v-model="siteId" placeholder="请选择"
                     @change="chooseHandle">
            <el-option
                v-for="item in sites"
                :key="item.id"
                :label="item.siteName"
                :value="item.id"
            >
            </el-option>
          </el-select>
          <el-button type="text" @click="openHandle(0)"
                     style="position: absolute;z-index: 1999;display: flex;
                     justify-content: flex-end;margin-left: 1rem;pointer-events: fill;">
            <span v-if="!show1">展开</span>
            <span v-if="show1">收缩</span>
          </el-button>
        </div>
        <div v-if="!show1" style="width: 120px"></div>
        <el-table
            v-show="show1"
            :key="0"
            ref="table1"
            :data="tableData1"
            style="width: 100%"
            :height="height"
            :row-class-name="tableRowClassName"
            class="no-header-table"
            @mouseenter.native="rollingTable('table1',rollingModel.rollingTableStop)"
            @mouseleave.native="rollingTable('table1',rollingModel.rollingTableStart)"
        >
          <el-table-column
              prop="site"
              label="单位"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="monitorName"
              label="监测点"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="mTime"
              label="监测时间"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="cwater011"
              label="COD"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="cwater060"
              label="氨氮"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </el-card>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>废气实时监测</span>
          <el-button type="text" @click="openHandle(1)"
                     style="position: absolute;z-index: 1999;display: flex;
                     justify-content: flex-end;margin-left: 1rem;pointer-events: fill;">
            <span v-if="!show2">展开</span>
            <span v-if="show2">收缩</span>
          </el-button>
        </div>
        <el-table
            v-show="show2"
            :key="1"
            ref="table2"
            :data="tableData2"
            style="width: 100%"
            :height="height"
            :row-class-name="tableRowClassName"
            class="no-header-table"
            @mouseenter.native="rollingTable('table2',rollingModel.rollingTableStop)"
            @mouseleave.native="rollingTable('table2',rollingModel.rollingTableStart)"
        >
          <el-table-column
              prop="site"
              label="单位"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="monitorName"
              label="监测点"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="mTime"
              label="监测时间"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="cair02R"
              label="SO₂实测"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="cair03R"
              label="NOX实测"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </el-card>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>超标情况监测</span>
          <el-button type="text" @click="openHandle(2)"
                     style="position: absolute;z-index: 1999;display: flex;
                     justify-content: flex-end;margin-left: 1rem;pointer-events: fill;">
            <span v-if="!show3">展开</span>
            <span v-if="show3">收缩</span>
          </el-button>
        </div>
        <el-tabs v-show="show3"
                 v-model="activeName" @tab-click="handleClick" style="position: absolute;z-index: 1999;display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
    pointer-events: fill;">
          <el-tab-pane label="废水超标情况" name="0"/>
          <el-tab-pane label="废气超标情况" name="1"/>
        </el-tabs>
        <el-table
            v-show="show3"
            v-if="this.activeName ==='0'"
            ref="table3"
            :data="tableData3"
            style="width: 100%;margin-top: 40px;"
            :height="height"
            :row-class-name="tableRowClassName"
            class="no-header-table"
            @mouseenter.native="rollingTable('table3',rollingModel.rollingTableStop)"
            @mouseleave.native="rollingTable('table3',rollingModel.rollingTableStart)"
        >
          <el-table-column
              prop="site"
              label="单位"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="monitorName"
              label="监测点"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="mTime"
              label="监测时间"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="cwater011"
              label="COD"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="cwater060"
              label="氨氮"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
        <el-table
            v-show="show3"
            v-if="this.activeName ==='1'"
            ref="table4"
            :data="tableData4"
            style="width: 100%;margin-top: 40px;"
            :height="height"
            :row-class-name="tableRowClassName"
            class="no-header-table"
            @mouseenter.native="rollingTable('table4',rollingModel.rollingTableStop)"
            @mouseleave.native="rollingTable('table4',rollingModel.rollingTableStart)"
        >
          <el-table-column
              prop="site"
              label="单位"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="monitorName"
              label="监测点"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="mTime"
              label="监测时间"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="cair02R"
              label="SO₂实测"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="cair03R"
              label="NOX实测"
              align="center"
              show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    <div class="card card-jy" v-if="mainModel === '1'">
      <div class="card-top">
        <cardCollapse title="甲烷管控模块" key="4" @child="childHandle">
          <template #content v-if="chartShow">
            <div class="select-jy select-jy-jy">
              <el-form
                  :inline="true"
                  :model="alarmSituationData"
                  class="demo-form-inline"
              >
                <el-form-item label="点位列表">
                  <el-select v-model="typeModeJy" placeholder="请选择">
                    <el-option
                        v-for="item in methaneOptions"
                        :key="item.id"
                        :label="item.deviceMark"
                        :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="开始时间">
                  <el-date-picker
                      v-model="timeArr"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :clearable="false"
                      popper-class="jyPicker"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="onSubmit('0')"
                  >实时查询
                  </el-button
                  >
                  <el-button type="primary" @click="onSubmit('1')"
                  >历史查询
                  </el-button
                  >
                </el-form-item>
              </el-form>
            </div>
            <el-row>
              <el-col :span="12">
                <line-chart :chart-data="data1.value" :time-data="data1.time" :height="200"
                            :legend="'浓度检测值 实时值'+value1+'ppm.m'" :title="'浓度检测值'"></line-chart>
              </el-col>
              <el-col :span="12">
                <line-chart :chart-data="data2.value" :time-data="data2.time" :height="200"
                            :legend="'浓度检测报警 实时值'+value2+' '+value3" :title="'浓度检测报警'"></line-chart>
              </el-col>
            </el-row>
          </template>
        </cardCollapse>
      </div>
      <div class="card-top">
        <cardCollapse title="气体泄漏报警统计" key="5">
          <template #content="{isCollapsed}">
            <div v-if="isCollapsed">
              <div class="select-jy select-jy-jy">
                <el-form
                    :inline="true"
                    :model="alarmSituationData"
                    class="demo-form-inline"
                >
                  <el-form-item label="点位列表">
                    <el-select v-model="typeModeJyQt" placeholder="请选择">
                      <el-option
                          v-for="item in methaneOptions"
                          :key="item.id"
                          :label="item.deviceMark"
                          :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="开始时间">
                    <el-date-picker
                        v-model="timeArrQt"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        popper-class="jyPicker"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="onSubmitQt"
                    >查询
                    </el-button
                    >
                    <el-button type="primary" @click="resetHandle"
                    >重置
                    </el-button
                    >
                  </el-form-item>
                </el-form>
              </div>
              <div style="width: 922px;">
                <el-table
                  :data="alarmTableData"
                  style="width: 100%"
                  height="300"
                  :row-class-name="tableRowClassName"
                  class="no-hover-table"
                >
                  <el-table-column
                    prop="pointName"
                    label="点位名称"
                    align="center"
                    show-overflow-tooltip
                  >
                  </el-table-column>
                  <el-table-column
                    prop="alarmsDateStr"
                    label="报警时间"
                    align="center"
                    show-overflow-tooltip
                  >
                  </el-table-column>
                  <el-table-column
                    prop="remark"
                    label="备注"
                    align="center"
                    show-overflow-tooltip
                  >
                  </el-table-column>
                </el-table>
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="alarmCurrentPage"
                  :page-sizes="[20, 40, 60, 80, 100]"
                  :page-size="alarmPageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="alarmTotal"
                  style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
              </div>
            </div>
          </template>
        </cardCollapse>
      </div>
    </div>
    <detailsDialog
        :deviceList="deviceList"
        :siteShortName="siteShortName"
        ref="detailsDialogRef"
    />
    <transition name="fade">
      <img v-if="dialogVisible"
           :src="dialogImageUrl"
           alt="Preview"
           class="preview-image">
    </transition>
  </div>
</template>
<script>
// 建议：每个页面需要的接口文件在 根目录下的 http 文件夹中新建一个文件，文件名为页面名.js
// 示例：
import {
  getDeptPoint,
  getHistoryData,
  getJoinDept,
  getMonitorInfoById,
  getMonitorInfoList,
  getMonitorList,
  getMonitorStatistics,
  getRealTimeData,
  getSiteList,
  getTreeNode,
  getAlarmPage
} from "@/http/environmental-overview";
import {overMonitorList} from "@/http/alarm-query"
import cardCollapse from "@/components/card-collapse.vue";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import detailsDialog from "./components/details-dialog.vue";
import ColumnChart from "./components/ColumnChart.vue";
import LineChart from "./components/LineChart.vue";
import menuDialog from "@/components/menu.vue";
import dayjs from "dayjs";

export default {
  components: {cardCollapse, detailsDialog, ColumnChart, LineChart, menuDialog},
  data() {
    return {
      x: 20,         // 初始X位置
      y: 500,         // 初始Y位置
      isDragging: false,
      isMenu: false,
      show1: true,
      show2: true,
      show3: true,
      siteList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hoverTimer: null,
      chartShow: false,
      height: window.innerHeight * 0.2,
      siteId: '',
      isCollapsed1: false,
      environmentalData: {
        day: new Date()
      },
      siteShortName: '',
      columnDate: [],
      siteName: '',
      activeName: '0',
      sites: [],
      deviceList: [],
      treeData: [],
      defaultProps: {
        children: "children",
        label: "siteShortName",
      },
      oldNode: [],
      isCollapsed: false,
      filterText: "",
      fullscreenLoading: false,
      sl: "http://11.89.144.83:8195/portalproxy/iserver/services/dom_ugcv5/mywmts/dom_ugcv5/default/Custom_dom_ugcv5/{z}/{y}/{x}.png",
      map: null,
      moveendEvent: null,
      positionArr: [],
      orgNode: null, //选中的组织机构信息
      endMarker: [],
      treeDataJy: [],
      mainModel: "0",
      alarmSituationData: {},
      methaneOptions: [], //甲烷模块点位列表
      typeModeJy: "",
      typeModeJyQt: "", // 气体泄漏报警统计点位列表
      firstTypeModeJy: "",
      timeArr: null, // 甲烷管控开始时间
      timeArrQt: null, // 气体泄漏报警统计开始时间
      timeArr1: null,
      timer: null,
      timer1: null,
      data1: [],
      data2: [],
      value1: 0,
      value2: 0,
      value3: '正常',
      options: [
        {
          label: "污染源在线管理模块",
          value: "0",
        },
        {
          label: "甲烷管控模块",
          value: "1",
        },
      ],
      tableData1: [],
      tableData2: [],
      tableData3: [],
      tableData4: [],
      // 气体泄漏报警统计表格数据
      alarmTableData: [],
      alarmCurrentPage: 1,
      alarmPageSize: 20,
      alarmTotal: 0,
      realMapboxgl: null,
    };
  },
  watch: {
    //树状搜索
    filterText(val) {
      this.$refs.monitorList.filter(val);
    },
  },
  destroyed() {
    clearInterval(this.timer);
    clearInterval(this.timer1);
    this.timer = null;
    this.timer1 = null;
  },
  async mounted() {
    // 获取组织机构数据
    await this.getTree();
    // 获取甲烷组织机构数据
    await this.getTreeJy();
    this.initMap(this.sl, "EBq07rAk2Hn1dfQxzLs9slqj");
    window["handlePopupClick"] = (id) => {
      this.handlePopupClick(id);
    };
    // 所有点
    await this.getMonitorInfoLists();
    // 图表
    this.endMarker.forEach((marker) => {
      if (marker && marker.remove) {
        marker.remove();
      }
    });
    this.addPoint(false);

    // 表格滚动
    this.rollingTable("table1", this.rollingModel.rollingTableStart);
    this.rollingTable("table2", this.rollingModel.rollingTableStart);
    this.rollingTable("table3", this.rollingModel.rollingTableStart);
  },
  methods: {
    resetHandle() {
      this.timeArrQt = null;
      this.typeModeJyQt = '';
      this.alarmCurrentPage = 1;
      this.alarmPageSize = 20;
      this.alarmTableData = [];
      this.alarmTotal = 0;
      this.onSubmitQt();
    },
    iconHandle() {
      this.isMenu = !this.isMenu;
      this.isCollapsed = true;
    },
    startDrag(e) {
      this.isDragging = true;
      const menu = this.$refs.menu.getBoundingClientRect();

      const onMouseMove = (e) => {
        const x = e.clientX - menu.width / 2;
        const y = e.clientY - menu.height / 2;

        // 边界限制
        this.x = Math.max(0, Math.min(x, window.innerWidth - menu.width));
        this.y = Math.max(0, Math.min(y, window.innerHeight - menu.height));
      }

      const onMouseUp = () => {
        this.isDragging = false;
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
      }

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    },
    openHandle(type) {
      if (type == 0) {
        this.show1 = !this.show1
      }
      if (type == 1) {
        this.show2 = !this.show2
      }
      if (type == 2) {
        this.show3 = !this.show3
      }
    },
    getSiteList(id) {
      getSiteList(id).then(res => {
        if (res.data.length > 0 && res.code === 200) {
          this.siteList = []
          for (let i = 1; i <= res.data.length; i++) {
            if (res.data.monitorType == "10") {
              this.siteList.push(require('@/assets/fkt/imgW.png'))
            } else {
              this.siteList.push(require('@/assets/fkt/imgA.png'))
            }
          }
        } else {
          this.siteList = []
        }
      })
    },
    sumWithTwoDecimals(value1, value2) {
      // 将值转换为数字（防止字符串拼接）
      const num1 = parseFloat(value1) || 0;
      const num2 = parseFloat(value2) || 0;

      // 求和并保留 2 位小数
      const sum = num1 + num2;
      return sum.toFixed(2);
    },
    // 修改表格颜色
    tableRowClassName({row, rowIndex}) {
      if (rowIndex % 2 === 0) {
        return "gray";
      } else {
        return "blue";
      }
    },
    chooseHandle() {
      console.log("siteId", this.siteId)
      this.queryHandle()
    },
    handleClick(e) {
      if (e.index === '0') {
        this.rollingTable("table3", this.rollingModel.rollingTableStart);
      } else {
        this.rollingTable("table4", this.rollingModel.rollingTableStart);
      }
    },
    childHandle(e) {
      this.chartShow = e.queryParam
      this.isCollapsed1 = e.queryParam;
      this.rollingTable("table1", this.rollingModel.rollingTableStart);
      this.rollingTable("table2", this.rollingModel.rollingTableStart);
      this.rollingTable("table3", this.rollingModel.rollingTableStart);
      this.activeName = '0'
    },
    // 查询
    queryHandle() {
      let date = '';
      if (this.environmentalData.day != null) {
        let year = this.environmentalData.day.getFullYear();
        let month = this.environmentalData.day.getMonth() + 1 < 10 ? "0" + (this.environmentalData.day.getMonth() + 1) : (this.environmentalData.day.getMonth() + 1);
        let day = this.environmentalData.day.getDate() < 10 ? "0" + this.environmentalData.day.getDate() : this.environmentalData.day.getDate();
        date = year + "-" + month + "-" + day;
      }
      this.getMonitorStatistics(date)
      this.monitorListBySite(date, this.siteId, 10)
      this.monitorListBySite(date, this.siteId, 20)
      this.overMonitorListBySite(date, this.siteId, 10)
      this.overMonitorListBySite(date, this.siteId, 20)
    },
    // 获取组织机构数据
    async getTree() {
      const res = await getTreeNode();
      this.treeData = res.data;
    },
    // 获取排放口摄像头列表
    async getCameraLists(params) {
      // const res = await getCameraList(params);
      this.deviceList = [
        {
          "indexCode": "51170000581314000088",
          "resourceType": "camera",
          "externalIndexCode": "51170000581314000088",
          "name": "铁石线连心村2组高铁穿越",
          "chanNum": null,
          "dacIndexCode": null,
          "cascadeCode": "51170100002000000066",
          "parentIndexCode": null,
          "longitude": null,
          "latitude": null,
          "elevation": null,
          "cameraType": 0,
          "capability": null,
          "recordLocation": "0",
          "channelType": null,
          "regionIndexCode": "51170000582160000004",
          "regionPath": "@root000000@dda79ba8-cf26-4e5f-ae6e-fe757eb1a0d3@3aeffd2d-5b4a-4e65-8a11-efc6d70cef95@6c3d4363-dd3a-4077-b563-2cd614ab278d@51170000582160000004@",
          "transType": null,
          "treatyType": null,
          "installLocation": null,
          "createTime": "2024-11-25T19:34:01.000+08:00",
          "updateTime": "2025-03-13T05:00:35.000+08:00",
          "disOrder": 2545,
          "resourceIndexCode": null,
          "decodeTag": "hikvision",
          "cameraRelateTalk": null,
          "regionName": "铁石线",
          "regionPathName": "根节点/G1006/CQPQ/CDBQK/铁石线"
        },
        {
          "indexCode": "51170000581314000001",
          "resourceType": "camera",
          "externalIndexCode": "51170000581314000001",
          "name": "大竹县石河镇五四村",
          "chanNum": null,
          "dacIndexCode": null,
          "cascadeCode": "51170100002000000066",
          "parentIndexCode": null,
          "longitude": null,
          "latitude": null,
          "elevation": null,
          "cameraType": 2,
          "capability": null,
          "recordLocation": "0",
          "channelType": null,
          "regionIndexCode": "51170000582160000004",
          "regionPath": "@root000000@dda79ba8-cf26-4e5f-ae6e-fe757eb1a0d3@3aeffd2d-5b4a-4e65-8a11-efc6d70cef95@6c3d4363-dd3a-4077-b563-2cd614ab278d@51170000582160000004@",
          "transType": null,
          "treatyType": null,
          "installLocation": null,
          "createTime": "2023-10-25T17:47:58.000+08:00",
          "updateTime": "2025-03-13T05:00:35.000+08:00",
          "disOrder": 841,
          "resourceIndexCode": null,
          "decodeTag": "hikvision",
          "cameraRelateTalk": null,
          "regionName": "铁石线",
          "regionPathName": "根节点/G1006/CQPQ/CDBQK/铁石线"
        },
        {
          "indexCode": "51170000581314000002",
          "resourceType": "camera",
          "externalIndexCode": "51170000581314000002",
          "name": "大竹县石河镇严家桥村6组",
          "chanNum": null,
          "dacIndexCode": null,
          "cascadeCode": "51170100002000000066",
          "parentIndexCode": null,
          "longitude": null,
          "latitude": null,
          "elevation": null,
          "cameraType": 2,
          "capability": null,
          "recordLocation": "0",
          "channelType": null,
          "regionIndexCode": "51170000582160000004",
          "regionPath": "@root000000@dda79ba8-cf26-4e5f-ae6e-fe757eb1a0d3@3aeffd2d-5b4a-4e65-8a11-efc6d70cef95@6c3d4363-dd3a-4077-b563-2cd614ab278d@51170000582160000004@",
          "transType": null,
          "treatyType": null,
          "installLocation": null,
          "createTime": "2023-10-25T17:47:58.000+08:00",
          "updateTime": "2025-03-13T05:00:35.000+08:00",
          "disOrder": 813,
          "resourceIndexCode": null,
          "decodeTag": "hikvision",
          "cameraRelateTalk": null,
          "regionName": "铁石线",
          "regionPathName": "根节点/G1006/CQPQ/CDBQK/铁石线"
        }
      ];
    },
    // 获取点位信息，经纬度
    async getMonitorInfoByIds(id) {
      console.log("获取点位信息，经纬度", id);
      const res = await getMonitorInfoById({id});
      console.log("获取点位信息，经纬度", res);
      this.sites = Array.from(
          new Map(this.sites.map((item) => [item.id, item])).values()
      );
      console.log("this.sites", this.sites)
      this.sites.push(res.data);
    },
    // 所有点
    async getMonitorInfoLists() {
      const res = await getMonitorInfoList();
      console.log("MonitorInfoLists", res);
      this.sites = res.data;
      if (this.sites.length > 0) {
        this.siteName = this.sites[0].siteName;
        this.siteId = this.sites[0].id
        let date = '';
        if (this.environmentalData.day != null) {
          let year = this.environmentalData.day.getFullYear();
          let month = this.environmentalData.day.getMonth() + 1 < 10 ? "0" + (this.environmentalData.day.getMonth() + 1) : (this.environmentalData.day.getMonth() + 1);
          let day = this.environmentalData.day.getDate() < 10 ? "0" + this.environmentalData.day.getDate() : this.environmentalData.day.getDate();
          date = year + "-" + month + "-" + day;
        }
        // 获取数据统计
        this.getMonitorStatistics(date)
        // 获取废水实时监测
        this.monitorListBySite(date, this.siteId, 10)
        // 获取废气实时监测
        this.monitorListBySite(date, this.siteId, 20)
        // 获取废水超标情况
        this.overMonitorListBySite(date, this.siteId, 10)
        // 获取废气超标情况
        this.overMonitorListBySite(date, this.siteId, 20)
      }
    },
    // 监测点统计
    getMonitorStatistics(date) {
      getMonitorStatistics({date: date}).then(res => {
        this.columnDate = res.data;
      })
    },
    // 查找树节点
    findNodeById(nodes, id) {
      for (let node of nodes) {
        if (node.id === id) {
          return node; // 找到目标节点，返回它
        }
        // 如果当前节点有子节点，递归查找子节点
        if (node.children && node.children.length > 0) {
          let result = this.findNodeById(node.children, id);
          if (result) {
            return result; // 如果在子节点中找到了目标节点，返回结果
          }
        }
      }
      return null; // 如果没有找到目标节点，返回 null
    },
    // 获取甲烷组织机构下点位
    getTreePoint(val) {
      return new Promise((resolve, reject) => {
        getDeptPoint({deptId: val ? this.treeDataJy[0].id : this.orgNode.id})
            .then((res) => {
              this.methaneOptions = res.rows || [];
              if (val) {
                this.firstTypeModeJy = this.typeModeJy = this.methaneOptions
                    .length
                    ? this.methaneOptions[0].id
                    : "";
              } else {
                this.typeModeJy = this.methaneOptions.length
                    ? this.methaneOptions[0].id
                    : "";
              }
              if (this.methaneOptions.length) {
                resolve();
              } else {
                reject();
              }
            })
            .catch(() => {
              reject();
            });
      });
    },
    // 缩放地图
    mapCale(val) {
      console.log("缩放地图")
      let zoom = this.map.getZoom() + val;
      this.map.flyTo({
        zoom: zoom, // 目标缩放级别
        speed: 1, // 动画速度（可选，默认为1）
      });
    },
    // 刷新地图
    refreshMap() {
      if (this.map && this.map.remove) {
        this.map.remove();
      }
      this.$nextTick(() => {
        this.initMap(this.sl, "EBq07rAk2Hn1dfQxzLs9slqj");
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      if (this.mainModel === "0") {
        return data.siteShortName.indexOf(value) !== -1;
      } else {
        return data.siteShortName.indexOf(value) !== -1;
      }
    },
    // 获取甲烷组织机构数据
    async getTreeJy() {
      try {
        const data = await getJoinDept();
        this.treeDataJy = data.data || [];
        await this.getTreePoint("firsh");
      } catch {
        console.log();
      }
    },
    async mainChange() {
      this.reset();
      let dom = document.getElementsByClassName("mapboxgl-popup");
      for (let i = 0; i < dom.length; i++) {
        if (dom[i] && dom[i].remove) {
          dom[i].remove();
        }
      }
      this.refreshMap();
      if (this.mainModel === "0") {
      } else {
        this.$nextTick(() => {
          this.$refs.monitorList.setCurrentKey(this.treeDataJy[0].id);
        });
        this.onSubmitQt();
        try {
          await this.getTreePoint("firsh");
          await this.onSubmit("0");
        } catch {
          console.log();
        }
      }
      if (this.mainModel == "0") {
        await this.$nextTick();
      }
      this.chartShow = false
      this.isCollapsed = false
      this.isCollapsed1 = false;
    },
    reset() {
      this.orgNode = null;
      this.oldNode = [];
      this.filterText = "";
      this.typeModeJy = this.firstTypeModeJy;
      this.typeModeJyQt = ''
      this.methaneOptions = [];
      const now = new Date();
      // 获取前一天的日期
      const dayBefore = new Date(now);
      dayBefore.setDate(dayBefore.getDate() - 1);

      // 格式化日期和时间
      function formatDateTime(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const hour = date.getHours().toString().padStart(2, "0");
        const minute = date.getMinutes().toString().padStart(2, "0");
        const second = date.getSeconds().toString().padStart(2, "0");
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      }

      this.timeArr = [formatDateTime(dayBefore), formatDateTime(now)];
      this.timeArrQt = [formatDateTime(dayBefore), formatDateTime(now)];
      clearInterval(this.timer);
      clearInterval(this.timer1);
      this.timer = null;
      this.timer1 = null;
    },
    // 点击树状
    async handleNodeClick(data, node, html) {
      this.oldNode.forEach((item) => {
        item.checked = false;
      });
      this.oldNode = [];
      let that = this;

      function filter(newNode) {
        if (newNode.parent) {
          that.oldNode.push(newNode.parent);
          newNode.parent.checked = true;
          filter(newNode.parent);
        }
      }

      filter(node);
      this.orgNode = this.$refs.monitorList.getCurrentNode();
      if (this.mainModel === "0") {
        if (this.orgNode.monitorType) {
          this.siteShortName = this.orgNode.siteShortName;
          await this.getMonitorInfoByIds(this.orgNode.id);
          this.endMarker.forEach((marker) => {
            if (marker && marker.remove) {
              marker.remove();
            }
          });
          this.addPoint(true);
        }
      } else {

      }
    },
    onSubmitQt() {
     this.alarmCurrentPage = 1;
     this.alarmPageSize = 20;
     this.alarmTableData = [];
     this.alarmTotal = 0;
     this.getAlarmStatistics();
    },
    // 分页大小改变
    handleSizeChange(val) {
      this.alarmPageSize = val;
      this.alarmCurrentPage = 1;
      this.getAlarmStatistics();
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.alarmCurrentPage = val;
      this.getAlarmStatistics();
    },
    // 获取报警统计数据
    async getAlarmStatistics() {
      const data = {
        point: this.typeModeJyQt,
        startTime: this.timeArrQt ? this.timeArrQt[0] : '',
        endTime: this.timeArrQt ? this.timeArrQt[1] : '',
        pageNum: this.alarmCurrentPage,
        pageSize: this.alarmPageSize,
      }
      try {
        const res = await getAlarmPage(data);
        this.alarmTotal = res.total;
        this.alarmTableData = res.rows;
        console.log(this.alarmTableData)
      } catch (error) {
        console.error("获取报警统计数据失败:", error);
      }
    },
    async onSubmit(val) {
      // 清理旧定时器
      clearInterval(this.timer);
      const node = this.methaneOptions.find((item) => {
        return item.id === this.typeModeJy;
      });
      if (val === "0") {
        this.timerHandle(node)
      } else {
        const data = {
          monitorPoint: node.monitorPointNo || "",
          alarmPoint: node.alarmPointNo || "",
          startTime: this.timeArr[0],
          endTime: this.timeArr[1],
        };
        getHistoryData(data).then((res) => {
          this.data1 = res.data[0]
          this.data2 = res.data[1]
          this.value1 = (res.data[0] !== undefined && res.data[0] !== null && res.data[0].value.length > 0) ? res.data[0].value[res.data[0].value.length - 1] : 0;
          this.value2 = (res.data[1] !== undefined && res.data[1] !== null && res.data[1].value.length > 0) ? res.data[1].value[res.data[1].value.length - 1] : 0;
          if (this.value2 != '0') {
            this.value3 = '报警'
          } else {
            this.value3 = '正常'
          }
        });
      }
    },

    async timerHandle(node) {
      this.timer = null;
      // 初始化数据容器（最多存储5条记录）
      this.data1 = {
        time: [],  // 时间队列
        value: []  // 数值队列
      };
      this.data2 = {
        time: [],  // 时间队列
        value: []  // 数值队列
      };
      const data = {
        monitorPoint: node.monitorPointNo || "",
        alarmPoint: node.alarmPointNo || ""
      };
      // 立即执行第一次请求
      let isFetching = false;
      const fetchData = async () => {
        if (isFetching) return;
        isFetching = true;
        try {
          const res = await getRealTimeData(data);
          const timestamp = dayjs().format("YYYY-MM-DD HH:mm:ss");
          const value1 = res.code === 500 ? 0 : res.data[0]?.value[0] || 0;
          const value2 = res.code === 500 ? 0 : res.data[1]?.value[0] || 0;
          // 维护队列长度（最大5条）
          if (this.data1.time.length >= 5) {
            this.data1.time.shift();
            this.data1.value.shift();
          }
          if (this.data2.time.length >= 5) {
            this.data2.time.shift();
            this.data2.value.shift();
          }
          this.data1.time.push(timestamp);
          this.data1.value.push(value1);
          this.data2.time.push(timestamp);
          this.data2.value.push(value2);
        } catch (error) {
          console.error("请求失败:", error);
        } finally {
          isFetching = false;
        }
      };
      // 启动轮询（立即执行+定时执行）
      await fetchData();
      this.timer = setInterval(fetchData, 5000);
    },


    initMap(iserverUrl, key) {
      var attribution =
          "<a href='https://www.mapbox.com/about/maps/' target='_blank'>© Mapbox </a>" +
          " with <span>© <a href='https://iclient.supermap.io' target='_blank'>SuperMap iClient</a> | </span>" +
          " Map Data <span>© <a href='http://support.supermap.com.cn/product/iServer.aspx' target='_blank'>SuperMap iServer</a></span> ";
      // eslint-disable-next-line no-undef
      this.realMapboxgl = this.$mapboxgl || mapboxgl;
      // eslint-disable-next-line no-undef
      this.map = new this.realMapboxgl.Map({
        container: "screen-map",
        /* 内网 */
        style: {
          version: 8,
          sources: {
            A4: {
              type: "raster",
              tiles: [iserverUrl],
              tileSize: 256,
            },
          },
          layers: [
            {
              id: "A4-tiles",
              type: "raster",
              source: "A4",
            },
          ],
        },
        transformRequest: (url, resourceType) => {
          if (url.indexOf("portalproxy") != -1) {
            if (url.indexOf("&") != -1) {
              return {
                url: url + `&key=${key}`,
              };
            } else {
              return {
                url: url + `?key=${key}`,
              };
            }
          }
        },
        // eslint-disable-next-line no-undef
        crs: new this.realMapboxgl.CRS("EPSG:4326", [-180, -90, 180, 90]),
        minZoom: 0,
        maxZoom: 24,
        zoom: 7.5, // starting zoom
        center: [104.09172, 30.67224], // 地图中心点
      });
      this.map.on("load", () => {
        // eslint-disable-next-line no-undef
        // this.map.addControl(new mapboxgl.NavigationControl(), 'top-left');
        if (this.mainModel === "0") {
          this.addPoint(false);
        }
        this.addMapMoveEvent((data) => {
          console.log("addMapMoveEvent", data);
        });
      });
    },
    // 添加点位图
    addPoint(fly) {
      if (this.sites.length < 0) return;
      this.sites.forEach((item) => {
        const el1 = document.createElement("div");
        if (item.alarmCount > 0) {
          // 红白闪烁
          el1.className = "level2";
        } else {
          el1.className = "level3";
        }
        el1.addEventListener("click", (e) => {
          this.getCameraLists({pageNo: 1, pageSize: 100});
          const node = this.findNodeById(this.treeData, item.id);
          this.$refs.detailsDialogRef.openDialog(node);
        });
        // 地图上设置图片
        const marker = new this.realMapboxgl.Marker({
          element: el1,
          offset: [0, 0],
        });
        marker
            .setLngLat([item.longitude, item.latitude])
            .addTo(this.map);

        // 为标记添加自定义的HTML元素（例如文字标签）
        el1.textContent = item.siteName + ":" + item.num + "个点位";

        el1.addEventListener('mouseenter', () => {
          clearTimeout(this.hoverTimer); // 重置已有定时器
          this.hoverTimer = setTimeout(() => {
            this.handleFiveSecondHover(item.id);
          }, 3000);
        });

        el1.addEventListener('mouseleave', () => {
          clearTimeout(this.hoverTimer);
          this.dialogVisible = false
          this.hoverTimer = null
        });

        this.endMarker.push(marker);
        if (fly) {
          this.map.flyTo({
            center: [item.longitude, item.latitude],
            essential: true,
          });
        }
      });
    },
    // 3秒悬停触发事件
    handleFiveSecondHover(e) {
      let bo = false
      if (e === '2116100') {
        this.dialogImageUrl = require('@/assets/fkt/dazhu.png')
        bo = true
      }
      if (e === '20043483') {
        this.dialogImageUrl = require('@/assets/fkt/anyue.png')
        bo = true
      }
      if (e === '20043483') {
        this.dialogImageUrl = require('@/assets/fkt/anyue.png')
        bo = true
      }
      if (e === '20014583') {
        this.dialogImageUrl = require('@/assets/fkt/suining.png')
        bo = true
      }
      if (e === '2188200') {
        this.dialogImageUrl = require('@/assets/fkt/wanzhou.png')
        bo = true
      }
      if (e === '2007500') {
        this.dialogImageUrl = require('@/assets/fkt/yinjin.png')
        bo = true
      }
      if (e === '2007800') {
        this.dialogImageUrl = require('@/assets/fkt/zhongxian.png')
        bo = true
      }
      if (e === '20046342') {
        this.dialogImageUrl = require('@/assets/fkt/jiange.png')
        bo = true
      }
      if (e === '2006500') {
        this.dialogImageUrl = require('@/assets/fkt/mojing.png')
        bo = true
      }
      if (e === '20046343') {
        this.dialogImageUrl = require('@/assets/fkt/canxiyichang.png')
        bo = true
      }
      if (bo) {
        this.dialogVisible = true
        this.getSiteList(e)
      }
    },
    // 添加地图移动缩放监听
    addMapMoveEvent(callback) {
      this.moveendEvent = (e) => {
        let data = this.getMapCenterZoom();
        if (callback) {
          callback(data);
        }
      };
      this.map.on("moveend", this.moveendEvent);
    }
    ,
    // 获取地图中心点坐标及层级
    getMapCenterZoom() {
      let center = this.map.getCenter();
      let zoom = this.map.getZoom();
      return {center, zoom};
    }
    ,
    // 获取-废水/废气实时监测
    monitorListBySite(date, siteId, monitorType) {
      getMonitorList({date: date, siteId: siteId, monitorType: monitorType}).then(res => {
        if (monitorType === 10) {
          this.tableData1 = res.data;
        }
        if (monitorType === 20) {
          this.tableData2 = res.data;
        }
      })
    }
    ,
    // 获取-废水/废气超标数据监测
    overMonitorListBySite(date, siteId, monitorType) {
      overMonitorList({date: date, vldSiteId: siteId, monitorType: monitorType}).then(res => {
        if (monitorType === 10) {
          this.tableData3 = res.data;
        }
        if (monitorType === 20) {
          this.tableData4 = res.data;
        }
      })
    }
  },
};
</script>

<style lang="less" scoped>

::v-deep .el-card {
  background: transparent;
  border: 1px solid rgb(43 141 197);
  color: #ffffff;
  width: 100%;
  margin-top: 10px;

  .el-card__header {
    //padding: 8px;
    border-color: rgb(43 141 197);
    background-color: #073aa9;

    .clearfix:after {
      display: none;
    }

    .clearfix {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span:nth-child(1) {
        font-size: 14px;
      }

      span:nth-child(2) {
        font-size: 12px;
      }
    }
  }

  .el-card__body {
    //padding: 16px;
    font-size: 12px;
    background-color: #073aa9;
  }
}

::v-deep .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav div.is-active {
  font-size: 18px;
}

::v-deep .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav div {
  font-size: 18px;
}

::v-deep .el-form-item {
  margin-bottom: 0px;
}

.environmental-overview-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  //background: rgba(0, 0, 0, 0.3);
  color: #fff;

  .wrapper {
    width: 30%;
    height: 100%;
  }

  .map-wrapper {
    flex: 1;
    margin: 0 20px;
  }

  .screen-map {
    position: absolute;
    height: 100%;
    width: 100%;
    // background-image: url(../../assets/images/test.png);
  }

  .dv-wrapper {
    height: 50%;

    h1 {
      font-size: 28px;
      line-height: 50px;
    }

    h3 {
      font-size: 18px;
      text-align: right;
      line-height: 70px;
    }
  }
}

::v-deep .el-pagination .el-select .el-input {
  margin: 0px;
}

.flex {
  display: flex;
  justify-content: space-between;
}

.number {
  display: inline-block;
  width: 120px;
  text-align: right;
  line-height: 36px;
}

.margin-bottom-small {
  margin-bottom: 10px;
}

::v-deep .el-form-item__label {
  color: #ffffff;
}

::v-deep .el-input__inner {
  color: #fff;
  background-color: #073aa9;
  border: 0;
  box-shadow: 0 0 0.05rem #86a5e7 inset;
}

::v-deep .el-tabs--border-card {
  border: none;
}

::v-deep .el-tabs--border-card > .el-tabs__header {
  background: transparent;
  border: none;
}

::v-deep .el-tabs--border-card {
  background: transparent;
}

::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  background: #073aa9;
  border: none;
}

::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  border: none;
}

::v-deep .el-tabs__nav-wrap {
  margin-bottom: 0px;
}

::v-deep .number {
  letter-spacing: 0px;
}

::v-deep .el-pagination__total {
  color: #ffffff;
}

::v-deep .el-pagination__jump {
  color: #ffffff;
}
</style>
<style lang="less" scoped>
.image-container {
  position: relative;
  width: 100%; /* 容器宽度自适应 */
  height: auto; /* 保持图片比例 */
}

.base-image {
  width: 100%;
  height: 100%;
  display: block; /* 确保图片块级显示 */
}

.overlay {
  position: absolute;
  width: 3%; /* 保持原有宽度百分比 */
  height: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* 居中定位 */
  z-index: 2; /* 确保图标在基础图上层 */
}

/* 可选：自定义图标位置 */
.icon-w {
  transform: translate(-50%, -40%); /* W图标向上偏移 */
}

.icon-a {
  transform: translate(-50%, 60%); /* A图标向下偏移 */
}


.main-background {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
  background-image: url("~@/assets/images/main-background.png");
  background-repeat: no-repeat;
  background-size: cover;
  pointer-events: none;
}

::v-deep .el-input__inner {
  box-shadow: 0 0 0.05rem transparent inset;
  background: rgba(0, 27, 64, 0.6);
}

.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  ::v-deep .el-input--small {
    border: 1px solid #00409a;
    border-radius: 5px;
  }
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;
}

.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  transition: left 0.5s;
  top: calc((100% - 89px) / 2);
}

.left-img-s {
  left: 0px;
  transition: left 0.5s;
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  transform: rotate(180deg);
  top: calc((100% - 50px) / 2);
  transition: transform 0.5s, left 0.5s;
  cursor: pointer;
}

.left-img-icon-s {
  left: -8px;
  transform: rotate(0deg);
  transition: transform 0.5s, left 0.5s;
}

.control {
  position: absolute;
  left: 406px;
  bottom: 18px;
  transition: left 0.5s;
}

.control-s {
  left: 16px;
  transition: left 0.5s;
}

.control-div {
  position: relative;
  display: flex;
  flex-direction: column;

  .img {
    margin-bottom: 4px;
    width: 28px;
    height: 28px;
    cursor: pointer;
  }

  .img1 {
    height: 57px;
    margin-bottom: 0px;
  }

  .imgClick {
    position: absolute;
    bottom: 0px;
    width: 28px;
    height: 57px;

    div {
      width: 100%;
      height: 50%;
      cursor: pointer;
    }
  }
}

.select {
  position: absolute;
  left: 414px;
  top: 22px;
  transition: left 0.5s;

  ::v-deep .el-select {
    width: 186px;
  }
}


.select1 {
  position: absolute;
  left: 510px;
  top: 22px;
  transition: left 0.5s;

  ::v-deep .el-select {
    width: 186px;
  }
}

.select-s {
  left: 24px;
  transition: left 0.5s;
}

.tree-wrapper {
  height: calc(100% - 52px);
  overflow: auto;
  margin-top: 20px;

  ::v-deep .el-input {
    border: 1px solid #00409a;
    background: rgba(0, 27, 64, 0.6);
  }

  ::v-deep .el-tree {
    background: transparent;
    color: #ffffff;
    font-size: 14px;
  }

  ::v-deep .el-tree-node__content {
    height: 30px;
    line-height: 15px;
    white-space: wrap;
  }

  ::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
    background: transparent;

    .el-tree-node__label {
      color: rgba(11, 196, 255, 1);
    }
  }

  ::v-deep .el-tree-node__content:hover {
    background-color: transparent !important;
  }

  /*树节点前面的图标修改*/

  ::v-deep .el-tree .el-tree-node__expand-icon.expanded {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  ::v-deep .el-tree .el-icon-caret-right:before {
    background: url("~@/assets/images/weizhankaiweigaoliang.png") no-repeat center center;
    content: "";
    display: block;
    width: 11px;
    height: 11px;
    font-size: 11px;
    background-size: 11px;
    transform: rotate(0deg);
    transition-property: transform;
    transition-duration: 0.3s;
  }

  ::v-deep
  .el-tree
  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
    background: url("~@/assets/images/weizhankaiweigaoliang.png") no-repeat center center;
    content: "";
    display: block;
    width: 11px;
    height: 11px;
    font-size: 11px;
    background-size: 11px;
    transform: rotate(90deg);
    transition-property: transform;
    transition-duration: 0.3s;
  }

  ::v-deep .el-tree-node__expand-icon.is-leaf::before {
    display: none;
  }

  ::v-deep .el-tree-node:focus > .el-tree-node__content {
    background: transparent !important;
  }

  ::v-deep
  .is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon::before {
    background: url("~@/assets/images/weizhankaigaoliang.png") no-repeat center center !important;
    content: "" !important;
    display: block !important;
    width: 11px !important;
    height: 11px !important;
    background-size: 11px !important;
  }

  // 展开状态
  ::v-deep
  .is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon.expanded::before {
    background: url("~@/assets/images/weizhankaigaoliang.png") no-repeat center center !important;
    content: "" !important;
    display: block !important;
    width: 11px !important;
    height: 11px !important;
    background-size: 11px !important;
  }

  ::v-deep
  .is-checked
  > .el-tree-node__content
  .el-tree-node__expand-icon::before {
    background: url("~@/assets/images/weizhankaigaoliang.png") no-repeat center center !important;
    content: "" !important;
    display: block !important;
    width: 11px !important;
    height: 11px !important;
    background-size: 11px !important;
  }

  // 展开状态
  ::v-deep
  .is-checked
  > .el-tree-node__content
  .el-tree-node__expand-icon.expanded::before {
    background: url("~@/assets/images/weizhankaigaoliang.png") no-repeat center center !important;
    content: "" !important;
    display: block !important;
    width: 11px !important;
    height: 11px !important;
    background-size: 11px !important;
  }

  // 没有子节点
  ::v-deep .is-current .el-tree-node__expand-icon.is-leaf::before {
    background: transparent !important;
    content: "" !important;
    display: block !important;
    width: 0px !important;
    height: 0px !important;
    background-size: 100% 100% !important;
  }

  ::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
    color: rgba(11, 196, 255, 1) !important;
  }
}

::v-deep .el-table {
  background: transparent;
}

::v-deep .el-table tr {
  background: transparent;
  color: white;
}

::v-deep .no-hover-table .el-table__body tr:hover > td {
  background-color: transparent !important;
}

::v-deep .el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background: transparent;
  color: white;
}

::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background: linear-gradient(to bottom,
  rgba(0, 106, 255, 0.3),
  rgba(0, 106, 255, 1));
  border-color: rgba(0, 43, 104, 0.5) !important;
  box-shadow: -1px 0 0 0 #00409a;
}

::v-deep .el-radio-button__inner {
  color: rgba(255, 255, 255, 0.75);
  background: rgba(0, 27, 64, 0.3);
  border-color: #00409a;
  font-size: 14px;
  height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

::v-deep .el-radio-button:first-child .el-radio-button__inner {
  border-color: #00409a;
}

::v-deep .el-select .el-input .el-input__inner {
  font-size: 14px;
  background: rgba(0, 27, 64, 0.3);
  box-shadow: 0 0 0.05rem rgba(0, 0, 0, 0) inset;
}

::v-deep .el-select {
  border: 1px solid #00409a;
  border-radius: 4px;
}

::v-deep .el-select .el-input--suffix .el-input__inner {
  padding-left: 8px;
}

::v-deep .el-radio-button--small .el-radio-button__inner {
  padding: 9px 8px;
}

::v-deep .el-date-editor.el-input {
  border: 1px solid #00409a;
}

::v-deep .el-date-editor .el-input__inner {
  background-color: rgba(0, 27, 64, 0.3);
  box-shadow: 0 0 0.05rem rgba(0, 0, 0, 0) inset;
}

::v-deep .el-date-editor.el-input {
  border-radius: 4px;
}

/* 隐藏浏览器自带的滚动条 */
::-webkit-scrollbar {
  width: 0; /* 调整滚动条宽度 */
  height: 0;
}

/* 隐藏滚动条，但仍可滚动 */
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  display: none; /* 针对Webkit浏览器 */
}

::v-deep .el-table__body-wrapper {
  -ms-overflow-style: none; /* 针对IE、Edge浏览器 */
  scrollbar-width: none; /* 针对Firefox浏览器 */
}

//::v-deep .el-table__body {
//  width: 500px !important;
//}

.custom-tree-node {
  display: flex;
  align-items: center;

  .gray-div {
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background: #a5a5a5;
    margin-right: 7px;
  }

  .green-div {
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background: #1afa29;
    margin-right: 7px;
  }

  img {
    width: 16px;
    height: 14px;
    margin-right: 6px;
  }
}

#echarts2 {
  padding-top: 10px;
}

.card-charts {
  height: 100% !important;
  top: 0 !important;
  right: 0 !important;
}

.card {
  position: absolute;
  top: 16px;
  right: 16px;
  pointer-events: none;

  .card-top {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
    pointer-events: fill;
  }

  .card-bottom {
    display: flex;
    pointer-events: none;
    justify-content: flex-end;

    .card-bottom-left {
      margin-right: 15px;
      pointer-events: none;

      .main {
        pointer-events: fill;
      }
    }

    .card-bottom-right {
      pointer-events: none;

      .main {
        pointer-events: fill;
      }
    }
  }

  .charts-group {
    height: 100%;
    width: 414px;
    display: flex;
    flex-direction: column !important;

    .chart-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: rgba(0, 43, 104, 0.5);

      .item-head {
        height: 34px;
        background-image: linear-gradient(45deg, #006d9c, #052761);

        .arrow {
          position: absolute;
          width: 26px;
          height: 26px;
          border-top: 4px solid #00ffff;
          border-left: 4px solid #00ffff;
        }

        .head-title {
          height: 100%;
          line-height: 34px;
          padding-left: 14px;
          font-size: 18px;
        }
      }

      .item-body {
        flex: 1;
      }
    }
  }
}



.table-jy {
  width: 100%;
  height: calc(100% - 94px);
  margin-bottom: 16px;
  position: relative;

  .table-jy-border {
    position: absolute;
    bottom: 0px;
    width: 1037px;
    border: none;
    border-bottom: 1px solid #2496ff;
  }
}

.table-jy-event {
  height: calc(100% - 44px);

  .table-jy-border {
    width: 658px;
  }
}

.content-jy {
  width: 100%;
  height: calc(100% - 51px);
  display: flex;

  #echartsJy {
    margin-right: 16px;
  }

  div {
    width: calc(50% - 8px);
  }
}

.pagination-jy {
  width: 100%;
}

.select-jy {
  position: static;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.select-jy-jy {
  justify-content: flex-start;
}

::v-deep .el-pagination {
  position: relative;
  display: flex;
  justify-content: flex-end;

  .el-select {
    height: 28px;
    box-sizing: border-box;
  }

  .el-pagination__total {
    position: absolute;
    left: 0px;
  }

  .btn-prev,
  .btn-next {
    background: transparent;
    border: 1px solid #00409a;
    padding-right: 6px;
    padding-left: 6px;
    color: rgba(255, 255, 255, 0.75);

    &:hover {
      color: #01c2ff;
    }
  }

  .el-pager li {
    background: transparent;
    color: rgba(255, 255, 255, 0.75);
    font-size: 14px;
    border: none;

    &:hover {
      color: #01c2ff;
    }
  }

  .el-pager li.active {
    color: #01c2ff;
  }
}

.slot {
  width: 53px;
  background: transparent;
  border: 1px solid #00409a;
  border-left: 0px;
  color: rgba(255, 255, 255, 0.75);
  font-size: 13px;
  font-family: Arial;
  font-weight: 400;
  cursor: pointer;
  line-height: 28px;
  height: 28px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;

  &:hover {
    color: #01c2ff;
  }
}
</style>
<style lang="less">
.imageSmallClass {
  width: 20px;
  height: 20px;
}

.imageClass {
  width: calc(50% - 2px);
  height: calc(50% - 2px);
}

.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);;
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.badge {
  background: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}


.level2 {
  background-image: url("~@/assets/hbgk-imgs/warning.png");
  width: 129px;
  height: 52px;
  background-size: cover;
  background-position: center;
  text-align: center;
  font-size: 9px;
  line-height: 1.2; /* 调整行高 */
  overflow: hidden; /* 隐藏溢出内容 */
  white-space: normal; /* 允许换行 */
  word-wrap: break-word; /* 强制长单词换行 */
  display: flex; /* 弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.level3 {
  background-image: url("~@/assets/hbgk-imgs/info.png");
  width: 129px;
  height: 52px;
  background-size: cover;
  background-position: center;
  text-align: center;
  font-size: 9px;
  line-height: 1.2; /* 调整行高 */
  overflow: hidden; /* 隐藏溢出内容 */
  white-space: normal; /* 允许换行 */
  word-wrap: break-word; /* 强制长单词换行 */
  display: flex; /* 弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.mapboxgl-popup {
  max-width: 250px;
  font-size: 16px;
}

.mapboxgl-popup-content {
  font-family: "Open Sans", sans-serif;
  padding: 20px;
}

.mapboxgl-popup-close-button {
  color: #fff;
}

.custom-popup {
  background: transparent;
}

.custom-popup .mapboxgl-popup-content {
  background-color: rgba(0, 43, 104, 0.5);
  position: absolute;
  transform: translateX(-50%);
  white-space: nowrap;
}

.jyPicker {
  .el-input__inner {
    background-color: rgba(0, 27, 64, 0.3) !important;
    border-color: #00409a;
    color: #ffffff;
  }

  .el-picker-panel__footer {
    background: #073aa9;
  }
}

.preview-image {
  position: absolute;
  left: 30%;
  width: 300px;
  height: auto;
  padding: 5px;
  top: 30%;
  background: white;
  border: 1px solid #ddd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.demo-form-inline {
  width: 100%;
  .el-form-item:last-child {
    margin-right: 0px;
    float: right;
  }
}
</style>
