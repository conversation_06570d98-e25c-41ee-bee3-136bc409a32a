<template>
  <div class="environmental-alarm-page">
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <a-input
        v-model:value="filterText"
        class="search"
        placeholder="输入关键词"
        allow-clear
        size="small"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="tree-wrapper">
        <a-tree
          ref="monitorList"
          :tree-data="treeData as any"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'vldSiteId',
          }"
          :show-line="false"
          :show-icon="false"
          @select="handleNodeClick as any"
        />
      </div>
    </div>

    <img
      src="@/assets/images/shouqi.png"
      alt=""
      :class="['left-img', isCollapsed ? 'left-img-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <img
      src="@/assets/images/shouqiicon.png"
      alt=""
      :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />

    <!-- 菜单图标 -->
    <div
      ref="menu"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>

    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog />
    </div>

    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <a-tabs v-model:active-key="activeName" @change="handleClick as any">
        <a-tab-pane key="0" tab="废水在线监测数据" />
        <a-tab-pane key="1" tab="废气在线监测数据" />
      </a-tabs>

      <a-form :model="formData" layout="inline" class="demo-form-inline">
        <a-form-item label="监测时间:">
          <a-range-picker
            v-model:value="formData.timeArr as any"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD HH:mm:ss"
          />
        </a-form-item>
        <a-form-item class="ant-form-item-bottom">
          <a-space>
            <a-button type="primary" @click="search">查询</a-button>
            <a-button @click="resetForm">重置</a-button>
            <a-button type="primary" @click="exportXlsx">数据导出</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <div class="table-scrollbar">
        <div class="table-wrapper">
          <a-table
            v-if="name === '0'"
            :columns="waterColumns"
            :data-source="tableData"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }: any">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-if="column.key === 'totalEmission'">
                {{
                  sumWithTwoDecimals(record.cwater011All, record.cwater060All)
                }}
              </template>
            </template>
          </a-table>

          <a-table
            v-if="name === '1'"
            :columns="gasColumns"
            :data-source="gasData"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
            </template>
          </a-table>
        </div>
        <a-pagination
          v-model:current="formData.pageNum"
          v-model:page-size="formData.pageSize"
          :total="total"
          :show-size-changer="true"
          :page-size-options="['20', '40', '60', '80', '100']"
          show-quick-jumper
          style="text-align: right; margin-top: 16px"
          @change="handleCurrentChange"
          @show-size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 单位选择弹框 -->
    <a-modal v-model:open="unitDialog" title="选择单位" width="400px">
      <div class="unit">
        <a-tree
          :tree-data="treeData as any"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'id',
          }"
          :show-line="false"
          :show-icon="false"
          default-expand-all
          @select="checkUnit as any"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import type { TableColumnsType } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';
import { getTreeNode } from '@/http/environmental-overview';
import { getWaterPage, geGasPage } from '@/http/monitoring-data-query';
import { exportJsonToExcel } from '@/excel/Export2Excel.js';
import menuDialog from '@/components/menu.vue';

// 类型定义
interface TreeNode {
  id: string;
  vldSiteId: string;
  siteShortName: string;
  children?: TreeNode[];
}

interface FormData {
  timeArr: [Dayjs, Dayjs] | null;
  unit: string;
  mBasicMonitorTbId: string;
  siteId: string;
  pageNum: number;
  pageSize: number;
}

interface WaterData {
  site: string;
  monitorName: string;
  mTime: string;
  cwater011All: number;
  cwater060All: number;
  cwater011: number;
  cwater060: number;
  cwater001: number;
  flag: string;
}

interface GasData {
  site: string;
  monitorName: string;
  mTime: string;
  cair02R: number;
  cair02: number;
  cair03R: number;
  cair03: number;
  cair03All: number;
  cairS03: number;
  cairS08: number;
  cairS05: number;
  cairS01: number;
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const filterText = ref('');
const isCollapsed = ref(false);
const name = ref('0');
const activeName = ref('0');
const total = ref(0);
const unitDialog = ref(false);
const monitorList = ref();

const formData = reactive<FormData>({
  timeArr: null,
  unit: '',
  mBasicMonitorTbId: '',
  siteId: '',
  pageNum: 1,
  pageSize: 20,
});

const tableData = ref<WaterData[]>([]);
const gasData = ref<GasData[]>([]);
const treeData = ref<TreeNode[]>([]);

// 表格列定义
const waterColumns: TableColumnsType = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  {
    title: '单位',
    dataIndex: 'site',
    key: 'site',
    align: 'center',
    width: 180,
  },
  {
    title: '监测点',
    dataIndex: 'monitorName',
    key: 'monitorName',
    align: 'center',
    width: 180,
  },
  {
    title: '监测时间',
    dataIndex: 'mTime',
    key: 'mTime',
    align: 'center',
    width: 180,
  },
  { title: '累计排放量', key: 'totalEmission', align: 'center', width: 120 },
  { title: 'COD', dataIndex: 'cwater011', key: 'cwater011', align: 'center' },
  {
    title: 'COD排放量',
    dataIndex: 'cwater011All',
    key: 'cwater011All',
    align: 'center',
    width: 120,
  },
  { title: '氨氮', dataIndex: 'cwater060', key: 'cwater060', align: 'center' },
  {
    title: '氨氮排放量',
    dataIndex: 'cwater060All',
    key: 'cwater060All',
    align: 'center',
    width: 120,
  },
  { title: 'PH', dataIndex: 'cwater001', key: 'cwater001', align: 'center' },
  { title: '数据状态', dataIndex: 'flag', key: 'flag', align: 'center' },
];

const gasColumns: TableColumnsType = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  {
    title: '单位',
    dataIndex: 'site',
    key: 'site',
    align: 'center',
    width: 180,
  },
  {
    title: '监测点',
    dataIndex: 'monitorName',
    key: 'monitorName',
    align: 'center',
    width: 180,
  },
  {
    title: '监测时间',
    dataIndex: 'mTime',
    key: 'mTime',
    align: 'center',
    width: 180,
  },
  { title: 'SO₂实测', dataIndex: 'cair02R', key: 'cair02R', align: 'center' },
  { title: 'SO₂折算', dataIndex: 'cair02', key: 'cair02', align: 'center' },
  { title: 'NOX实测', dataIndex: 'cair03R', key: 'cair03R', align: 'center' },
  { title: 'NOX折算', dataIndex: 'cair03', key: 'cair03', align: 'center' },
  {
    title: 'NOX排放量',
    dataIndex: 'cair03All',
    key: 'cair03All',
    align: 'center',
    width: 120,
  },
  { title: '温度', dataIndex: 'cairS03', key: 'cairS03', align: 'center' },
  { title: '压力', dataIndex: 'cairS08', key: 'cairS08', align: 'center' },
  { title: '湿度', dataIndex: 'cairS05', key: 'cairS05', align: 'center' },
  { title: '氧含量', dataIndex: 'cairS01', key: 'cairS01', align: 'center' },
];

// 方法定义
const sumWithTwoDecimals = (value1: number, value2: number): string => {
  const num1 = parseFloat(String(value1)) || 0;
  const num2 = parseFloat(String(value2)) || 0;
  const sum = num1 + num2;
  return sum.toFixed(2);
};

const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const menu = (e.target as HTMLElement)
    ?.closest('.draggable-menu')
    ?.getBoundingClientRect();
  if (!menu) return;

  const onMouseMove = (e: MouseEvent) => {
    const newX = e.clientX - menu.width / 2;
    const newY = e.clientY - menu.height / 2;

    x.value = Math.max(0, Math.min(newX, window.innerWidth - menu.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menu.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const handleNodeClick = (selectedKeys: string[], info: any) => {
  if (selectedKeys.length > 0) {
    formData.siteId = selectedKeys[0];
    search();
  }
};

const search = () => {
  const form: any = {
    startTime: '',
    endTime: '',
    mBasicMonitorTbId: formData.mBasicMonitorTbId,
    pageNum: formData.pageNum,
    pageSize: formData.pageSize,
    siteId: formData.siteId,
  };

  if (formData.timeArr) {
    form.startTime = formData.timeArr[0].format('YYYY-MM-DD HH:mm:00');
    form.endTime = formData.timeArr[1].format('YYYY-MM-DD 23:59:59');
  }

  name.value === '0' ? getTableData(form) : getGasDataFunc(form);
};

const handleClick = (key: string) => {
  Object.assign(formData, {
    timeArr: null,
    unit: '',
    mBasicMonitorTbId: '',
    pageNum: 1,
    pageSize: 20,
  });
  name.value = key;
  search();
};

const getTableData = async (form: any) => {
  try {
    const res = await getWaterPage(form);
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    message.error('获取废水数据失败');
  }
};

const getGasDataFunc = async (form: any) => {
  try {
    const res = await geGasPage(form);
    gasData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    message.error('获取废气数据失败');
  }
};

const handleSizeChange = (current: number, size: number) => {
  formData.pageSize = size;
  search();
};

const handleCurrentChange = (page: number) => {
  formData.pageNum = page;
  search();
};

const resetForm = () => {
  Object.assign(formData, {
    timeArr: null,
    unit: '',
    mBasicMonitorTbId: '',
    pageNum: 1,
    pageSize: 20,
  });
  search();
};

const selectUnit = () => {
  unitDialog.value = true;
};

const checkUnit = (selectedKeys: string[], info: any) => {
  if (selectedKeys.length > 0 && info.selectedNodes.length > 0) {
    const node = info.selectedNodes[0];
    formData.unit = node.siteShortName;
    formData.mBasicMonitorTbId = node.id;
    unitDialog.value = false;
  }
};

const getTreeNanfang = async () => {
  try {
    const res = await getTreeNode();
    treeData.value = res.data;
  } catch (error) {
    message.error('获取组织机构数据失败');
  }
};

const exportXlsx = () => {
  Modal.confirm({
    title: '提示',
    content: '确认导出当前页数据吗?',
    onOk() {
      let fieldName: string[];
      let filterVal: string[];
      let dataList: any[];
      let fileTilte: string;

      if (name.value === '0') {
        dataList = tableData.value.map(item => ({
          ...item,
          sumCount: sumWithTwoDecimals(item.cwater011All, item.cwater060All),
        }));
        fileTilte = '废水列表';
        fieldName = [
          '单位',
          '监测点',
          '监测时间',
          '累计排放量',
          'COD',
          'COD排放量',
          '氨氮',
          '氨氮排放量',
          'PH',
          '数据状态',
        ];
        filterVal = [
          'site',
          'monitorName',
          'mTime',
          'sumCount',
          'cwater011',
          'cwater011All',
          'cwater060',
          'cwater060All',
          'cwater001',
          'flag',
        ];
      } else {
        dataList = gasData.value;
        fileTilte = '废气列表';
        fieldName = [
          '单位',
          '监测点',
          '监测时间',
          'SO₂实测',
          'SO₂折算',
          'NOX实测',
          'NOX折算',
          'NOX排放量',
          '温度',
          '压力',
          '湿度',
          '氧含量',
        ];
        filterVal = [
          'site',
          'monitorName',
          'mTime',
          'cair02R',
          'cair02',
          'cair03R',
          'cair03',
          'cair03All',
          'cairS03',
          'cairS08',
          'cairS05',
          'cairS01',
        ];
      }

      const data = dataList.map(v => filterVal.map(j => (v as any)[j]));
      exportJsonToExcel(fieldName, data, fileTilte);
    },
  });
};

// 监听器
watch(filterText, val => {
  // 这里需要实现树的过滤功能
  console.log('Filter text:', val);
});

onMounted(() => {
  search();
  getTreeNanfang();
});
</script>

<style lang="less" scoped>
//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;
}

.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-body-s {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  transition: left 0.5s;
  top: calc((100% - 89px) / 2);
}

.left-img-s {
  left: 0px;
  transition: left 0.5s;
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  transform: rotate(180deg);
  top: calc((100% - 50px) / 2);
  transition: transform 0.5s, left 0.5s;
  cursor: pointer;
}

.left-img-icon-s {
  left: -8px;
  transform: rotate(0deg);
  transition: transform 0.5s, left 0.5s;
}

.tree-wrapper {
  height: calc(100% - 52px);
  overflow: auto;
  margin-top: 20px;
}

.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  color: #ffffff;
  display: block;

  .main {
    overflow: hidden;
    height: 100%;
  }

  .main:nth-child(1) {
    width: 350px;
  }

  .main:nth-child(2) {
    width: calc(100% - 366px);
  }
}

.demo-form-inline {
  margin-top: 16px;
}

::v-deep .number {
  letter-spacing: 0px;
}

div::-webkit-scrollbar {
  display: none;
}
.table-scrollbar {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}
// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
