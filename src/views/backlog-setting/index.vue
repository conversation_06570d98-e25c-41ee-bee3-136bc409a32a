<template>
  <div class="environmental-alarm-page">
    <!-- 菜单图标 -->
    <div
      ref="menuRef"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>
    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog></menuDialog>
    </div>
    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <div class="search">
        <a-tabs
          v-model:active-key="activeName"
          style="margin-bottom: 20px"
          @change="handleClick"
        >
          <a-tab-pane key="0" tab="超标数据推送配置" />
          <a-tab-pane key="1" tab="待办消息提醒配置" />
          <a-tab-pane key="2" tab="超标数据统计推送配置" />
        </a-tabs>
        <div class="content1">
          <a-form
            ref="formRef"
            layout="inline"
            :model="formData"
            class="demo-form-inline"
          >
            <a-form-item v-if="name === '0'" label="推送人员类型:">
              <a-select
                v-model:value="formData.levelType"
                placeholder="请选择"
                @change="selectHandle"
              >
                <a-select-option value="1">基层人员</a-select-option>
                <a-select-option value="2">二级单位</a-select-option>
                <a-select-option value="3">一级单位</a-select-option>
                <a-select-option value="4">处级领导</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item v-if="name === '1'" label="推送类型:">
              <a-select
                v-model:value="formData.pushClass"
                placeholder="请选择"
                @change="selectHandle"
              >
                <a-select-option value="1">方案待办</a-select-option>
                <a-select-option value="2">超标整改</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="用户姓名：">
              <a-input
                v-model:value="formData.userName"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="单位：">
              <a-input
                v-model:value="formData.userUnit"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="推送状态:">
              <a-select
                v-model:value="formData.pushType"
                placeholder="请选择"
                allow-clear
              >
                <a-select-option value="0">启用</a-select-option>
                <a-select-option value="1">停用</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ant-form-item-bottom">
              <a-space>
                <a-button type="primary" @click="search"> 查询 </a-button>
                <a-button @click="resetForm">重置</a-button>
                <a-button type="primary" @click="addUserHandle">
                  添加用户
                </a-button>
                <a-button type="primary" @click="batchDeleteHandle">
                  批量删除
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </div>
      <div style="text-align: right">
        <span
          v-if="name === '0'"
          style="text-align: left; float: left; font-size: 14px"
        >
          推送规则：{{ content }}
          <a-button
            type="link"
            style="margin-left: 5px"
            @click="updateConfigHandle"
          >
            点击修改
          </a-button>
        </span>
        <span
          v-if="name === '1'"
          style="text-align: left; float: left; font-size: 14px"
        >
          推送规则：{{ content }}
          <a-button
            type="link"
            style="margin-left: 5px"
            @click="updateConfigHandle"
          >
            点击修改
          </a-button>
        </span>
        <span
          v-if="name === '2'"
          style="text-align: left; float: left; font-size: 14px"
        >
          推送规则：{{ content }}
          <a-button
            type="link"
            style="margin-left: 5px"
            @click="updateConfigHandle"
          >
            点击修改
          </a-button>
        </span>
      </div>
      <div class="table-scrollbar">
        <div class="table-wrapper">
          <a-table
            v-if="name === '0'"
            :data-source="tableData1"
            :columns="table1Columns"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            :row-selection="rowSelection"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'pushType'">
                <a-switch
                  :checked="record.pushType === 0"
                  @change="(checked: any) => switchHandle(index, record, checked)"
                />
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="link"
                  size="small"
                  @click="deleteHandle(record)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
          <a-table
            v-if="name === '1'"
            :data-source="tableData2"
            :columns="table2Columns"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            :row-selection="rowSelection"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'pushType'">
                <a-switch
                  :checked="record.pushType === 0"
                  @change="(checked: boolean) => switchHandle(index, record, checked)"
                />
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="link"
                  size="small"
                  @click="deleteHandle(record)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
          <a-table
            v-if="name === '2'"
            :data-source="tableData3"
            :columns="table3Columns"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            :row-selection="rowSelection"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'pushType'">
                <a-switch
                  :checked="record.pushType === 0"
                  @change="(checked: boolean) => switchHandle(index, record, checked)"
                />
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="link"
                  size="small"
                  @click="deleteHandle(record)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
        <a-pagination
          v-model:current="formData.pageNum"
          v-model:page-size="formData.pageSize"
          :total="total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :page-size-options="['20', '40', '60', '80', '100']"
          :show-total="(total: number) => `共 ${total} 条`"
          style="margin-top: 16px; text-align: right"
          @change="onPageChange"
        />
      </div>
    </div>

    <!-- 新增用户弹框 -->
    <a-modal
      v-model:open="addDialog"
      title="添加用户"
      :footer="null"
      width="50%"
    >
      <a-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        :label-col="{ span: 6 }"
        style="width: 90%; margin-top: 20px"
      >
        <a-form-item name="userName" label="用户姓名:">
          <a-input
            v-model:value="userForm.userName"
            placeholder="请输入用户姓名"
            allow-clear
          />
        </a-form-item>
        <a-form-item name="userUnitId" label="单位名称:">
          <a-row>
            <a-col :span="20">
              <a-input
                v-model:value="userForm.userUnit"
                placeholder="请选择单位"
                readonly
                allow-clear
              />
            </a-col>
            <a-col :span="4" style="text-align: right">
              <a-button type="link" @click="selectUnit"> 选择单位 </a-button>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item name="userPhone" label="联系方式:">
          <a-input
            v-model:value="userForm.userPhone"
            placeholder="请输入联系方式"
            allow-clear
          />
        </a-form-item>
        <a-form-item name="userPushName" label="协同账号:">
          <a-input
            v-model:value="userForm.userPushName"
            placeholder="请输入协同账号"
            allow-clear
          />
        </a-form-item>
        <a-form-item v-if="name === '0'" name="levelType" label="推送人员类型:">
          <a-select
            v-model:value="userForm.levelType"
            placeholder="请选择推送人员类型"
            allow-clear
          >
            <a-select-option value="1">基层人员</a-select-option>
            <a-select-option value="2">二级单位</a-select-option>
            <a-select-option value="3">一级单位</a-select-option>
            <a-select-option value="4">处级领导</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="name === '1'" name="pushClass" label="推送类型:">
          <a-select
            v-model:value="userForm.pushClass"
            placeholder="请选择推送类型"
            allow-clear
          >
            <a-select-option value="1">方案待办</a-select-option>
            <a-select-option value="2">超标整改</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="imUserName" label="即时通讯账号:">
          <a-input
            v-model:value="userForm.imUserName"
            placeholder="请输入即时通讯账号"
            allow-clear
          />
        </a-form-item>
      </a-form>
      <div style="text-align: center; margin-top: 20px">
        <a-space>
          <a-button @click="addDialog = false">取 消</a-button>
          <a-button type="primary" @click="addOkHandle">确 定</a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 推送规则设置弹框 -->
    <a-modal
      v-model:open="settingDialog"
      title="推送规则"
      :footer="null"
      width="40%"
    >
      <a-form
        ref="configFormRef"
        :model="configForm"
        :rules="configFormRules"
        :label-col="{ span: 0 }"
      >
        <a-form-item v-if="name === '0'">
          <a-radio-group v-model:value="configForm.pushType">
            <div style="margin-top: 20px">
              <a-radio :value="0" @change="changeRadio"> 即时推送 </a-radio>
            </div>
            <div style="margin-top: 20px">
              <a-radio :value="1" @change="changeRadio">
                <a-input-number
                  v-model:value="configForm.min"
                  style="width: 100px"
                  placeholder="请输入分钟"
                  :min="0"
                />
                分钟监测还超标时推送
              </a-radio>
            </div>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="name === '1'">
          <div style="color: #ffffff; margin-top: 20px">
            <a-radio-group
              v-if="formData.pushClass === '2'"
              v-model:value="configForm.pushType"
            >
              <a-radio :value="0" @change="changeRadio"> 即时推送 </a-radio>
            </a-radio-group>
            <div v-if="formData.pushClass === '1'">
              每月
              <a-select
                v-model:value="configForm.day"
                placeholder="请选择"
                style="width: 100px"
              >
                <a-select-option v-for="item in days" :key="item" :value="item">
                  {{ item }}日
                </a-select-option>
              </a-select>
              <a-time-picker
                v-model:value="configForm.time"
                placeholder="任意时间点"
                format="HH:mm:ss"
              />
            </div>
          </div>
        </a-form-item>

        <a-form-item v-if="name === '2'">
          <div style="color: #ffffff; margin-top: 20px">
            <a-select
              v-model:value="configForm.cycleType"
              placeholder="请选择"
              style="width: 100px"
              @change="changeCycleHandle"
            >
              <a-select-option :value="0">每月</a-select-option>
              <a-select-option :value="1">每日</a-select-option>
            </a-select>
            <!-- 每月 -->
            <a-select
              v-if="configForm.cycleType === 0"
              v-model:value="configForm.day1"
              placeholder="请选择"
              style="width: 100px"
              @change="changeCycleHandle1"
            >
              <a-select-option v-for="item in days" :key="item" :value="item">
                {{ item }}日
              </a-select-option>
            </a-select>
            <a-select
              v-if="configForm.cycleType === 1"
              v-model:value="configForm.day2"
              placeholder="请选择"
              style="width: 100px"
              @change="changeCycleHandle2"
            >
              <a-select-option
                v-for="(item, index) in weeks"
                :key="index"
                :value="String(index)"
              >
                {{ item }}
              </a-select-option>
            </a-select>
            <a-time-picker
              v-model:value="configForm.time"
              placeholder="任意时间点"
              format="HH:mm:ss"
            />
          </div>
        </a-form-item>
      </a-form>
      <div style="text-align: center; margin-top: 20px">
        <a-space>
          <a-button @click="settingDialog = false">取 消</a-button>
          <a-button type="primary" @click="okHandle">确 定</a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 单位选择弹框 -->
    <a-modal
      v-model:open="unitDialog"
      title="选择单位"
      :footer="null"
      width="400px"
    >
      <div class="unit">
        <a-tree
          :tree-data="treeData"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'id',
          }"
          :show-line="false"
          :show-icon="false"
          default-expand-all
          @select="checkUnit"
        />
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { MenuOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import type { TableColumnProps, FormInstance } from 'ant-design-vue';
import type { Key } from 'ant-design-vue/es/_util/type';
import dayjs, { type Dayjs } from 'dayjs';
import {
  pushConfigInfo,
  addOrUpdateConfig,
  pushUserPage,
  addOrUpdatePushUser,
  updateStartType,
  deleteUser,
} from '@/http/backlog';
import { getTreeNode } from '@/http/environmental-overview';
import { getTreeNodesWithoutOutlets } from '@/http/monitor';
import menuDialog from '@/components/menu.vue';

// 接口定义
interface FormData {
  pushClass: string;
  levelType: string;
  userName: string;
  userUnit: string;
  pushType: string;
  type: string;
  pageNum: number;
  pageSize: number;
}

interface UserForm {
  userName: string;
  userUnitId: string;
  userUnit: string;
  userPhone: string;
  userPushName: string;
  levelType: string;
  imUserName: string;
  pushClass: string;
  type?: string;
}

interface ConfigForm {
  id: string | null;
  cycleType: number | null;
  pushType: number;
  min: number;
  day: string | null;
  day1: string | null;
  day2: string | null;
  time: Dayjs | null;
  levelType: string;
  pushClass?: string;
  type?: string;
  pushDate?: string;
}

interface TreeNode {
  id: string;
  siteShortName: string;
  sitePathName?: string;
  children?: TreeNode[];
}

interface TableRecord {
  id: string;
  userName: string;
  userUnit: string;
  userPhone: string;
  userPushName: string;
  imUserName: string;
  pushType: number;
  [key: string]: any;
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const isCollapsed = ref(true);

const days = [
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
  '24',
  '25',
  '26',
  '27',
  '28',
  '29',
  '30',
  '31',
];
const weeks = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

const selectedRowKeys = ref<Key[]>([]);
const unitDialog = ref(false);
const name = ref('0');
const activeName = ref('0');

const formData = ref<FormData>({
  pushClass: '1',
  levelType: '1',
  userName: '',
  userUnit: '',
  pushType: '',
  type: '',
  pageNum: 1,
  pageSize: 20,
});

const total = ref(0);
const addDialog = ref(false);
const settingDialog = ref(false);

const configForm = ref<ConfigForm>({
  id: null,
  cycleType: null,
  pushType: 0,
  min: 0,
  day: null,
  day1: null,
  day2: null,
  time: null,
  levelType: '',
});

const configFormRules = {
  min: [{ required: true, message: '请输入分数', trigger: 'blur' }],
};

const userForm = ref<UserForm>({
  userName: '',
  userUnitId: '',
  userUnit: '',
  userPhone: '',
  userPushName: '',
  levelType: '',
  imUserName: '',
  pushClass: '',
});

const userFormRules = {
  userName: [{ required: true, message: '请输入用户姓名', trigger: 'blur' }],
  userUnitId: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
  userPhone: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
  userPushName: [
    { required: true, message: '请输入协同账号', trigger: 'blur' },
  ],
  levelType: [{ required: true, message: '请输入协同账号', trigger: 'blur' }],
  imUserName: [
    { required: true, message: '请输入即时通讯账号', trigger: 'blur' },
  ],
  pushClass: [{ required: true, message: '请选择推送类型', trigger: 'blur' }],
};

const treeData = ref<TreeNode[]>([]);
const tableData1 = ref<TableRecord[]>([]);
const tableData2 = ref<TableRecord[]>([]);
const tableData3 = ref<TableRecord[]>([]);
const content = ref('');

// 表单引用
const formRef = ref<FormInstance>();
const userFormRef = ref<FormInstance>();
const configFormRef = ref<FormInstance>();
const menuRef = ref<HTMLElement>();

// 表格列定义
const baseColumns: TableColumnProps[] = [
  { title: '序号', key: 'index', width: 80, align: 'center', fixed: 'left' },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
    align: 'center',
  },
  {
    title: '单位名称',
    dataIndex: 'userUnit',
    key: 'userUnit',
    align: 'center',
  },
  {
    title: '联系方式',
    dataIndex: 'userPhone',
    key: 'userPhone',
    width: 120,
    align: 'center',
  },
  {
    title: '协同账号',
    dataIndex: 'userPushName',
    key: 'userPushName',
    width: 120,
    align: 'center',
  },
  {
    title: '即时通讯账号',
    dataIndex: 'imUserName',
    key: 'imUserName',
    width: 120,
    align: 'center',
  },
  {
    title: '推送状态',
    key: 'pushType',
    width: 80,
    align: 'center',
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    align: 'center',
    fixed: 'right',
  },
];

const table1Columns = computed(() => baseColumns);
const table2Columns = computed(() => baseColumns);
const table3Columns = computed(() => baseColumns);

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: Key[]) => {
    selectedRowKeys.value = keys;
  },
}));

// 方法
const iconHandle = (): void => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent): void => {
  isDragging.value = true;
  const menu = menuRef.value;
  if (!menu) return;

  const menuRect = menu.getBoundingClientRect();

  const onMouseMove = (e: MouseEvent): void => {
    const newX = e.clientX - menuRect.width / 2;
    const newY = e.clientY - menuRect.height / 2;

    x.value = Math.max(0, Math.min(newX, window.innerWidth - menuRect.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menuRect.height));
  };

  const onMouseUp = (): void => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

// 获取组织机构
const getTreeNanfang = async (): Promise<void> => {
  try {
    const res = await getTreeNode();
    treeData.value = res.data;
  } catch (error) {
    console.error('获取树形数据失败:', error);
  }
};

// 单选框选择
const changeRadio = (): void => {
  if (configForm.value.pushType === 0) {
    configForm.value.min = 0;
  }
};

// 修改配置
const updateConfigHandle = (): void => {
  settingDialog.value = true;
  // 查询数据
  pushConfigInfo({
    levelType: formData.value.levelType,
    type: name.value,
    pushClass: formData.value.pushClass,
  }).then((res: any) => {
    if (res.data !== null && res.data !== undefined) {
      configForm.value = { ...res.data };
      if (res.data.pushDate !== null && res.data.pushDate !== undefined) {
        const json = res.data.pushDate.split('-');
        configForm.value.day = json[0];
        configForm.value.time = dayjs(json[1], 'HH:mm:ss');
        if (name.value === '2') {
          if (configForm.value.cycleType === 0) {
            configForm.value.day1 = configForm.value.day;
          } else {
            configForm.value.day2 = configForm.value.day;
          }
        }
      }
    } else {
      configForm.value = {
        id: null,
        pushType: 0,
        min: 0,
        day: null,
        day1: null,
        day2: null,
        time: null,
        cycleType: null,
        levelType: formData.value.levelType,
      };
    }
    configForm.value.levelType = formData.value.levelType;
    configForm.value.pushClass = formData.value.pushClass;
  });
};

// 保存配置
const okHandle = (): void => {
  configForm.value.type = name.value;
  if (configForm.value.day !== null && configForm.value.time !== null) {
    if (name.value === '1') {
      configForm.value.pushType = 0;
    }
    if (name.value === '2') {
      if (configForm.value.cycleType === 0) {
        configForm.value.day = configForm.value.day1;
      } else {
        configForm.value.day = configForm.value.day2;
      }
    }
    configForm.value.pushDate =
      configForm.value.day + '-' + configForm.value.time?.format('HH:mm:ss');
  }

  configFormRef.value
    ?.validate()
    .then(() => {
      const submitData = {
        ...configForm.value,
        time: configForm.value.time?.format('HH:mm:ss'),
      };

      addOrUpdateConfig(submitData).then((res: any) => {
        if (res.code === 200) {
          message.success(res.msg);
          settingDialog.value = false;
          getPushInfo();
        } else {
          message.error(res.msg);
        }
      });
    })
    .catch(() => {
      // 验证失败
    });
};

// 新增用户弹窗
const addUserHandle = (): void => {
  addDialog.value = true;
  getTreeNanfang();
  userForm.value = {
    userName: '',
    userUnitId: '',
    userUnit: '',
    userPhone: '',
    userPushName: '',
    levelType: '',
    imUserName: '',
    pushClass: '',
  };
};

// 保存用户
const addOkHandle = (): void => {
  userFormRef.value
    ?.validate()
    .then(() => {
      const submitData = {
        ...userForm.value,
        type: name.value,
      };

      addOrUpdatePushUser(submitData).then((res: any) => {
        if (res.code === 200) {
          message.success('新增成功!');
          search();
          addDialog.value = false;
        } else {
          message.error(res.msg);
        }
      });
    })
    .catch(() => {
      // 验证失败
    });
};

// 查询
const search = (): void => {
  const form = {
    levelType: formData.value.levelType,
    userName: formData.value.userName,
    userUnit: formData.value.userUnit,
    pushType: formData.value.pushType,
    pushClass: formData.value.pushClass,
    type: name.value,
    pageNum: formData.value.pageNum,
    pageSize: formData.value.pageSize,
  };

  if (name.value === '0') {
    getTableData1(form);
  } else if (name.value === '1') {
    getTableData2(form);
  } else if (name.value === '2') {
    getTableData3(form);
  }
};

// tabs切换
const handleClick = (key: Key): void => {
  selectedRowKeys.value = [];
  formData.value = {
    pushClass: '1',
    levelType: '1',
    userName: '',
    userUnit: '',
    pushType: '',
    type: '',
    pageNum: 1,
    pageSize: 20,
  };
  name.value = String(key);
  search();
  getPushInfo();
};

// 超标数据推送配置
const getTableData1 = (form: any): void => {
  pushUserPage(form).then((res: any) => {
    tableData1.value = res.data.records;
    total.value = res.data.total;
  });
};

// 切换
const switchHandle = (
  editIndex: number,
  record: any,
  checked: boolean
): void => {
  record.pushType = checked ? 0 : 1;
  updateStartType({ id: record.id, pushType: record.pushType }).then(
    (res: any) => {
      if (res.code === 200) {
        tableData1.value.splice(editIndex, 1, record);
      }
    }
  );
};

// 批量删除
const batchDeleteHandle = (): void => {
  if (selectedRowKeys.value.length === 0) {
    message.info('请选择删除对象');
    return;
  }
  deleteHandle({
    id: '',
    userName: '',
    userUnit: '',
    userPhone: '',
    userPushName: '',
    imUserName: '',
    pushType: 0,
  } as TableRecord);
};

// 删除
const deleteHandle = (row: any): void => {
  const userIds = row.id || selectedRowKeys.value.join(',');
  Modal.confirm({
    title: '提示',
    content: '是否确认删除选中的数据项?',
    onOk: () => {
      deleteUser(userIds).then((res: any) => {
        if (res.code === 200) {
          message.success('删除成功!');
          search();
        }
      });
    },
    onCancel: () => {
      message.info('已取消删除');
    },
  });
};

// 待办提醒推送配置
const getTableData2 = (form: any): void => {
  pushUserPage(form).then((res: any) => {
    tableData2.value = res.data.records;
    total.value = res.data.total;
  });
};

// 待办提醒推送配置
const getTableData3 = (form: any): void => {
  pushUserPage(form).then((res: any) => {
    tableData3.value = res.data.records;
    total.value = res.data.total;
  });
};

// 分页
const onPageChange = (page: number, size: number): void => {
  formData.value.pageNum = page || 1;
  formData.value.pageSize = size;
  search();
};

// 重置表单
const resetForm = (): void => {
  formData.value = {
    pushClass: '1',
    levelType: '1',
    userName: '',
    userUnit: '',
    pushType: '',
    type: '',
    pageNum: 1,
    pageSize: 20,
  };
  search();
};

// 选择单位
const checkUnit = (selectedKeys: Key[], info: any): void => {
  const node = info.node;
  if (node.sitePathName === null) {
    userForm.value.userUnit = node.siteShortName;
  } else {
    userForm.value.userUnit = node.sitePathName;
  }
  userForm.value.userUnitId = node.id;
  unitDialog.value = false;
};

// 单位弹框
const selectUnit = (): void => {
  if (Number(activeName.value) === 0 || Number(activeName.value) === 2) {
    getTreeNanfang();
  } else {
    getTreeNodesWithoutOutlets().then((res: any) => {
      treeData.value = res.data;
    });
  }
  unitDialog.value = true;
};

// 获取配置
const getPushInfo = (): void => {
  pushConfigInfo({
    levelType: formData.value.levelType,
    type: name.value,
    pushClass: formData.value.pushClass,
  }).then((res: any) => {
    if (res.data === null || res.data === undefined) {
      content.value = '-';
      return;
    }

    if (name.value === '0') {
      if (res.data.min !== null && res.data.min !== undefined) {
        if (res.data.pushType === 1) {
          content.value = res.data.min + '分钟监测还超标时推送';
        } else {
          content.value = '即时推送';
        }
      } else {
        content.value = '-';
      }
    } else if (name.value === '1') {
      if (
        res.data.pushDate !== null &&
        res.data.pushDate !== undefined &&
        res.data.pushClass === '1'
      ) {
        const json = res.data.pushDate.split('-');
        const day = json[0];
        const time = json[1];
        content.value = day + '日' + time + '推送';
      } else {
        content.value = '-';
      }

      if (res.data.pushClass != null && res.data.pushClass === '2') {
        content.value = '即时推送';
      }
    } else if (name.value === '2') {
      if (res.data.pushDate !== null && res.data.pushDate !== undefined) {
        const json = res.data.pushDate.split('-');
        let contentText;
        if (res.data.cycleType === 0) {
          contentText = '每月' + json[0] + '日' + json[1] + '推送';
        } else {
          contentText = '每周' + weeks[Number(json[0])] + json[1] + '推送';
        }
        content.value = contentText;
      } else {
        content.value = '-';
      }
    }
  });
};

// 选择推送人员类型
const selectHandle = (): void => {
  getPushInfo();
};

const changeCycleHandle = (): void => {
  configForm.value.day = null;
};

const changeCycleHandle1 = (val: string): void => {
  configForm.value.day1 = val;
  configForm.value.day = configForm.value.day1;
};

const changeCycleHandle2 = (val: string): void => {
  configForm.value.day2 = val;
  configForm.value.day = configForm.value.day2;
};

// 生命周期
onMounted(() => {
  search();
  getPushInfo();
});
</script>

<style lang="less" scoped>
//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;
}

.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
}

.main-body-s {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

div::-webkit-scrollbar {
  display: none;
}

.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  transition: left 0.5s;
  top: calc((100% - 89px) / 2);
}

.left-img-s {
  left: 0px;
  transition: left 0.5s;
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  transform: rotate(180deg);
  top: calc((100% - 50px) / 2);
  transition: transform 0.5s, left 0.5s;
  cursor: pointer;
}

.left-img-icon-s {
  left: -8px;
  transform: rotate(0deg);
  transition: transform 0.5s, left 0.5s;
}

.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  //background: rgba(0, 0, 0, 0.3);
  color: #fff;

  .main {
    overflow: hidden;
    height: 100%;
  }

  .main:nth-child(1) {
    width: 350px;
  }

  .main:nth-child(2) {
    width: calc(100% - 366px);
  }
}

.demo-form-inline {
  margin-top: 16px;
}

.table {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}

::v-deep .number {
  letter-spacing: 0px;
}

// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
.table-scrollbar {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}
</style>
