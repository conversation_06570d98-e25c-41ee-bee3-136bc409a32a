<template>
  <div
    ref="chartRef"
    :style="{ width: '100%', height: height + 'px' }"
    @click="clickHandle"
  ></div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue';
import * as echarts from 'echarts';

interface Props {
  chartData: number[][];
  timeData?: string[];
  title: string;
  height?: number;
  pointValue?: number;
  type?: string;
  siteId?: string;
  monitorType?: string;
  legend?: string;
}

const props = withDefaults(defineProps<Props>(), {
  chartData: () => [],
  timeData: () => [],
  title: '',
  height: 300,
  pointValue: 0,
  type: '',
  siteId: '',
  monitorType: '',
  legend: '',
});

interface Emits {
  (
    e: 'childChart',
    data: { hour: number; type: string; siteId: string; monitorType: string }
  ): void;
}

const emit = defineEmits<Emits>();

const chartRef = ref<HTMLDivElement>();
let myChart: echarts.ECharts | null = null;

const initChart = () => {
  if (!chartRef.value) return;

  // 如果图表已存在，先销毁
  if (myChart) {
    myChart.dispose();
  }

  // 基于准备好的dom，初始化echarts实例
  myChart = echarts.init(chartRef.value);

  const legendValue =
    props.legend === '' || props.legend === undefined || props.legend === null
      ? props.title
      : props.legend;

  console.log(legendValue);

  // 指定图表的配置项和数据
  const option: echarts.EChartsOption = {
    title: {
      text: legendValue,
      textStyle: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
      },
      left: 'center',
      top: 10,
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#333',
      textStyle: {
        color: '#fff',
      },
    },
    legend: {
      data: ['未整改', '已整改'],
      left: 'center',
      top: 40,
      icon: 'circle',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [
        '1月',
        '2月',
        '3月',
        '4月',
        '5月',
        '6月',
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月',
      ],
      axisLabel: {
        color: '#99b7d2',
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: '#99b7d2',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#99b7d2',
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: '#99b7d2',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(153, 183, 210, 0.2)',
        },
      },
    },
    series: [
      {
        name: '未整改',
        type: 'line',
        stack: 'Total',
        data: props.chartData[0] || [],
        itemStyle: {
          color: '#ff6b6b',
        },
        lineStyle: {
          color: '#ff6b6b',
        },
      },
      {
        name: '已整改',
        type: 'line',
        stack: 'Total',
        data: props.chartData[1] || [],
        itemStyle: {
          color: '#48dbfb',
        },
        lineStyle: {
          color: '#48dbfb',
        },
      },
    ],
  };

  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(option);
};

const clickHandle = () => {
  emit('childChart', {
    hour: 24,
    type: props.type,
    siteId: props.siteId,
    monitorType: props.monitorType,
  });
};

watch(
  () => props.chartData,
  () => {
    console.log('变化-newVal');
    console.log('变化-oldVal');
    nextTick(() => {
      initChart();
    });
  },
  { deep: true }
);

watch(
  () => props.title,
  () => {
    nextTick(() => {
      initChart();
    });
  }
);

onMounted(() => {
  nextTick(() => {
    initChart();
  });
});
</script>

<style scoped>
/* 图表容器样式 */
</style>
