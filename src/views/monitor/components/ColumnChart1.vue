<template>
  <div ref="chartRef" :style="{ width: '100%', height: height + 'px' }"></div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import * as echarts from 'echarts';

interface Props {
  chartData: number[];
  timeData?: string[];
  title: string;
  height?: number;
}

const props = withDefaults(defineProps<Props>(), {
  chartData: () => [],
  timeData: () => [],
  title: '',
  height: 300,
});

const chartRef = ref<HTMLDivElement>();
let myChart: echarts.ECharts | null = null;

const initChart = () => {
  if (!chartRef.value) return;

  // 如果图表已存在，先销毁
  if (myChart) {
    myChart.dispose();
  }

  // 基于准备好的dom，初始化echarts实例
  myChart = echarts.init(chartRef.value);

  // 指定图表的配置项和数据
  const option: echarts.EChartsOption = {
    title: {
      text: props.title,
      textStyle: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
      },
      left: 'center',
      top: 10,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#333',
      textStyle: {
        color: '#fff',
      },
    },
    legend: {
      data: [],
      left: 'center',
      top: 40,
      icon: 'circle',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [
        '有组织废气',
        '无组织废气',
        '泄露检测',
        '废水',
        '噪声',
        '地表水和地下水',
        '土壤',
        '环境空气',
        '循环冷却水',
      ],
      axisLabel: {
        color: '#99b7d2',
        interval: 0,
        rotate: 45,
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: '#99b7d2',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#99b7d2',
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: '#99b7d2',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(153, 183, 210, 0.2)',
        },
      },
    },
    series: [
      {
        name: '',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
          color: '#fff',
          fontSize: 10,
        },
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          color: '#1890ff',
        },
        data: props.chartData || [],
      },
    ],
  };

  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(option);
};

const handleResize = () => {
  if (myChart) {
    myChart.resize();
  }
};

watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      initChart();
    });
  },
  { deep: true }
);

watch(
  () => props.title,
  () => {
    nextTick(() => {
      initChart();
    });
  }
);

onMounted(() => {
  nextTick(() => {
    initChart();
    window.addEventListener('resize', handleResize);
  });
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style scoped>
/* 图表容器样式 */
</style>
