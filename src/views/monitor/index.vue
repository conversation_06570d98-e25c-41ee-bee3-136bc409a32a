<template>
  <div class="environmental-alarm-page">
    <!-- 左侧树形菜单 -->
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <a-input
        v-model:value="filterText"
        class="search"
        placeholder="输入关键词"
        allow-clear
        size="small"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="tree-wrapper">
        <a-tree
          ref="monitorListRef"
          :tree-data="treeData"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'id',
          }"
          :show-line="false"
          :show-icon="false"
          @select="handleNodeClick"
        />
      </div>
    </div>

    <!-- 展开收起按钮 -->
    <div v-if="isShow">
      <img
        src="@/assets/images/shouqi.png"
        alt=""
        :class="['left-img', isCollapsed ? 'left-img-s' : '']"
        @click="isCollapsed = !isCollapsed"
      />
      <img
        src="@/assets/images/shouqiicon.png"
        alt=""
        :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
        @click="isCollapsed = !isCollapsed"
      />
    </div>

    <!-- 拖拽菜单图标 -->
    <div
      ref="menuRef"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>

    <!-- 菜单弹窗 -->
    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <MenuDialog />
    </div>

    <!-- 主内容区域 -->
    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <!-- 主标签页 -->
      <a-tabs
        v-model:active-key="activeName"
        style="margin-bottom: 20px"
        @change="handleClick"
      >
        <a-tab-pane v-if="tbzBo" key="0" tab="自行监测方案" />
        <a-tab-pane v-if="isAuditShow && spzBo" key="1" tab="方案审批" />
        <a-tab-pane v-if="tbzBo" key="2" tab="自行监测结果" />
        <a-tab-pane v-if="tbzBo" key="3" tab="自行监测统计" />
        <a-tab-pane v-if="isAdmin" key="4" tab="方案审批数据权限管理" />
      </a-tabs>

      <!-- 监测类型标签页 -->
      <a-tabs
        v-if="(activeName === '0' || activeName === '2') && tbzBo"
        v-model:active-key="monitorType"
        @change="handleClick1"
      >
        <a-tab-pane key="0" tab="有组织废气" />
        <a-tab-pane key="1" tab="无组织废气" />
        <a-tab-pane key="2" tab="泄露检测" />
        <a-tab-pane key="3" tab="废水" />
        <a-tab-pane key="4" tab="噪声" />
        <a-tab-pane key="5" tab="地表水和地下水" />
        <a-tab-pane key="6" tab="土壤" />
        <a-tab-pane key="7" tab="环境空气" />
        <a-tab-pane key="8" tab="循环冷却水" />
      </a-tabs>

      <!-- 审批类型标签页 -->
      <a-tabs
        v-if="activeName === '1' && spzBo"
        v-model:active-key="auditType"
        @change="handleClick2"
      >
        <a-tab-pane key="0" tab="待处理" />
        <a-tab-pane key="1" tab="已处理" />
      </a-tabs>

      <!-- 查询表单 -->
      <a-form
        v-if="tbzBo || spzBo"
        ref="formRef"
        :model="formData"
        layout="inline"
        class="demo-form-inline"
      >
        <a-form-item v-if="activeName !== '4'" label="年份:">
          <a-date-picker
            v-model:value="formData.year"
            picker="year"
            placeholder="选择年"
            style="width: 200px"
          />
        </a-form-item>

        <a-form-item
          v-if="['0', '1', '2'].includes(activeName)"
          label="点位名称:"
          label-width="100px"
        >
          <a-input
            v-model:value="formData.pointName"
            placeholder="请输入"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>

        <a-form-item
          v-if="activeName === '1'"
          label="监测类型:"
          label-width="100px"
        >
          <a-select
            v-model:value="formData.monitorType"
            placeholder="请选择"
            style="width: 200px"
            show-search
            allow-clear
          >
            <a-select-option :value="0">有组织废气</a-select-option>
            <a-select-option :value="1">无组织废气</a-select-option>
            <a-select-option :value="2">泄露监测</a-select-option>
            <a-select-option :value="3">废水</a-select-option>
            <a-select-option :value="4">噪声</a-select-option>
            <a-select-option :value="5">地表水和地下水</a-select-option>
            <a-select-option :value="6">土壤</a-select-option>
            <a-select-option :value="7">环境空气</a-select-option>
            <a-select-option :value="8">循环冷却水</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          v-if="['0', '1', '2'].includes(activeName) && monitorType !== '8'"
          label="监测指标:"
          label-width="100px"
        >
          <a-select
            v-model:value="formData.monitorIndex"
            placeholder="请选择"
            style="width: 200px"
            show-search
            allow-clear
          >
            <a-select-option
              v-for="item in monitorIndexs"
              :key="item.dict_value"
              :value="item.dict_value"
            >
              {{ item.dict_label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          v-if="['0', '1', '2'].includes(activeName) && monitorType === '8'"
          label="监测指标:"
          label-width="100px"
        >
          <a-input
            v-model:value="formData.monitorIndex"
            placeholder="请输入"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>

        <a-form-item
          v-if="activeName === '0'"
          label="状态:"
          label-width="100px"
        >
          <a-select
            v-model:value="formData.monitorStatus"
            placeholder="请选择"
            style="width: 200px"
            show-search
            allow-clear
          >
            <a-select-option :value="0">待提交</a-select-option>
            <a-select-option :value="1">审批中</a-select-option>
            <a-select-option :value="2">审批不通过</a-select-option>
            <a-select-option :value="3">审批通过</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          v-if="activeName === '2'"
          label="完成情况:"
          label-width="100px"
        >
          <a-select
            v-model:value="formData.completeType"
            placeholder="请选择"
            style="width: 200px"
            show-search
            allow-clear
          >
            <a-select-option :value="0">进行中</a-select-option>
            <a-select-option :value="1">未完成</a-select-option>
            <a-select-option :value="2">已完成</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          v-if="activeName === '4'"
          label="单位:"
          label-width="100px"
        >
          <a-input
            v-model:value="formData.deptName"
            placeholder="请输入"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>

        <a-form-item
          v-if="activeName === '4'"
          label="姓名:"
          label-width="100px"
        >
          <a-input
            v-model:value="formData.userName"
            placeholder="请输入"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>

        <a-form-item class="ant-form-item-bottom">
          <a-button type="primary" style="margin-left: 100px" @click="search"
            >查询</a-button
          >
          <a-button style="margin-left: 8px" @click="resetForm">重置</a-button>
          <a-button
            v-if="activeName === '4'"
            type="primary"
            style="margin-left: 8px"
            @click="addAuditUserHandle(null)"
          >
            设置权限
          </a-button>
          <a-button
            v-if="activeName === '0'"
            type="primary"
            style="margin-left: 8px"
            @click="addHandle"
          >
            新增
          </a-button>
          <a-button
            v-if="activeName === '0'"
            type="primary"
            style="margin-left: 8px"
            @click="importXlsx"
          >
            数据导入
          </a-button>
          <a-button
            v-if="activeName === '2'"
            type="primary"
            style="margin-left: 8px"
            @click="bindFileHandle"
          >
            绑定检测报告
          </a-button>
          <a-button
            v-if="activeName === '2'"
            type="primary"
            style="margin-left: 8px"
            @click="exportXlsx"
          >
            数据导出
          </a-button>
        </a-form-item>
      </a-form>

      <!-- 表格区域 -->
      <div v-if="activeName !== '3' && (tbzBo || spzBo)" class="table">
        <div class="table-wrapper">
          <!-- 自行监测方案表格 -->
          <a-table
            v-if="activeName === '0'"
            ref="myTableRef"
            :columns="planColumns"
            :data-source="monitorResultDate"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            row-key="id"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-if="column.key === 'limitValue'">
                <span>{{ record.minNum }}-{{ record.maxNum }}</span>
              </template>
              <template v-if="column.key === 'status'">
                <span v-if="record.type === 0">待提交</span>
                <span v-if="record.type === 1">审批中</span>
                <span v-if="record.type === 2">审批不通过</span>
                <span v-if="record.type === 3">审批通过</span>
              </template>
              <template v-if="column.key === 'completion'">
                已完成:0 进行中:0 未完成:0
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleInfoClick(record)"
                    >详情</a-button
                  >
                  <a-button
                    v-if="
                      activeName === '0' && ![0, 1, 2].includes(record.type)
                    "
                    type="link"
                    size="small"
                    @click="alterationHandle(record)"
                  >
                    变更方案
                  </a-button>
                  <a-button
                    v-if="activeName === '0' && record.type === 0"
                    type="link"
                    size="small"
                    @click="updateHandle(record)"
                  >
                    编辑
                  </a-button>
                  <a-button
                    v-if="activeName === '0' && record.type !== 3"
                    type="link"
                    size="small"
                    danger
                    @click="deleteHandle(record)"
                  >
                    删除
                  </a-button>
                  <a-button
                    v-if="activeName === '2'"
                    type="link"
                    size="small"
                    @click="handleReasonClick(record)"
                  >
                    原因说明
                  </a-button>
                  <a-button
                    v-if="activeName === '2'"
                    type="link"
                    size="small"
                    @click="handleFillClick(record)"
                  >
                    填报
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>

          <!-- 方案审批表格 -->
          <a-table
            v-if="activeName === '1'"
            :columns="auditColumns"
            :data-source="auditMonitorResultDate"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            row-key="id"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-if="column.key === 'monitorType'">
                <span v-if="record.monitorType === 0">有组织废气</span>
                <span v-if="record.monitorType === 1">无组织废气</span>
                <span v-if="record.monitorType === 2">泄露监测</span>
                <span v-if="record.monitorType === 3">废水</span>
                <span v-if="record.monitorType === 4">噪声</span>
                <span v-if="record.monitorType === 5">地表水和地下水</span>
                <span v-if="record.monitorType === 6">土壤</span>
                <span v-if="record.monitorType === 7">环境空气</span>
                <span v-if="record.monitorType === 8">循环冷却水</span>
              </template>
              <template v-if="column.key === 'limitValue'">
                <span>{{ record.minNum }}-{{ record.maxNum }}</span>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button
                    v-if="auditType === '0'"
                    type="link"
                    size="small"
                    @click="auditHandle(record)"
                  >
                    审批
                  </a-button>
                  <a-button
                    v-if="auditType === '1'"
                    type="link"
                    size="small"
                    @click="handleInfoClick(record)"
                  >
                    详情
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>

          <!-- 自行监测结果表格 -->
          <a-table
            v-if="activeName === '2'"
            :columns="resultColumns"
            :data-source="monitorResultDate"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            row-key="id"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-if="column.key === 'limitValue'">
                <span>{{ record.minNum }}-{{ record.maxNum }}</span>
              </template>
              <template v-if="column.key === 'completeType'">
                <span v-if="record.completeType === 0">进行中</span>
                <span v-if="record.completeType === 1">未完成</span>
                <span v-if="record.completeType === 2">已完成</span>
              </template>
              <template v-if="column.key === 'outLimit'">
                <a-popover trigger="hover" placement="top">
                  <template #content>
                    <a-table
                      :columns="outLimitColumns"
                      :data-source="record.outLimitData"
                      :pagination="false"
                      size="small"
                    >
                      <template #bodyCell="{ column: col, index: idx }">
                        <template v-if="col.key === 'index'">{{
                          idx + 1
                        }}</template>
                      </template>
                    </a-table>
                  </template>
                  <div style="color: red; cursor: pointer">
                    {{ record.outLimitData?.length || 0 }}
                  </div>
                </a-popover>
              </template>
              <template v-if="column.key === 'fileNumber'">
                <a @click="showFileHandle(record)">{{ record.fileNumber }}</a>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleInfoClick(record)"
                    >详情</a-button
                  >
                  <a-button
                    v-if="activeName === '2'"
                    type="link"
                    size="small"
                    @click="handleReasonClick(record)"
                  >
                    原因说明
                  </a-button>
                  <a-button
                    v-if="activeName === '2'"
                    type="link"
                    size="small"
                    @click="handleFillClick(record)"
                  >
                    填报
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>

          <!-- 方案审批数据权限管理表格 -->
          <a-table
            v-if="activeName === '4'"
            :columns="auditUserColumns"
            :data-source="auditUserData"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            row-key="id"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-if="column.key === 'deptName'">
                {{ record.secondLevelName }}/{{ record.threeLevelName }}
              </template>
              <template v-if="column.key === 'action'">
                <a-button
                  type="link"
                  size="small"
                  @click="addAuditUserHandle(record)"
                >
                  设置权限
                </a-button>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 分页 -->
        <a-pagination
          v-model:current="formData.pageNum"
          v-model:page-size="formData.pageSize"
          :total="total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `共 ${total} 条记录`"
          :page-size-options="['20', '40', '60', '80', '100']"
          style="margin-top: 16px; text-align: right"
          @change="handleCurrentChange"
        />
      </div>

      <!-- 自行监测统计 -->
      <div v-if="activeName === '3'" class="scroll-container">
        <ColumnChart1 :chart-data="statisticsData2" title="单位超标情况" />
        <LineChart :chart-data="statisticsData3" title="超标整改情况" />
      </div>
    </div>

    <!-- 新增/编辑方案弹窗 -->
    <a-modal
      v-model:open="addDialog"
      :title="title"
      width="70%"
      :mask-closable="false"
    >
      <a-form
        ref="addFormRef"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="year" label="年份:">
              <a-date-picker
                v-model:value="form.year"
                picker="year"
                placeholder="选择年"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="secondLevelId" label="二级单位:">
              <a-select
                v-model:value="form.secondLevelId"
                placeholder="请选择单位"
                style="width: 100%"
                show-search
                allow-clear
                @change="(val: any) => getThreeDeptList(val)"
              >
                <a-select-option
                  v-for="item in towDeptList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              :name="threeDeptList.length > 0 ? 'threeLevelId' : ''"
              label="三级单位:"
            >
              <a-select
                v-model:value="form.threeLevelId"
                placeholder="请选择单位"
                style="width: 100%"
                show-search
                allow-clear
              >
                <a-select-option
                  v-for="item in threeDeptList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="monitorType" label="监测类型:">
              <a-select
                v-model:value="form.monitorType"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option :value="0">有组织废气</a-select-option>
                <a-select-option :value="1">无组织废气</a-select-option>
                <a-select-option :value="2">泄露监测</a-select-option>
                <a-select-option :value="3">废水</a-select-option>
                <a-select-option :value="4">噪声</a-select-option>
                <a-select-option :value="5">地表水和地下水</a-select-option>
                <a-select-option :value="6">土壤</a-select-option>
                <a-select-option :value="7">环境空气</a-select-option>
                <a-select-option :value="8">循环冷却水</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 根据监测类型显示不同的表单项 -->
        <a-row v-if="form.monitorType === 4" :gutter="16">
          <a-col :span="12">
            <a-form-item name="monitorPointType" label="监测点位类型:">
              <a-select
                v-model:value="form.monitorPointType"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in monitorPointTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="stationType" label="场站类型:">
              <a-select
                v-model:value="form.stationType"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in stationTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 地表水、土壤、环境空气的监测点位类型 -->
        <a-row v-if="[5, 6, 7].includes(form.monitorType)" :gutter="16">
          <a-col :span="24">
            <a-form-item name="monitorPointType" label="监测点位类型:">
              <a-select
                v-model:value="form.monitorPointType"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in monitorPointTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 废水类型 -->
        <a-row
          v-if="form.monitorType === 3 || form.monitorType === 8"
          :gutter="16"
        >
          <a-col :span="24">
            <a-form-item name="wastewaterType" label="废水类型:">
              <a-select
                v-model:value="form.wastewaterType"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in wastewaterTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 点位名称和地理位置 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              name="pointName"
              :label="form.monitorType === 8 ? '点位名称(进口):' : '点位名称:'"
            >
              <a-input
                v-model:value="form.pointName"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              name="province"
              :label="form.monitorType === 8 ? '地理位置(进口):' : '地理位置:'"
            >
              <a-space>
                <a-select
                  v-model:value="form.province"
                  placeholder="省"
                  style="width: 100px"
                  show-search
                  @change="(val: any) => cityList(2, val)"
                >
                  <a-select-option
                    v-for="item in provinces"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.city"
                  placeholder="市"
                  style="width: 100px"
                  show-search
                  @change="(val: any) => cityList(3, val)"
                >
                  <a-select-option
                    v-for="item in citys"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.area"
                  placeholder="区"
                  style="width: 100px"
                  show-search
                >
                  <a-select-option
                    v-for="item in areas"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 循环冷却水的出口信息 -->
        <a-row v-if="form.monitorType === 8" :gutter="16">
          <a-col :span="12">
            <a-form-item name="pointNameOut" label="点位名称(出口):">
              <a-input
                v-model:value="form.pointNameOut"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="provinceOut" label="地理位置(出口):">
              <a-space>
                <a-select
                  v-model:value="form.provinceOut"
                  placeholder="省"
                  style="width: 100px"
                  show-search
                  @change="(val: any) => cityList(2, val)"
                >
                  <a-select-option
                    v-for="item in provinces"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.cityOut"
                  placeholder="市"
                  style="width: 100px"
                  show-search
                  @change="(val: any) => cityList(3, val)"
                >
                  <a-select-option
                    v-for="item in citys"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.areaOut"
                  placeholder="区"
                  style="width: 100px"
                  show-search
                >
                  <a-select-option
                    v-for="item in areas"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点数量（地表水、土壤、环境空气） -->
        <a-row v-if="[5, 6, 7].includes(form.monitorType)" :gutter="16">
          <a-col :span="24">
            <a-form-item name="distributionNumber" label="布点数量:">
              <a-input
                v-model:value="form.distributionNumber"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点编号（噪声） -->
        <a-row v-if="form.monitorType === 4" :gutter="16">
          <a-col :span="24">
            <a-form-item name="distributionId" label="布点编号:">
              <a-input
                v-model:value="form.distributionId"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 废水相关字段 -->
        <a-row v-if="form.monitorType === 3" :gutter="16">
          <a-col :span="12">
            <a-form-item name="emissionDestination" label="排放去向:">
              <a-input
                v-model:value="form.emissionDestination"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="discharge" label="排放量:">
              <a-input
                v-model:value="form.discharge"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 有组织废气的锅炉相关字段 -->
        <a-row v-if="form.monitorType === 0" :gutter="16">
          <a-col :span="12">
            <a-form-item name="boilerType" label="锅炉类型:">
              <a-input
                v-model:value="form.boilerType"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="boilerPower" label="功率:">
              <a-select
                v-model:value="form.boilerPower"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in boilerPowerTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-if="form.monitorType === 0" :gutter="16">
          <a-col :span="12">
            <a-form-item name="boilerSize" label="规模:">
              <a-input
                v-model:value="form.boilerSize"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              name="dischargeMethodAndDestination"
              label="排放方式及去向:"
            >
              <a-select
                v-model:value="form.dischargeMethodAndDestination"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in dischargeMethodAndDestinations"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 循环冷却水的监测指标和次数 -->
        <div v-if="form.monitorType === 8">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item name="monitorIndexOut" label="监测指标(进口):">
                <a-input
                  v-model:value="form.monitorIndexOut"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumberOut" label="监测次数(进口):">
                <a-input
                  v-model:value="form.monitorNumberOut"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item name="monitorIndex" label="监测指标(出口):">
                <a-input
                  v-model:value="form.monitorIndex"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumber" label="监测次数(出口):">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 其他监测类型的监测指标和次数 -->
        <div v-else>
          <!-- 非泄露检测和废水的监测指标 -->
          <a-row
            v-if="form.monitorType !== 2 && form.monitorType !== 3"
            :gutter="16"
          >
            <a-col :span="12">
              <a-form-item name="monitorIndex" label="监测指标:">
                <a-select
                  v-model:value="form.monitorIndex"
                  placeholder="请选择"
                  style="width: 100%"
                  show-search
                >
                  <a-select-option
                    v-for="item in monitorIndexs"
                    :key="item.dict_value"
                    :value="item.dict_value"
                  >
                    {{ item.dict_label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumber" label="监测次数:">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 泄露检测和废水的监测指标 -->
          <a-row v-else :gutter="16">
            <a-col :span="8">
              <a-form-item name="monitorIndex" label="监测指标:">
                <a-select
                  v-model:value="form.monitorIndex"
                  placeholder="请选择"
                  style="width: 100%"
                  show-search
                >
                  <a-select-option
                    v-for="item in monitorIndexs"
                    :key="item.dict_value"
                    :value="item.dict_value"
                  >
                    {{ item.dict_label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col v-if="form.monitorType === 2" :span="8">
              <a-form-item name="sealNumber" label="密封点数:">
                <a-input
                  v-model:value="form.sealNumber"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="monitorNumber" label="监测次数:">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 监测周期和监测方式 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="monitorCycle" label="监测周期:">
              <a-select
                v-model:value="form.monitorCycle"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in monitorCycles"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="monitorWay" label="每次监测方式:">
              <a-select
                v-model:value="form.monitorWay"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in monitorWays"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 执行标准 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item name="executiveStandard" label="执行标准:">
              <a-select
                v-model:value="form.executiveStandard"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in executiveStandards"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 限值和单位 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="minNum" label="限值:">
              <a-input-number
                v-if="[3, 4, 5, 8].includes(form.monitorType)"
                v-model:value="form.minNum"
                :min="0"
                :max="9999999999"
                :placeholder="
                  form.monitorType === 4 ? '昼间限值最大值' : '请输入'
                "
                style="width: 49%; margin-right: 2%"
              />
              <a-input-number
                v-model:value="form.maxNum"
                :min="0"
                :max="9999999999"
                :placeholder="
                  form.monitorType === 4 ? '夜间限值最大值' : '请输入'
                "
                style="width: 49%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="unit" label="单位:">
              <a-select
                v-model:value="form.unit"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in unitTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测分析方法及依据和点位确定监测依据 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="monitorAnalysis" label="监测分析方法及依据:">
              <a-select
                v-model:value="form.monitorAnalysis"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option
                  v-for="item in monitorMethodsBases"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="form.monitorType !== 0 && form.monitorType !== 2"
            :span="12"
          >
            <a-form-item name="pointRecognition" label="点位确定监测依据:">
              <a-input
                v-model:value="form.pointRecognition"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 是否通过无异味企业验收 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item name="isPeculiarSmell" label="是否通过无异味企业验收:">
              <a-select
                v-model:value="form.isPeculiarSmell"
                placeholder="请选择"
                style="width: 100%"
                show-search
              >
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="1">是</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 文件上传 -->
        <a-form-item label="监测方案:">
          <a-upload
            v-model:file-list="fileList"
            action="api/file/upload"
            list-type="picture"
            :headers="{ Authorization: token }"
            :before-upload="beforeUpload"
            @change="handleUploadChange"
            @remove="handleRemove"
          >
            <a-button type="primary">
              <UploadOutlined />
              点击上传附件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>

      <template #footer>
        <div style="text-align: right">
          <a-button @click="addDialog = false">取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 8px"
            @click="saveHandle(0)"
            >保存</a-button
          >
          <a-button
            type="primary"
            style="margin-left: 8px"
            @click="saveHandle(1)"
            >提交</a-button
          >
        </div>
      </template>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="resultDialog"
      title="监测方案详情"
      width="70%"
      :mask-closable="false"
    >
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="年份">{{ form.year }}</a-descriptions-item>
        <a-descriptions-item label="二级单位">{{
          form.secondLevelName
        }}</a-descriptions-item>
        <a-descriptions-item label="三级单位">{{
          form.threeLevelName
        }}</a-descriptions-item>
        <a-descriptions-item label="监测类型">
          <span v-if="form.monitorType === 0">有组织废气</span>
          <span v-else-if="form.monitorType === 1">无组织废气</span>
          <span v-else-if="form.monitorType === 2">泄露监测</span>
          <span v-else-if="form.monitorType === 3">废水</span>
          <span v-else-if="form.monitorType === 4">噪声</span>
          <span v-else-if="form.monitorType === 5">地表水和地下水</span>
          <span v-else-if="form.monitorType === 6">土壤</span>
          <span v-else-if="form.monitorType === 7">环境空气</span>
          <span v-else-if="form.monitorType === 8">循环冷却水</span>
        </a-descriptions-item>
        <a-descriptions-item label="点位名称">{{
          form.pointName
        }}</a-descriptions-item>
        <a-descriptions-item label="地理位置"
          >{{ form.province }}-{{ form.city }}-{{
            form.area
          }}</a-descriptions-item
        >
        <a-descriptions-item label="监测指标">{{
          form.monitorIndex
        }}</a-descriptions-item>
        <a-descriptions-item label="监测次数">{{
          form.monitorNumber
        }}</a-descriptions-item>
        <a-descriptions-item label="监测周期">{{
          form.monitorCycle
        }}</a-descriptions-item>
        <a-descriptions-item label="监测方式">{{
          form.monitorWay
        }}</a-descriptions-item>
        <a-descriptions-item label="执行标准">{{
          form.executiveStandard
        }}</a-descriptions-item>
        <a-descriptions-item label="限值"
          >{{ form.minNum }}-{{ form.maxNum }}</a-descriptions-item
        >
        <a-descriptions-item label="单位">{{ form.unit }}</a-descriptions-item>
        <a-descriptions-item label="监测方案" :span="2">
          <a v-if="form.fileName" :href="form.fileUrl" target="_blank">{{
            form.fileName
          }}</a>
          <span v-else>暂无附件</span>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 方案审批弹窗 -->
    <!-- <a-modal
      v-model:open="auditDialog"
      title="方案审批"
      width="60%"
      :mask-closable="false"
    >
      <a-form
        ref="auditFormRef"
        :model="form"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="年份:">
              <a-input :value="form.year ? form.year.year() : ''" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="二级单位:">
              <a-input v-model:value="form.secondLevelName" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="三级单位:">
              <a-input v-model:value="form.threeLevelName" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="点位名称:">
              <a-input v-model:value="form.pointName" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="审批意见:">
          <a-textarea
            v-model:value="form.auditContext"
            :rows="4"
            placeholder="请输入审批意见"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="auditDialog = false">取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 8px"
            @click="auditApprove"
            >通过</a-button
          >
          <a-button danger style="margin-left: 8px" @click="auditReject"
            >驳回</a-button
          >
        </div>
      </template>
    </a-modal> -->

    <!-- 原因说明弹窗 -->
    <a-modal
      v-model:open="addReasonDialog"
      title="未完成原因说明"
      width="50%"
      :mask-closable="false"
    >
      <a-form
        ref="reasonFormRef"
        :model="reasonForm"
        :rules="reasonFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="未完成原因:" name="reason">
          <a-textarea
            v-model:value="reasonForm.reason"
            :rows="4"
            placeholder="请输入未完成原因"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="addReasonDialog = false">取消</a-button>
          <a-button type="primary" style="margin-left: 8px" @click="saveReason"
            >保存</a-button
          >
        </div>
      </template>
    </a-modal>

    <!-- 填报弹窗 -->
    <a-modal
      v-model:open="addFillDialog"
      title="填报监测结果"
      width="40%"
      :mask-closable="false"
    >
      <a-form
        ref="fillFormRef"
        :model="fillForm"
        :rules="fillRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="监测结果:" name="monitorResult">
              <a-input-number
                v-model:value="fillForm.monitorResult"
                :min="1"
                :max="9999999999"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="单位:" name="monitorUnit">
              <a-select
                v-model:value="fillForm.monitorUnit"
                placeholder="请选择"
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in unitTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="监测时间:" name="monitorTime">
              <a-date-picker
                v-model:value="fillForm.monitorTime"
                placeholder="选择监测时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="监测报告:">
              <a-upload
                v-model:file-list="fileList3"
                action="api/file/upload"
                list-type="picture"
                :headers="{ Authorization: token }"
                :limit="1"
                @change="handleFillUploadChange"
                @remove="handleFillRemove"
              >
                <a-button type="primary">点击上传附件</a-button>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="addFillDialog = false">取消</a-button>
          <a-button type="primary" style="margin-left: 8px" @click="fillHandle">
            确定
          </a-button>
        </div>
      </template>
    </a-modal>

    <!-- 审批用户权限管理弹窗 -->
    <a-modal
      v-model:open="auditUserDialog"
      title="新增审批人"
      width="50%"
      :mask-closable="false"
    >
      <div style="font-size: 20px; font-weight: bolder; margin-bottom: 20px">
        {{ userTitle }}
      </div>
      <a-form
        ref="auditUserFormRef"
        :model="auditUserForm"
        :rules="auditUserFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="选择用户:" name="userId">
              <a-select
                v-model:value="auditUserForm.userId"
                placeholder="请选择"
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in userList"
                  :key="item.userId"
                  :value="item.userId"
                >
                  {{ item.nickName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <div class="tree-wrapper">
              <a-form-item label="请选择审批部门的权限:" name="auditDeptJson">
                <a-tree
                  ref="treeRef"
                  v-model:checked-keys="auditUserForm.auditDeptJson"
                  :tree-data="treeData"
                  :field-names="{
                    children: 'children',
                    title: 'siteShortName',
                    key: 'id',
                  }"
                  checkable
                  :show-line="false"
                  :show-icon="false"
                  @check="handleCheckChange"
                />
              </a-form-item>
            </div>
          </a-col>
        </a-row>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="auditUserDialog = false">取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 8px"
            @click="saveAuditUserHandle"
          >
            确定
          </a-button>
        </div>
      </template>
    </a-modal>

    <!-- 文件绑定弹窗 -->
    <a-modal
      v-model:open="bindDialog"
      title="绑定检测报告"
      width="50%"
      :mask-closable="false"
    >
      <a-form
        ref="bindFormRef"
        :model="bindForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="选择报告:" name="fileId">
          <a-select
            v-model:value="bindForm.fileId"
            placeholder="请选择检测报告"
            style="width: 100%"
          >
            <a-select-option
              v-for="file in fileOptions"
              :key="file.id"
              :value="file.id"
            >
              {{ file.fileName }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="bindDialog = false">取消</a-button>
          <a-button type="primary" style="margin-left: 8px" @click="bindFile"
            >绑定</a-button
          >
        </div>
      </template>
    </a-modal>

    <!-- 文件查看弹窗 -->
    <a-modal
      v-model:open="showFileDialog"
      title="监测报告文件"
      width="80%"
      :mask-closable="false"
    >
      <a-table
        :columns="fileColumns"
        :data-source="fileList"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ index + 1 }}
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="downloadFile(record)">下载</a-button>
            <a-button type="link" @click="previewFile(record)">预览</a-button>
          </template>
        </template>
      </a-table>
    </a-modal>

    <!-- 数据导入弹窗 -->
    <a-modal
      v-model:open="importDialog"
      title="数据导入"
      width="50%"
      :mask-closable="false"
    >
      <a-form>
        <a-form-item label="下载模板:">
          <a-button type="link" @click="downloadTemplate"
            >点击下载导入模板</a-button
          >
        </a-form-item>
        <a-form-item label="上传文件:">
          <a-upload
            v-model:file-list="importFileList"
            action="api/file/upload"
            :headers="{ Authorization: token }"
            :before-upload="beforeUpload"
            :max-count="1"
            @change="handleImportChange"
          >
            <a-button type="primary">
              <UploadOutlined />
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="importDialog = false">取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 8px"
            @click="confirmImport"
            >导入</a-button
          >
        </div>
      </template>
    </a-modal>

    <!-- 方案详情弹窗 -->
    <a-modal
      v-model:open="resultDialog"
      title="方案详情"
      width="70%"
      :mask-closable="false"
    >
      <div style="font-size: 20px; font-weight: bolder; margin-bottom: 16px">
        填报监测结果
      </div>
      <a-form
        :model="form"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="年份:">
              <a-date-picker
                :value="form.year"
                picker="year"
                placeholder="选择年"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="二级单位:">
              <a-select
                v-model:value="form.secondLevelId"
                placeholder="请选择单位"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in towDeptList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="三级单位:">
              <a-select
                v-model:value="form.threeLevelId"
                placeholder="请选择单位"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in threeDeptList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="监测类型:">
              <a-select
                v-model:value="form.monitorType"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option :value="0">有组织废气</a-select-option>
                <a-select-option :value="1">无组织废气</a-select-option>
                <a-select-option :value="2">泄露监测</a-select-option>
                <a-select-option :value="3">废水</a-select-option>
                <a-select-option :value="4">噪声</a-select-option>
                <a-select-option :value="5">地表水和地下水</a-select-option>
                <a-select-option :value="6">土壤</a-select-option>
                <a-select-option :value="7">环境空气</a-select-option>
                <a-select-option :value="8">循环冷却水</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 点位名称和地理位置 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              :label="form.monitorType === 8 ? '点位名称(进口):' : '点位名称:'"
            >
              <a-input
                v-model:value="form.pointName"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              :label="form.monitorType === 8 ? '地理位置(进口):' : '地理位置:'"
            >
              <a-space>
                <a-select
                  v-model:value="form.province"
                  placeholder="省"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in provinces"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.city"
                  placeholder="市"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in citys"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.area"
                  placeholder="区"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in areas"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 循环冷却水的出口信息 -->
        <a-row v-if="form.monitorType === 8" :gutter="16">
          <a-col :span="12">
            <a-form-item label="点位名称(出口):">
              <a-input
                v-model:value="form.pointNameOut"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="地理位置(出口):">
              <a-space>
                <a-select
                  v-model:value="form.provinceOut"
                  placeholder="省"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in provinces"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.cityOut"
                  placeholder="市"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in citys"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.areaOut"
                  placeholder="区"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in areas"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点数量（地表水、土壤、环境空气） -->
        <a-row v-if="[5, 6, 7].includes(form.monitorType)" :gutter="16">
          <a-col :span="24">
            <a-form-item label="布点数量:">
              <a-input
                v-model:value="form.distributionNumber"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点编号（噪声） -->
        <a-row v-if="form.monitorType === 4" :gutter="16">
          <a-col :span="24">
            <a-form-item label="布点编号:">
              <a-input
                v-model:value="form.distributionId"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 废水相关字段 -->
        <a-row v-if="form.monitorType === 3" :gutter="16">
          <a-col :span="12">
            <a-form-item label="排放去向:">
              <a-input
                v-model:value="form.emissionDestination"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排放量:">
              <a-input
                v-model:value="form.discharge"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 有组织废气的锅炉相关字段 -->
        <a-row v-if="form.monitorType === 0" :gutter="16">
          <a-col :span="12">
            <a-form-item label="锅炉类型:">
              <a-input
                v-model:value="form.boilerType"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="功率:">
              <a-select
                v-model:value="form.boilerPower"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in boilerPowerTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-if="form.monitorType === 0" :gutter="16">
          <a-col :span="12">
            <a-form-item label="规模:">
              <a-input
                v-model:value="form.boilerSize"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排放方式及去向:">
              <a-select
                v-model:value="form.dischargeMethodAndDestination"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in dischargeMethodAndDestinations"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 循环冷却水的监测指标和次数 -->
        <div v-if="form.monitorType === 8">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="监测指标(进口):">
                <a-input
                  v-model:value="form.monitorIndexOut"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="监测次数(进口):">
                <a-input
                  v-model:value="form.monitorNumberOut"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="监测指标(出口):">
                <a-input
                  v-model:value="form.monitorIndex"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="监测次数(出口):">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 其他监测类型的监测指标和次数 -->
        <div v-else>
          <!-- 非泄露检测和废水的监测指标 -->
          <a-row
            v-if="form.monitorType !== 2 && form.monitorType !== 3"
            :gutter="16"
          >
            <a-col :span="12">
              <a-form-item label="监测指标:">
                <a-select
                  v-model:value="form.monitorIndex"
                  placeholder="请选择"
                  style="width: 100%"
                  disabled
                >
                  <a-select-option
                    v-for="item in monitorIndexs"
                    :key="item.dict_value"
                    :value="item.dict_value"
                  >
                    {{ item.dict_label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="监测次数:">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 泄露检测和废水的监测指标 -->
          <a-row v-else :gutter="16">
            <a-col :span="8">
              <a-form-item label="监测指标:">
                <a-select
                  v-model:value="form.monitorIndex"
                  placeholder="请选择"
                  style="width: 100%"
                  disabled
                >
                  <a-select-option
                    v-for="item in monitorIndexs"
                    :key="item.dict_value"
                    :value="item.dict_value"
                  >
                    {{ item.dict_label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col v-if="form.monitorType === 2" :span="8">
              <a-form-item label="密封点数:">
                <a-input
                  v-model:value="form.sealNumber"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="监测次数:">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 监测周期和监测方式 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="监测周期:">
              <a-select
                v-model:value="form.monitorCycle"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorCycles"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="每次监测方式:">
              <a-select
                v-model:value="form.monitorWay"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorWays"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 执行标准 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="执行标准:">
              <a-select
                v-model:value="form.executiveStandard"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in executiveStandards"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 限值和单位 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="限值:">
              <a-input-number
                v-if="[3, 4, 5, 8].includes(form.monitorType)"
                v-model:value="form.minNum"
                :min="0"
                :max="9999999999"
                :placeholder="
                  form.monitorType === 4 ? '昼间限值最大值' : '请输入'
                "
                style="width: 49%; margin-right: 2%"
                disabled
              />
              <a-input-number
                v-model:value="form.maxNum"
                :min="0"
                :max="9999999999"
                :placeholder="
                  form.monitorType === 4 ? '夜间限值最大值' : '请输入'
                "
                style="width: 49%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="单位:">
              <a-select
                v-model:value="form.unit"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in unitTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测分析方法及依据和点位确定监测依据 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="监测分析方法及依据:">
              <a-select
                v-model:value="form.monitorAnalysis"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorMethodsBases"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="form.monitorType !== 0 && form.monitorType !== 2"
            :span="12"
          >
            <a-form-item label="点位确定监测依据:">
              <a-input
                v-model:value="form.pointRecognition"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 是否通过无异味企业验收 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="是否通过无异味企业验收:">
              <a-select
                v-model:value="form.isPeculiarSmell"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="1">是</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 文件上传 -->
        <a-form-item label="监测方案:">
          <a-upload
            v-model:file-list="fileList"
            action="api/file/upload"
            list-type="picture"
            :headers="{ Authorization: token }"
            disabled
          >
          </a-upload>
        </a-form-item>
      </a-form>

      <!-- 未完成原因 -->
      <div style="margin-top: 20px">
        <span style="font-size: 20px; font-weight: bold">未完成原因</span>
        <a-table
          :columns="reasonColumns"
          :data-source="tableData1"
          :pagination="false"
          size="small"
          style="margin-top: 10px"
        />
      </div>

      <!-- 方案审批 -->
      <div style="margin-top: 20px">
        <span style="font-size: 20px; font-weight: bold">方案审批</span>
        <a-table
          :columns="detailAuditColumns"
          :data-source="tableData2"
          :pagination="false"
          size="small"
          style="margin-top: 10px"
        />
      </div>

      <!-- 方案变更 -->
      <div style="margin-top: 20px">
        <span style="font-size: 20px; font-weight: bold">方案变更</span>
        <a-table
          :columns="alterationTableColumns"
          :data-source="tableData3"
          :pagination="false"
          size="small"
          style="margin-top: 10px"
        />
      </div>

      <!-- 完成情况 -->
      <div style="margin-top: 20px">
        <span style="font-size: 20px; font-weight: bold">完成情况</span>
        <a-table
          :columns="completionColumns"
          :data-source="resultDate"
          :pagination="false"
          size="small"
          :scroll="{ y: 300 }"
          style="margin-top: 10px"
        />
      </div>

      <template #footer>
        <div style="text-align: right">
          <a-button @click="resultDialog = false">取消</a-button>
        </div>
      </template>
    </a-modal>

    <!-- 变更方案弹窗 -->
    <a-modal
      v-model:open="alterationDialog"
      title="变更方案"
      width="50%"
      :mask-closable="false"
    >
      <a-form
        ref="alterationFormRef"
        :model="alterationForm"
        :rules="alterationRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item name="alterationReason" label="变更原因:">
          <a-textarea
            v-model:value="alterationForm.alterationReason"
            :rows="4"
            placeholder="请输入变更原因"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="变更审批表:">
          <a-upload
            v-model:file-list="alterationFileList"
            action="api/file/upload"
            list-type="picture"
            :headers="{ Authorization: token }"
            :before-upload="beforeUpload"
            :limit="1"
            @change="handleAlterationUploadChange"
            @remove="handleAlterationRemove"
          >
            <a-button type="primary">
              <UploadOutlined />
              点击上传附件
            </a-button>
          </a-upload>
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="alterationMonitorNumber" label="监测次数:">
              <a-input
                v-model:value="alterationForm.alterationMonitorNumber"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="alterationMonitorCycle" label="监测周期:">
              <a-select
                v-model:value="alterationForm.alterationMonitorCycle"
                placeholder="请选择"
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in monitorCycles"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item name="alterationTakeEffect" label="生效时间:">
          <a-date-picker
            v-model:value="alterationForm.alterationTakeEffect"
            placeholder="选择生效时间"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>

      <template #footer>
        <div style="text-align: right">
          <a-button @click="alterationDialog = false">取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 8px"
            @click="saveAlterationHandle"
            >确定</a-button
          >
        </div>
      </template>
    </a-modal>

    <!-- 整改弹窗 -->
    <a-modal v-model:open="abarbeitungDialog" title="超标整改" width="40%">
      <a-form
        ref="abarbeitungFormRef"
        :model="abarbeitungForm"
        :rules="abarbeitungRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item name="abarbeitungReason" label="超标原因分析:">
          <a-textarea
            v-model:value="abarbeitungForm.abarbeitungReason"
            :rows="4"
            placeholder="请输入超标原因"
            allow-clear
          />
        </a-form-item>

        <a-form-item name="abarbeitungMeasure" label="超标整改措施:">
          <a-textarea
            v-model:value="abarbeitungForm.abarbeitungMeasure"
            :rows="4"
            placeholder="请输入整改措施"
            allow-clear
          />
        </a-form-item>

        <a-form-item name="lastIsStandards" label="整改后是否达标:">
          <a-select
            v-model:value="abarbeitungForm.lastIsStandards"
            placeholder="请选择"
            style="width: 100%"
          >
            <a-select-option :value="0">否</a-select-option>
            <a-select-option :value="1">是</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          v-if="abarbeitungForm.lastIsStandards === 0"
          name="lastMeasure"
          label="整改后仍未达标下部措施:"
        >
          <a-textarea
            v-model:value="abarbeitungForm.lastMeasure"
            :rows="4"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>
      </a-form>

      <template #footer>
        <div style="text-align: right">
          <a-button @click="abarbeitungDialog = false">取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 8px"
            @click="saveAbarbeitungHandle"
          >
            确定
          </a-button>
        </div>
      </template>
    </a-modal>

    <!-- 整改详情弹窗 -->
    <a-modal v-model:open="abarbeitungInfoDialog" title="整改详情" width="60%">
      <a-form
        :model="abarbeitungInfoForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="年份:">
              <a-date-picker
                :value="abarbeitungInfoForm.qhMonitorResult?.yearDate"
                picker="year"
                placeholder="选择年"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="二级单位:">
              <a-select
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.secondLevelId
                "
                placeholder="请选择单位"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in towDeptList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="三级单位:">
              <a-select
                v-model:value="abarbeitungInfoForm.qhMonitorResult.threeLevelId"
                placeholder="请选择单位"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in threeDeptList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="监测类型:">
              <a-select
                v-model:value="abarbeitungInfoForm.qhMonitorResult.monitorType"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option :value="0">有组织废气</a-select-option>
                <a-select-option :value="1">无组织废气</a-select-option>
                <a-select-option :value="2">泄露监测</a-select-option>
                <a-select-option :value="3">废水</a-select-option>
                <a-select-option :value="4">噪声</a-select-option>
                <a-select-option :value="5">地表水和地下水</a-select-option>
                <a-select-option :value="6">土壤</a-select-option>
                <a-select-option :value="7">环境空气</a-select-option>
                <a-select-option :value="8">循环冷却水</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 根据监测类型显示不同字段 -->
        <a-row
          v-if="abarbeitungInfoForm.qhMonitorResult?.monitorType === 4"
          :gutter="16"
        >
          <a-col :span="12">
            <a-form-item label="监测点位类型:">
              <a-select
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.monitorPointType
                "
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorPointTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="场站类型:">
              <a-select
                v-model:value="abarbeitungInfoForm.qhMonitorResult.stationType"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in stationTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row
          v-if="
            [5, 6, 7].includes(abarbeitungInfoForm.qhMonitorResult?.monitorType)
          "
          :gutter="16"
        >
          <a-col :span="24">
            <a-form-item label="监测点位类型:">
              <a-select
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.monitorPointType
                "
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorPointTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row
          v-if="abarbeitungInfoForm.qhMonitorResult?.monitorType === 3"
          :gutter="16"
        >
          <a-col :span="24">
            <a-form-item label="废水类型:">
              <a-select
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.wastewaterType
                "
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in wastewaterTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="点位名称:">
              <a-input
                v-model:value="abarbeitungInfoForm.qhMonitorResult.pointName"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="地理位置:">
              <a-space>
                <a-select
                  v-model:value="abarbeitungInfoForm.qhMonitorResult.province"
                  placeholder="省"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in provinces"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="abarbeitungInfoForm.qhMonitorResult.city"
                  placeholder="市"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in citys"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="abarbeitungInfoForm.qhMonitorResult.area"
                  placeholder="区"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in areas"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点数量（地表水、土壤、环境空气） -->
        <a-row
          v-if="
            [5, 6, 7].includes(abarbeitungInfoForm.qhMonitorResult?.monitorType)
          "
          :gutter="16"
        >
          <a-col :span="24">
            <a-form-item label="布点数量:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.distributionNumber
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 噪声的厂界/敏感点和布点编号 -->
        <a-row
          v-if="abarbeitungInfoForm.qhMonitorResult?.monitorType === 4"
          :gutter="16"
        >
          <a-col :span="12">
            <a-form-item label="厂界/敏感点:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.sensitiveSpot
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="布点编号:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.distributionId
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 废水相关字段 -->
        <a-row
          v-if="abarbeitungInfoForm.qhMonitorResult?.monitorType === 3"
          :gutter="16"
        >
          <a-col :span="12">
            <a-form-item label="排放去向:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.emissionDestination
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排放量:">
              <a-input
                v-model:value="abarbeitungInfoForm.qhMonitorResult.discharge"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 有组织废气的锅炉相关字段 -->
        <a-row
          v-if="abarbeitungInfoForm.qhMonitorResult?.monitorType === 0"
          :gutter="16"
        >
          <a-col :span="12">
            <a-form-item label="锅炉类型及规模:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.boilerTypeAndSize
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排放方式及去向:">
              <a-select
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult
                    .dischargeMethodAndDestination
                "
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in dischargeMethodAndDestinations"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测指标和监测次数 -->
        <a-row
          v-if="
            abarbeitungInfoForm.qhMonitorResult?.monitorType !== 2 &&
            abarbeitungInfoForm.qhMonitorResult?.monitorType !== 3
          "
          :gutter="16"
        >
          <a-col :span="12">
            <a-form-item label="监测指标:">
              <a-input
                v-model:value="abarbeitungInfoForm.qhMonitorResult.monitorIndex"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="监测次数:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.monitorNumber
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 泄露检测和废水的监测指标 -->
        <a-row v-else :gutter="16">
          <a-col :span="8">
            <a-form-item label="监测指标:">
              <a-input
                v-model:value="abarbeitungInfoForm.qhMonitorResult.monitorIndex"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="密封点数:">
              <a-input
                v-model:value="abarbeitungInfoForm.qhMonitorResult.sealNumber"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="监测次数:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.monitorNumber
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测周期和监测方式 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="监测周期:">
              <a-select
                v-model:value="abarbeitungInfoForm.qhMonitorResult.monitorCycle"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorCycles"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="每次监测方式:">
              <a-select
                v-model:value="abarbeitungInfoForm.qhMonitorResult.monitorWay"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorWays"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 执行标准 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="执行标准:">
              <a-select
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.executiveStandard
                "
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in executiveStandards"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 限值和单位 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="限值:">
              <a-input-number
                v-model:value="abarbeitungInfoForm.qhMonitorResult.minNum"
                :min="1"
                :max="9999999999"
                placeholder="请输入"
                style="width: 49%; margin-right: 2%"
                disabled
              />
              <a-input-number
                v-model:value="abarbeitungInfoForm.qhMonitorResult.maxNum"
                :min="1"
                :max="9999999999"
                placeholder="请输入"
                style="width: 49%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="单位:">
              <a-select
                v-model:value="abarbeitungInfoForm.qhMonitorResult.unit"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in unitTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测分析方法及依据和点位确定监测依据 -->
        <a-row
          v-if="
            [1, 3, 4, 5, 6, 7].includes(
              abarbeitungInfoForm.qhMonitorResult?.monitorType
            )
          "
          :gutter="16"
        >
          <a-col :span="12">
            <a-form-item label="监测分析方法及依据:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.monitorAnalysis
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="点位确定监测依据:">
              <a-input
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResult.pointRecognition
                "
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测方案文件 -->
        <a-form-item label="监测方案:">
          <a-upload
            v-model:file-list="fileList"
            action="api/file/upload"
            list-type="picture"
            :headers="{ Authorization: token }"
            disabled
          >
          </a-upload>
        </a-form-item>

        <!-- 监测结果信息 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="监测结果:">
              <a-input-number
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResultChild.monitorResult
                "
                :min="1"
                :max="9999999999"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="单位:">
              <a-select
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResultChild.monitorUnit
                "
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in unitTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="监测时间:">
              <a-date-picker
                :value="abarbeitungInfoForm.qhMonitorResultChild.monitorTime"
                placeholder="选择监测时间"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否超标:">
              <a-select
                v-model:value="
                  abarbeitungInfoForm.qhMonitorResultChild.lastIsStandards
                "
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option :value="0">未超标</a-select-option>
                <a-select-option :value="1">超标</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测报告文件 -->
        <a-form-item label="监测报告:">
          <a-upload
            v-model:file-list="fileList3"
            action="api/file/upload"
            list-type="picture"
            :headers="{ Authorization: token }"
            disabled
          >
          </a-upload>
        </a-form-item>

        <!-- 整改信息 -->
        <a-form-item label="超标原因分析:">
          <a-textarea
            v-model:value="
              abarbeitungInfoForm.qhMonitorResultChild.abarbeitungReason
            "
            :rows="4"
            placeholder="请输入超标原因"
            disabled
          />
        </a-form-item>

        <a-form-item label="超标整改措施:">
          <a-textarea
            v-model:value="
              abarbeitungInfoForm.qhMonitorResultChild.abarbeitungMeasure
            "
            :rows="4"
            placeholder="请输入整改措施"
            disabled
          />
        </a-form-item>

        <a-form-item label="整改后是否达标:">
          <a-select
            v-model:value="
              abarbeitungInfoForm.qhMonitorResultChild.lastIsStandards
            "
            placeholder="请选择"
            style="width: 100%"
            disabled
          >
            <a-select-option :value="0">否</a-select-option>
            <a-select-option :value="1">是</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          v-if="abarbeitungInfoForm.qhMonitorResultChild.lastIsStandards === 0"
          label="整改后仍未达标下部措施:"
        >
          <a-textarea
            v-model:value="abarbeitungInfoForm.qhMonitorResultChild.lastMeasure"
            :rows="4"
            placeholder="请输入"
            disabled
          />
        </a-form-item>
      </a-form>

      <template #footer>
        <div style="text-align: right">
          <a-button @click="abarbeitungInfoDialog = false">关闭</a-button>
        </div>
      </template>
    </a-modal>

    <!-- 审批弹窗 -->
    <a-modal v-model:open="auditDialog" title="审批" width="70%">
      <a-form
        ref="auditFormRef"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 基本信息 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="year" label="年份:">
              <a-date-picker
                v-model:value="form.year"
                picker="year"
                placeholder="选择年"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="secondLevelId" label="二级单位:">
              <a-select
                v-model:value="form.secondLevelId"
                placeholder="请选择单位"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in towDeptList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              :name="threeDeptList.length > 0 ? 'threeLevelId' : ''"
              label="三级单位:"
            >
              <a-select
                v-model:value="form.threeLevelId"
                placeholder="请选择单位"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in threeDeptList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="monitorType" label="监测类型:">
              <a-select
                v-model:value="form.monitorType"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option :value="0">有组织废气</a-select-option>
                <a-select-option :value="1">无组织废气</a-select-option>
                <a-select-option :value="2">泄露监测</a-select-option>
                <a-select-option :value="3">废水</a-select-option>
                <a-select-option :value="4">噪声</a-select-option>
                <a-select-option :value="5">地表水和地下水</a-select-option>
                <a-select-option :value="6">土壤</a-select-option>
                <a-select-option :value="7">环境空气</a-select-option>
                <a-select-option :value="8">循环冷却水</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 根据监测类型显示不同字段 -->
        <a-row v-if="form.monitorType === 4" :gutter="16">
          <a-col :span="12">
            <a-form-item name="monitorPointType" label="监测点位类型:">
              <a-select
                v-model:value="form.monitorPointType"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorPointTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="stationType" label="场站类型:">
              <a-select
                v-model:value="form.stationType"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in stationTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-if="[5, 6, 7].includes(form.monitorType)" :gutter="16">
          <a-col :span="24">
            <a-form-item name="monitorPointType" label="监测点位类型:">
              <a-select
                v-model:value="form.monitorPointType"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorPointTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-if="[3, 8].includes(form.monitorType)" :gutter="16">
          <a-col :span="24">
            <a-form-item name="wastewaterType" label="废水类型:">
              <a-select
                v-model:value="form.wastewaterType"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in wastewaterTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 点位名称和地理位置 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              name="pointName"
              :label="form.monitorType === 8 ? '点位名称(进口):' : '点位名称:'"
            >
              <a-input
                v-model:value="form.pointName"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              name="province"
              :label="form.monitorType === 8 ? '地理位置(进口):' : '地理位置:'"
            >
              <a-space>
                <a-select
                  v-model:value="form.province"
                  placeholder="省"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in provinces"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.city"
                  placeholder="市"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in citys"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.area"
                  placeholder="区"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in areas"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 循环冷却水的出口信息 -->
        <a-row v-if="form.monitorType === 8" :gutter="16">
          <a-col :span="12">
            <a-form-item name="pointNameOut" label="点位名称(出口):">
              <a-input
                v-model:value="form.pointNameOut"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="provinceOut" label="地理位置(出口):">
              <a-space>
                <a-select
                  v-model:value="form.provinceOut"
                  placeholder="省"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in provinces"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.cityOut"
                  placeholder="市"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in citys"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.areaOut"
                  placeholder="区"
                  style="width: 100px"
                  disabled
                >
                  <a-select-option
                    v-for="item in areas"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点数量（地表水、土壤、环境空气） -->
        <a-row v-if="[5, 6, 7].includes(form.monitorType)" :gutter="16">
          <a-col :span="24">
            <a-form-item name="distributionNumber" label="布点数量:">
              <a-input
                v-model:value="form.distributionNumber"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点编号（噪声） -->
        <a-row v-if="form.monitorType === 4" :gutter="16">
          <a-col :span="24">
            <a-form-item name="distributionId" label="布点编号:">
              <a-input
                v-model:value="form.distributionId"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 废水相关字段 -->
        <a-row v-if="form.monitorType === 3" :gutter="16">
          <a-col :span="12">
            <a-form-item name="emissionDestination" label="排放去向:">
              <a-input
                v-model:value="form.emissionDestination"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="discharge" label="排放量:">
              <a-input
                v-model:value="form.discharge"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 有组织废气的锅炉相关字段 -->
        <a-row v-if="form.monitorType === 0" :gutter="16">
          <a-col :span="12">
            <a-form-item name="boilerType" label="锅炉类型:">
              <a-input
                v-model:value="form.boilerType"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="boilerPower" label="功率:">
              <a-select
                v-model:value="form.boilerPower"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in boilerPowerTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-if="form.monitorType === 0" :gutter="16">
          <a-col :span="12">
            <a-form-item name="boilerSize" label="规模:">
              <a-input
                v-model:value="form.boilerSize"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              name="dischargeMethodAndDestination"
              label="排放方式及去向:"
            >
              <a-select
                v-model:value="form.dischargeMethodAndDestination"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in dischargeMethodAndDestinations"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 循环冷却水的监测指标和次数 -->
        <div v-if="form.monitorType === 8">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item name="monitorIndexOut" label="监测指标(进口):">
                <a-input
                  v-model:value="form.monitorIndexOut"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumberOut" label="监测次数(进口):">
                <a-input
                  v-model:value="form.monitorNumberOut"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item name="monitorIndex" label="监测指标(出口):">
                <a-input
                  v-model:value="form.monitorIndex"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumber" label="监测次数(出口):">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 其他监测类型的监测指标和次数 -->
        <div v-else>
          <!-- 非泄露检测和废水的监测指标 -->
          <a-row
            v-if="form.monitorType !== 2 && form.monitorType !== 3"
            :gutter="16"
          >
            <a-col :span="12">
              <a-form-item name="monitorIndex" label="监测指标:">
                <a-select
                  v-model:value="form.monitorIndex"
                  placeholder="请选择"
                  style="width: 100%"
                  disabled
                >
                  <a-select-option
                    v-for="item in monitorIndexs"
                    :key="item.dict_value"
                    :value="item.dict_value"
                  >
                    {{ item.dict_label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumber" label="监测次数:">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 泄露检测和废水的监测指标 -->
          <a-row v-else :gutter="16">
            <a-col :span="8">
              <a-form-item name="monitorIndex" label="监测指标:">
                <a-select
                  v-model:value="form.monitorIndex"
                  placeholder="请选择"
                  style="width: 100%"
                  disabled
                >
                  <a-select-option
                    v-for="item in monitorIndexs"
                    :key="item.dict_value"
                    :value="item.dict_value"
                  >
                    {{ item.dict_label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col v-if="form.monitorType === 2" :span="8">
              <a-form-item name="sealNumber" label="密封点数:">
                <a-input
                  v-model:value="form.sealNumber"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="monitorNumber" label="监测次数:">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 监测周期和监测方式 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="monitorCycle" label="监测周期:">
              <a-select
                v-model:value="form.monitorCycle"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorCycles"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="monitorWay" label="每次监测方式:">
              <a-select
                v-model:value="form.monitorWay"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorWays"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 执行标准 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item name="executiveStandard" label="执行标准:">
              <a-select
                v-model:value="form.executiveStandard"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in executiveStandards"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 限值和单位 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="minNum" label="限值:">
              <a-input-number
                v-if="[3, 4, 5, 8].includes(form.monitorType)"
                v-model:value="form.minNum"
                :min="0"
                :max="9999999999"
                :placeholder="
                  form.monitorType === 4 ? '昼间限值最大值' : '请输入'
                "
                style="width: 49%; margin-right: 2%"
                disabled
              />
              <a-input-number
                v-model:value="form.maxNum"
                :min="0"
                :max="9999999999"
                :placeholder="
                  form.monitorType === 4 ? '夜间限值最大值' : '请输入'
                "
                style="width: 49%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="unit" label="单位:">
              <a-select
                v-model:value="form.unit"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in unitTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测分析方法及依据和点位确定监测依据 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="monitorAnalysis" label="监测分析方法及依据:">
              <a-select
                v-model:value="form.monitorAnalysis"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option
                  v-for="item in monitorMethodsBases"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="form.monitorType !== 0 && form.monitorType !== 2"
            :span="12"
          >
            <a-form-item name="pointRecognition" label="点位确定监测依据:">
              <a-input
                v-model:value="form.pointRecognition"
                placeholder="请输入"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 是否通过无异味企业验收 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item name="isPeculiarSmell" label="是否通过无异味企业验收:">
              <a-select
                v-model:value="form.isPeculiarSmell"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="1">是</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 文件上传 -->
        <a-form-item label="监测方案:">
          <a-upload
            v-model:file-list="fileList"
            action="api/file/upload"
            list-type="picture"
            :headers="{ Authorization: token }"
            disabled
          >
          </a-upload>
        </a-form-item>

        <!-- 审批意见部分 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item name="radioValue" label="是否通过:">
              <a-radio-group v-model:value="form.radioValue">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item
              :name="form.radioValue === 0 ? 'auditContext' : ''"
              label="备注:"
            >
              <a-textarea
                v-model:value="form.auditContext"
                :rows="4"
                placeholder="请输入备注"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <template #footer>
        <div style="text-align: right">
          <a-button @click="auditDialog = false">取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 8px"
            @click="auditOkHandle"
          >
            确定
          </a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch, h } from 'vue';
import { message, Modal } from 'ant-design-vue';
import type { TableColumnsType, FormInstance, TreeProps } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';
import {
  SearchOutlined,
  MenuOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import MenuDialog from '@/components/menu.vue';
import ColumnChart1 from './components/ColumnChart1.vue';
import LineChart from './components/LineChart.vue';

// 引入API
import { getCityList } from '@/http/city';
import { getInfo } from '@/http/apis';
import {
  addMonitorResult,
  addUnfinishedReason,
  auditMonitorResult,
  deleteMonitorResult,
  getDeptList,
  getDictList,
  getMonitorResultInfo,
  getTowLevelDept,
  getTreeNodesWithoutOutlets as getTreeNode,
  monitorResultChildFill,
  monitorResultChildInfo,
  monitorResultChildInfoFill,
  monitorResultPage,
  monitorStatistics1,
  monitorStatistics2,
  monitorStatistics3,
  updateMonitorPlan,
  importResult,
} from '@/http/monitor';
import {
  addOrUpdateAuditUser,
  auditUserList,
  getAuditPrivilege,
  getAuditUserPage,
} from '@/http/auditUser';
import { bindData, getBindList, getList } from '@/http/monitor-file';

// 类型定义
interface FormData {
  year: Dayjs | undefined;
  moduleType: string;
  monitorType: string | number;
  siteId: string;
  pointName: string;
  monitorIndex: string;
  monitorStatus: string | number;
  completeType: string | number;
  deptName: string;
  userName: string;
  pageNum: number;
  pageSize: number;
}

interface MonitorForm {
  id?: string;
  year: Dayjs | undefined;
  secondLevelId: string;
  secondLevelName: string;
  threeLevelId: string;
  threeLevelName: string;
  monitorType: number;
  monitorPointType: string;
  stationType: string;
  wastewaterType: string;
  pointName: string;
  province: string;
  city: string;
  area: string;
  pointNameOut: string;
  provinceOut: string;
  cityOut: string;
  areaOut: string;
  monitorNumberOut: string;
  monitorIndexOut: string;
  monitorIndex: string;
  sealNumber: string;
  monitorNumber: string;
  monitorCycle: string;
  monitorWay: string;
  executiveStandard: string;
  minNum: number;
  maxNum: number;
  unit: string;
  fileUrl: string;
  fileName: string;
  emissionDestination: string;
  discharge: string;
  boilerType: string;
  boilerPower: string;
  boilerSize: string;
  dischargeMethodAndDestination: string;
  sensitiveSpot: string;
  distributionId: string;
  distributionNumber: string;
  monitorAnalysis: string;
  pointRecognition: string;
  isPeculiarSmell: string;
  yearDate: string;
  radioValue: string;
  auditContext: string;
  type?: number;
  [key: string]: any;
}

interface DictItem {
  dict_value: string;
  dict_label: string;
}

interface TreeNode {
  id: string;
  key: string;
  siteShortName: string;
  title?: string;
  children?: TreeNode[];
}

interface ReasonForm {
  id?: string;
  reason: string;
}

interface FillForm {
  resultId: string;
  monitorResult: number | undefined;
  monitorUnit: string;
  monitorTime: Dayjs | undefined;
  monitorFileUrl: string;
  monitorFileName: string;
}

interface AuditUserForm {
  id: string;
  userId: string | number;
  auditDeptJson: string[];
}

interface BindForm {
  fileId: string;
  monitorIds: string[];
}

interface FileOption {
  id: string;
  fileName: string;
}

// 响应式数据
const router = useRouter();

// 拖拽相关
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const menuRef = ref<HTMLElement>();

// 界面状态
const loading = ref(false);
const importDialog = ref(false);
const isAdmin = ref(false);
const spzBo = ref(true); // 默认显示，避免表单闪烁
const tbzBo = ref(true); // 默认显示，避免表单闪烁
const isLoading = ref(false);
const selectedRowKeys = ref<string[]>([]);
const isAuditShow = ref(false);
const isShow = ref(true);
const auditDialog = ref(false);
const filterText = ref('');
const isCollapsed = ref(false);
const title = ref('');
const activeName = ref('0');
const monitorType = ref('0');
const auditType = ref('0');

// 表单数据
const formData = reactive<FormData>({
  year: dayjs() as Dayjs,
  moduleType: '',
  monitorType: '',
  siteId: '',
  pointName: '',
  monitorIndex: '',
  monitorStatus: '',
  completeType: '',
  deptName: '',
  userName: '',
  pageNum: 1,
  pageSize: 20,
});

// 表格数据
const auditMonitorResultDate = ref<any[]>([]);
const monitorResultDate = ref<any[]>([]);
const auditUserData = ref<any[]>([]);
const total = ref(0);

// 树形数据
const treeData = ref<TreeNode[]>([]);
const monitorListRef = ref();

// 弹窗状态
const addDialog = ref(false);
const resultDialog = ref(false);
const alterationDialog = ref(false);
const addReasonDialog = ref(false);
const addFillDialog = ref(false);
const abarbeitungDialog = ref(false);
const abarbeitungInfoDialog = ref(false);
const auditUserDialog = ref(false);
const bindDialog = ref(false);
const showFileDialog = ref(false);

// 表单实例
const formRef = ref<FormInstance>();
const addFormRef = ref<FormInstance>();
const reasonFormRef = ref<FormInstance>();
const fillFormRef = ref<FormInstance>();
const auditUserFormRef = ref<FormInstance>();
const bindFormRef = ref<FormInstance>();
const alterationFormRef = ref<FormInstance>();
const abarbeitungFormRef = ref<FormInstance>();
const auditFormRef = ref<FormInstance>();

// 表单数据
const form = reactive<MonitorForm>({
  year: undefined,
  secondLevelId: '',
  secondLevelName: '',
  threeLevelId: '',
  threeLevelName: '',
  monitorType: 0,
  monitorPointType: '',
  stationType: '',
  wastewaterType: '',
  pointName: '',
  province: '',
  city: '',
  area: '',
  pointNameOut: '',
  provinceOut: '',
  cityOut: '',
  areaOut: '',
  monitorNumberOut: '',
  monitorIndexOut: '',
  monitorIndex: '',
  sealNumber: '',
  monitorNumber: '',
  monitorCycle: '',
  monitorWay: '',
  executiveStandard: '',
  minNum: 0,
  maxNum: 0,
  unit: '',
  fileUrl: '',
  fileName: '',
  emissionDestination: '',
  discharge: '',
  boilerType: '',
  boilerPower: '',
  boilerSize: '',
  dischargeMethodAndDestination: '',
  sensitiveSpot: '',
  distributionId: '',
  distributionNumber: '',
  monitorAnalysis: '',
  pointRecognition: '',
  isPeculiarSmell: '',
  yearDate: '',
  radioValue: '',
  auditContext: '',
});

// 表单验证规则
const rules = {
  year: [{ required: true, message: '请选择年份', trigger: 'blur' }],
  secondLevelId: [
    { required: true, message: '请选择二级单位', trigger: 'blur' },
  ],
  threeLevelId: [
    { required: true, message: '请选择三级单位', trigger: 'blur' },
  ],
  monitorType: [{ required: true, message: '请选择监测类型', trigger: 'blur' }],
  pointName: [{ required: true, message: '请输入点位名称', trigger: 'blur' }],
  province: [{ required: true, message: '请选择地理位置', trigger: 'blur' }],
  monitorIndex: [
    { required: true, message: '请输入监测指标', trigger: 'blur' },
  ],
  monitorNumber: [
    { required: true, message: '请输入监测次数', trigger: 'blur' },
  ],
  pointNameOut: [
    { required: true, message: '请输入点位名称', trigger: 'blur' },
  ],
  provinceOut: [{ required: true, message: '请选择地理位置', trigger: 'blur' }],
  monitorIndexOut: [
    { required: true, message: '请输入监测指标', trigger: 'blur' },
  ],
  monitorNumberOut: [
    { required: true, message: '请输入监测次数', trigger: 'blur' },
  ],
  monitorCycle: [
    { required: true, message: '请选择监测周期', trigger: 'blur' },
  ],
  monitorWay: [{ required: true, message: '请选择监测方式', trigger: 'blur' }],
  executiveStandard: [
    { required: true, message: '请选择执行标准', trigger: 'blur' },
  ],
  minNum: [{ required: true, message: '请输入限值最小值', trigger: 'blur' }],
  maxNum: [{ required: true, message: '请输入限值最大值', trigger: 'blur' }],
  unit: [{ required: true, message: '请选择单位', trigger: 'blur' }],
  boilerPower: [{ required: true, message: '请选择功率', trigger: 'blur' }],
  dischargeMethodAndDestination: [
    { required: true, message: '请选择排放方式及去向', trigger: 'blur' },
  ],
  monitorAnalysis: [
    { required: true, message: '请输入监测分析方法及依据', trigger: 'blur' },
  ],
  pointRecognition: [
    { required: true, message: '请输入点位确认监测依据', trigger: 'blur' },
  ],
  sealNumber: [{ required: true, message: '请输入密封点数', trigger: 'blur' }],
  wastewaterType: [
    { required: true, message: '请选择废水类型', trigger: 'blur' },
  ],
  emissionDestination: [
    { required: true, message: '请输入排放去向', trigger: 'blur' },
  ],
  discharge: [{ required: true, message: '请输入排放量', trigger: 'blur' }],
  monitorPointType: [
    { required: true, message: '请选择监测点位类型', trigger: 'blur' },
  ],
  stationType: [{ required: true, message: '请选择场站类型', trigger: 'blur' }],
  sensitiveSpot: [
    { required: true, message: '请选择厂界/敏感点', trigger: 'blur' },
  ],
  area: [{ required: true, message: '请选择地理位置', trigger: 'blur' }],
  isPeculiarSmell: [
    {
      required: true,
      message: '请选择是否通过无异味企业验收',
      trigger: 'blur',
    },
  ],
  radioValue: [{ required: true, message: '请选择是否通过', trigger: 'blur' }],
  auditContext: [{ required: true, message: '请输入备注', trigger: 'blur' }],
} as any;

// 字典数据
const monitorCycles = ref<DictItem[]>([]);
const monitorWays = ref<DictItem[]>([]);
const executiveStandards = ref<DictItem[]>([]);
const monitorMethodsBases = ref<DictItem[]>([]);
const dischargeMethodAndDestinations = ref<DictItem[]>([]);
const boilerPowerTypes = ref<DictItem[]>([]);
const wastewaterTypes = ref<DictItem[]>([]);
const monitorPointTypes = ref<DictItem[]>([]);
const monitorIndexs = ref<DictItem[]>([]);
const stationTypes = ref<DictItem[]>([]);
const unitTypes = ref<DictItem[]>([]);
const provinces = ref<any[]>([]);
const citys = ref<any[]>([]);
const areas = ref<any[]>([]);
const towDeptList = ref<any[]>([]);
const threeDeptList = ref<any[]>([]);

// 文件上传
const token = ref('Bearer a7696874-56be-4dcd-984d-74c6b6ae3323');
const fileList = ref<any[]>([]);
const importFileList = ref<any[]>([]);

// 统计数据
const statisticsData1 = ref<any[]>([]);
const statisticsData2 = ref<any[]>([]);
const statisticsData3 = ref<any[]>([]);

// 弹窗表单数据
const reasonForm = reactive<ReasonForm>({
  reason: '',
});

const fillForm = reactive<FillForm>({
  resultId: '',
  monitorResult: undefined,
  monitorUnit: '',
  monitorTime: undefined,
  monitorFileUrl: '',
  monitorFileName: '',
});

const auditUserForm = reactive<AuditUserForm>({
  id: '',
  userId: '',
  auditDeptJson: [],
});

const bindForm = reactive<BindForm>({
  fileId: '',
  monitorIds: [],
});

// 变更方案表单数据
interface AlterationForm {
  id?: string;
  alterationReason: string;
  alterationFileUrl: string;
  alterationFileName: string;
  alterationMonitorNumber: string;
  alterationMonitorCycle: string;
  alterationTakeEffect: Dayjs | undefined;
}

const alterationForm = reactive<AlterationForm>({
  alterationReason: '',
  alterationFileUrl: '',
  alterationFileName: '',
  alterationMonitorNumber: '',
  alterationMonitorCycle: '',
  alterationTakeEffect: undefined,
});

// 详情弹窗相关数据
const resultDate = ref<any[]>([]);
const tableData1 = ref<any[]>([]);
const tableData2 = ref<any[]>([]);
const tableData3 = ref<any[]>([]);

// 变更方案文件列表
const alterationFileList = ref<any[]>([]);

// 整改详情监测报告文件列表
const fileList3 = ref<any[]>([]);

// 审批用户相关数据
const userTitle = ref<string>('');
const userList = ref<any[]>([]);
const treeRef = ref();

// 整改表单数据
interface AbarbeitungForm {
  id?: string;
  abarbeitungReason: string;
  abarbeitungMeasure: string;
  lastIsStandards: number | undefined;
  lastMeasure: string;
}

const abarbeitungForm = reactive<AbarbeitungForm>({
  abarbeitungReason: '',
  abarbeitungMeasure: '',
  lastIsStandards: undefined,
  lastMeasure: '',
});

// 整改详情表单数据
interface AbarbeitungInfoForm {
  qhMonitorPlan?: any;
  qhMonitorResult?: any;
  qhMonitorResultChild?: any;
}

const abarbeitungInfoForm = reactive<AbarbeitungInfoForm>({
  qhMonitorPlan: {},
  qhMonitorResult: {},
  qhMonitorResultChild: {},
});

// 详情弹窗表格列定义
const detailResultColumns: TableColumnsType = [
  {
    title: '监测时间',
    dataIndex: 'monitorTime',
    key: 'monitorTime',
    align: 'center',
  },
  {
    title: '监测结果',
    dataIndex: 'monitorResult',
    key: 'monitorResult',
    align: 'center',
  },
  {
    title: '是否超标',
    dataIndex: 'isStandards',
    key: 'isStandards',
    align: 'center',
    customRender: ({ record }) => {
      return record.isStandards === 0 ? '未超标' : '超标';
    },
  },
];

const reasonColumns: TableColumnsType = [
  {
    title: '时间',
    dataIndex: 'unfinishedReason',
    key: 'unfinishedReason',
    align: 'center',
  },
  {
    title: '未完成原因说明',
    dataIndex: 'unfinishedDate',
    key: 'unfinishedDate',
    align: 'center',
  },
];

const detailAuditColumns: TableColumnsType = [
  {
    title: '审批人',
    dataIndex: 'auditUserName',
    key: 'auditUserName',
    align: 'center',
  },
  {
    title: '审批时间',
    dataIndex: 'auditTime',
    key: 'auditTime',
    align: 'center',
  },
  {
    title: '审批结果',
    dataIndex: 'type',
    key: 'type',
    align: 'center',
    customRender: ({ record }) => {
      const statusMap: { [key: number]: string } = {
        0: '待提交',
        1: '审批中',
        2: '审批不通过',
        3: '审批通过',
      };
      return statusMap[record.type] || '未知';
    },
  },
  {
    title: '原因',
    dataIndex: 'auditContext',
    key: 'auditContext',
    align: 'center',
  },
];

const alterationTableColumns: TableColumnsType = [
  {
    title: '变更原因',
    dataIndex: 'alterationReason',
    key: 'alterationReason',
    align: 'center',
  },
  {
    title: '变更审批表',
    dataIndex: 'alterationFileName',
    key: 'alterationFileName',
    align: 'center',
    customRender: ({ record }) => {
      if (record.alterationFileName) {
        return h(
          'a',
          {
            href: record.alterationFileUrl,
            target: '_blank',
          },
          record.alterationFileName
        );
      }
      return '-';
    },
  },
  {
    title: '监测次数',
    dataIndex: 'alterationMonitorNumber',
    key: 'alterationMonitorNumber',
    align: 'center',
  },
  {
    title: '监测周期',
    dataIndex: 'alterationMonitorCycle',
    key: 'alterationMonitorCycle',
    align: 'center',
  },
  {
    title: '生效时间',
    dataIndex: 'alterationTakeEffect',
    key: 'alterationTakeEffect',
    align: 'center',
  },
];

const completionColumns: TableColumnsType = [
  {
    title: '监测结果',
    dataIndex: 'monitorResult',
    key: 'monitorResult',
    align: 'center',
  },
  {
    title: '监测时间',
    dataIndex: 'monitorTime',
    key: 'monitorTime',
    align: 'center',
  },
  {
    title: '监测报告',
    dataIndex: 'monitorFileName',
    key: 'monitorFileName',
    align: 'center',
    customRender: ({ record }) => {
      if (record.monitorFileName) {
        return h(
          'a',
          {
            href: record.monitorFileUrl,
            target: '_blank',
          },
          record.monitorFileName
        );
      }
      return '-';
    },
  },
  {
    title: '填报时间',
    dataIndex: 'createDate',
    key: 'createDate',
    align: 'center',
  },
  {
    title: '是否超标',
    dataIndex: 'isStandards',
    key: 'isStandards',
    align: 'center',
    customRender: ({ record }) => {
      return record.isStandards === 0 ? '未超标' : '超标';
    },
  },
  {
    title: '操作',
    key: 'action',
    align: 'center',
    width: 180,
    customRender: ({ record }) => {
      if (record.isStandards === 0) {
        // 未超标时显示灰色不可点击的整改按钮
        return h(
          'a-button',
          {
            type: 'link',
            size: 'small',
            style: { color: '#AAAAAA' },
            disabled: true,
          },
          '整改'
        );
      } else if (record.isStandards === 1) {
        // 超标时根据lastIsStandards判断显示整改还是整改详情
        if (
          record.lastIsStandards === null ||
          record.lastIsStandards === '' ||
          record.lastIsStandards === undefined
        ) {
          return h(
            'a-button',
            {
              type: 'link',
              size: 'small',
              onClick: () => abarbeitungHandle(record),
            },
            '整改'
          );
        } else {
          return h(
            'a-button',
            {
              type: 'link',
              size: 'small',
              onClick: () => abarbeitungInfoHandle(record),
            },
            '整改详情'
          );
        }
      }
      return '-';
    },
  },
];

// 其他表单验证规则
const reasonFormRules = {
  reason: [{ required: true, message: '请输入原因', trigger: 'blur' }],
} as any;

const fillRules = {
  monitorResult: [
    { required: true, message: '请输入监测结果', trigger: 'blur' },
  ],
  monitorUnit: [{ required: true, message: '请选择监测单位', trigger: 'blur' }],
  monitorTime: [{ required: true, message: '请选择监测时间', trigger: 'blur' }],
} as any;

const auditUserFormRules = {
  userId: [{ required: true, message: '请选择用户', trigger: 'blur' }],
  auditDeptJson: [{ required: true, message: '请选择部门', trigger: 'blur' }],
} as any;

const alterationRules = {
  alterationReason: [
    { required: true, message: '请输入变更原因', trigger: 'blur' },
  ],
  alterationMonitorNumber: [
    { required: true, message: '请输入监测次数', trigger: 'blur' },
  ],
  alterationMonitorCycle: [
    { required: true, message: '请选择监测周期', trigger: 'blur' },
  ],
  alterationTakeEffect: [
    { required: true, message: '请选择生效时间', trigger: 'blur' },
  ],
} as any;

const abarbeitungRules = {
  abarbeitungReason: [
    { required: true, message: '请输入超标原因', trigger: 'blur' },
  ],
  abarbeitungMeasure: [
    { required: true, message: '请输入整改措施', trigger: 'blur' },
  ],
  lastIsStandards: [{ required: true, message: '请选择', trigger: 'blur' }],
  lastMeasure: [{ required: true, message: '请输入下步措施', trigger: 'blur' }],
} as any;

// 文件相关数据
const fileOptions = ref<FileOption[]>([]);
const fileColumns = [
  { title: '序号', key: 'index', width: 60 },
  { title: '文件名称', dataIndex: 'fileName', key: 'fileName' },
  { title: '文件类型', dataIndex: 'fileType', key: 'fileType' },
  { title: '上传时间', dataIndex: 'uploadTime', key: 'uploadTime' },
  { title: '操作', key: 'action', width: 120 },
];

// 基础表格列
const baseColumns: TableColumnsType = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  { title: '年份', dataIndex: 'year', key: 'year', width: 80, align: 'center' },
  {
    title: '二级单位',
    dataIndex: 'secondLevelName',
    key: 'secondLevelName',
    width: 150,
    align: 'center',
  },
  {
    title: '三级单位',
    dataIndex: 'threeLevelName',
    key: 'threeLevelName',
    width: 150,
    align: 'center',
  },
];

// 动态计算的方案表格列
const planColumns = computed<TableColumnsType>(() => {
  const columns: TableColumnsType = [...baseColumns];

  // 监测点位类型（噪声、地表水、土壤、环境空气）
  if (['4', '5', '6', '7'].includes(monitorType.value)) {
    columns.push({
      title: '监测点位类型',
      dataIndex: 'monitorPointType',
      key: 'monitorPointType',
      width: 150,
      align: 'center',
    });
  }

  // 场站类型（噪声）
  if (monitorType.value === '4') {
    columns.push({
      title: '场站类型',
      dataIndex: 'stationType',
      key: 'stationType',
      width: 150,
      align: 'center',
    });
  }

  // 基本信息列
  columns.push(
    {
      title: '点位名称',
      dataIndex: 'pointName',
      key: 'pointName',
      width: 150,
      align: 'center',
    },
    {
      title: '地理位置',
      dataIndex: 'province',
      key: 'province',
      width: 150,
      align: 'center',
    }
  );

  // 锅炉相关列（有组织废气）
  if (monitorType.value === '0') {
    columns.push(
      {
        title: '锅炉类型',
        dataIndex: 'boilerType',
        key: 'boilerType',
        width: 150,
        align: 'center',
      },
      {
        title: '功率',
        dataIndex: 'boilerPower',
        key: 'boilerPower',
        width: 150,
        align: 'center',
      },
      {
        title: '规模',
        dataIndex: 'boilerSize',
        key: 'boilerSize',
        width: 150,
        align: 'center',
      },
      {
        title: '排放方式及去向',
        dataIndex: 'dischargeMethodAndDestination',
        key: 'dischargeMethodAndDestination',
        width: 150,
        align: 'center',
      }
    );
  }

  // 废水相关列
  if (monitorType.value === '3') {
    columns.push(
      {
        title: '排放去向',
        dataIndex: 'emissionDestination',
        key: 'emissionDestination',
        width: 150,
        align: 'center',
      },
      {
        title: '排放量',
        dataIndex: 'discharge',
        key: 'discharge',
        width: 150,
        align: 'center',
      }
    );
  }

  // 监测指标
  columns.push({
    title: '监测指标',
    dataIndex: 'monitorIndex',
    key: 'monitorIndex',
    width: 150,
    align: 'center',
  });

  // 密封点数（泄露检测）
  if (monitorType.value === '2') {
    columns.push({
      title: '密封点数',
      dataIndex: 'sealNumber',
      key: 'sealNumber',
      width: 150,
      align: 'center',
    });
  }

  // 布点编号（噪声）
  if (monitorType.value === '4') {
    columns.push({
      title: '布点编号',
      dataIndex: 'distributionId',
      key: 'distributionId',
      width: 150,
      align: 'center',
    });
  }

  // 布点数量（地表水、土壤、环境空气）
  if (['5', '6', '7'].includes(monitorType.value)) {
    columns.push({
      title: '布点数量',
      dataIndex: 'distributionNumber',
      key: 'distributionNumber',
      width: 150,
      align: 'center',
    });
  }

  // 通用监测信息
  columns.push(
    {
      title: '监测次数',
      dataIndex: 'monitorNumber',
      key: 'monitorNumber',
      width: 150,
      align: 'center',
    },
    {
      title: '监测周期',
      dataIndex: 'monitorCycle',
      key: 'monitorCycle',
      width: 150,
      align: 'center',
    },
    {
      title: '每次监测方式',
      dataIndex: 'monitorWay',
      key: 'monitorWay',
      width: 150,
      align: 'center',
    },
    {
      title: '执行标准',
      dataIndex: 'executiveStandard',
      key: 'executiveStandard',
      width: 150,
      align: 'center',
    }
  );

  // 限值（除无组织废气外）
  if (monitorType.value !== '1') {
    columns.push({
      title: '限值',
      key: 'limitValue',
      width: 150,
      align: 'center',
      customRender: ({ record }) =>
        `${record.minNum || 0}-${record.maxNum || 0}`,
    });
  }

  // 噪声专用限值
  if (monitorType.value === '4') {
    columns.push(
      {
        title: '昼间限值最大值',
        dataIndex: 'minNum',
        key: 'minNum',
        width: 150,
        align: 'center',
      },
      {
        title: '夜间限值最大值',
        dataIndex: 'maxNum',
        key: 'maxNum2',
        width: 150,
        align: 'center',
      }
    );
  }

  // 单位和分析方法
  columns.push(
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 150,
      align: 'center',
    },
    {
      title: '监测分析方法及依据',
      dataIndex: 'monitorAnalysis',
      key: 'monitorAnalysis',
      width: 150,
      align: 'center',
    }
  );

  // 点位确定监测依据（除有组织废气和泄露检测外）
  if (!['0', '2'].includes(monitorType.value)) {
    columns.push({
      title: '点位确定监测依据',
      dataIndex: 'pointRecognition',
      key: 'pointRecognition',
      width: 150,
      align: 'center',
    });
  }

  // 状态和完成情况
  columns.push(
    { title: '状态', key: 'status', width: 150, align: 'center' },
    { title: '完成情况', key: 'completion', width: 250, align: 'center' },
    {
      title: '操作',
      key: 'action',
      width: 240,
      align: 'center',
      fixed: 'right',
    }
  );

  return columns;
});

const auditColumns: TableColumnsType = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  { title: '年份', dataIndex: 'year', key: 'year', align: 'center' },
  {
    title: '二级单位',
    dataIndex: 'secondLevelName',
    key: 'secondLevelName',
    width: 150,
    align: 'center',
  },
  {
    title: '三级单位',
    dataIndex: 'threeLevelName',
    key: 'threeLevelName',
    width: 150,
    align: 'center',
  },
  { title: '监测类型', key: 'monitorType', align: 'center' },
  {
    title: '点位名称',
    dataIndex: 'pointName',
    key: 'pointName',
    width: 150,
    align: 'center',
  },
  {
    title: '监测指标',
    dataIndex: 'monitorIndex',
    key: 'monitorIndex',
    align: 'center',
  },
  {
    title: '监测频次',
    dataIndex: 'monitorCycle',
    key: 'monitorCycle',
    align: 'center',
  },
  {
    title: '执行标准',
    dataIndex: 'executiveStandard',
    key: 'executiveStandard',
    align: 'center',
  },
  { title: '限值', key: 'limitValue', align: 'center' },
  { title: '限值单位', dataIndex: 'unit', key: 'unit', align: 'center' },
  { title: '操作', key: 'action', width: 200, align: 'center', fixed: 'right' },
];

// 动态计算的结果表格列
const resultColumns = computed<TableColumnsType>(() => {
  const columns: TableColumnsType = [...baseColumns];

  // 监测点位类型（噪声、地表水、土壤、环境空气）
  if (['4', '5', '6', '7'].includes(monitorType.value)) {
    columns.push({
      title: '监测点位类型',
      dataIndex: 'monitorPointType',
      key: 'monitorPointType',
      width: 150,
      align: 'center',
    });
  }

  // 场站类型（噪声）
  if (monitorType.value === '4') {
    columns.push({
      title: '场站类型',
      dataIndex: 'stationType',
      key: 'stationType',
      width: 150,
      align: 'center',
    });
  }

  // 基本信息列
  columns.push(
    {
      title: '点位名称',
      dataIndex: 'pointName',
      key: 'pointName',
      width: 150,
      align: 'center',
    },
    {
      title: '地理位置',
      dataIndex: 'province',
      key: 'province',
      width: 150,
      align: 'center',
    }
  );

  // 锅炉相关列（有组织废气）
  if (monitorType.value === '0') {
    columns.push(
      {
        title: '锅炉类型',
        dataIndex: 'boilerType',
        key: 'boilerType',
        width: 150,
        align: 'center',
      },
      {
        title: '功率',
        dataIndex: 'boilerPower',
        key: 'boilerPower',
        width: 150,
        align: 'center',
      },
      {
        title: '规模',
        dataIndex: 'boilerSize',
        key: 'boilerSize',
        width: 150,
        align: 'center',
      },
      {
        title: '排放方式及去向',
        dataIndex: 'dischargeMethodAndDestination',
        key: 'dischargeMethodAndDestination',
        width: 150,
        align: 'center',
      }
    );
  }

  // 废水相关列
  if (monitorType.value === '3') {
    columns.push(
      {
        title: '排放去向',
        dataIndex: 'emissionDestination',
        key: 'emissionDestination',
        width: 150,
        align: 'center',
      },
      {
        title: '排放量',
        dataIndex: 'discharge',
        key: 'discharge',
        width: 150,
        align: 'center',
      }
    );
  }

  // 监测指标
  columns.push({
    title: '监测指标',
    dataIndex: 'monitorIndex',
    key: 'monitorIndex',
    width: 150,
    align: 'center',
  });

  // 密封点数（泄露检测）
  if (monitorType.value === '2') {
    columns.push({
      title: '密封点数',
      dataIndex: 'sealNumber',
      key: 'sealNumber',
      width: 150,
      align: 'center',
    });
  }

  // 布点编号（噪声）
  if (monitorType.value === '4') {
    columns.push({
      title: '布点编号',
      dataIndex: 'distributionId',
      key: 'distributionId',
      width: 150,
      align: 'center',
    });
  }

  // 布点数量（地表水、土壤、环境空气）
  if (['5', '6', '7'].includes(monitorType.value)) {
    columns.push({
      title: '布点数量',
      dataIndex: 'distributionNumber',
      key: 'distributionNumber',
      width: 150,
      align: 'center',
    });
  }

  // 通用监测信息
  columns.push(
    {
      title: '监测次数',
      dataIndex: 'monitorNumber',
      key: 'monitorNumber',
      width: 150,
      align: 'center',
    },
    {
      title: '监测周期',
      dataIndex: 'monitorCycle',
      key: 'monitorCycle',
      width: 150,
      align: 'center',
    },
    {
      title: '每次监测方式',
      dataIndex: 'monitorWay',
      key: 'monitorWay',
      width: 150,
      align: 'center',
    },
    {
      title: '执行标准',
      dataIndex: 'executiveStandard',
      key: 'executiveStandard',
      width: 150,
      align: 'center',
    }
  );

  // 限值（除无组织废气外）
  if (monitorType.value !== '1') {
    columns.push({
      title: '限值',
      key: 'limitValue',
      width: 150,
      align: 'center',
      customRender: ({ record }) =>
        `${record.minNum || 0}-${record.maxNum || 0}`,
    });
  }

  // 噪声专用限值
  if (monitorType.value === '4') {
    columns.push(
      {
        title: '昼间限值最大值',
        dataIndex: 'minNum',
        key: 'minNum',
        width: 150,
        align: 'center',
      },
      {
        title: '夜间限值最大值',
        dataIndex: 'maxNum',
        key: 'maxNum2',
        width: 150,
        align: 'center',
      }
    );
  }

  // 单位和分析方法
  columns.push(
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 150,
      align: 'center',
    },
    {
      title: '监测分析方法及依据',
      dataIndex: 'monitorAnalysis',
      key: 'monitorAnalysis',
      width: 150,
      align: 'center',
    }
  );

  // 点位确定监测依据（除有组织废气和泄露检测外）
  if (!['0', '2'].includes(monitorType.value)) {
    columns.push({
      title: '点位确定监测依据',
      dataIndex: 'pointRecognition',
      key: 'pointRecognition',
      width: 150,
      align: 'center',
    });
  }

  // 结果表格特有列
  columns.push(
    { title: '完成情况', key: 'completeType', width: 250, align: 'center' },
    {
      title: '未完成原因',
      dataIndex: 'unfinishedReason',
      key: 'unfinishedReason',
      width: 150,
      align: 'center',
    },
    { title: '数据超标情况', key: 'outLimit', width: 150, align: 'center' },
    {
      title: '获得监测数据总数',
      dataIndex: 'monitorLimit',
      key: 'monitorLimit',
      width: 150,
      align: 'center',
    },
    { title: '监测报告', key: 'fileNumber', width: 150, align: 'center' },
    {
      title: '操作',
      key: 'action',
      width: 240,
      align: 'center',
      fixed: 'right',
    }
  );

  return columns;
});

const auditUserColumns: TableColumnsType = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  { title: '单位', key: 'deptName', align: 'center' },
  { title: '姓名', dataIndex: 'userName', key: 'userName', align: 'center' },
  {
    title: '审批数据权限',
    dataIndex: 'auditDeptJsonStr',
    key: 'auditDeptJsonStr',
    align: 'center',
  },
  { title: '操作', key: 'action', align: 'center' },
];

const outLimitColumns: TableColumnsType = [
  { title: '序号', key: 'index', width: 60 },
  { title: '监测结果', dataIndex: 'monitorResult', key: 'monitorResult' },
  { title: '监测时间', dataIndex: 'monitorTime', key: 'monitorTime' },
];

// 方法定义
const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const menu = menuRef.value?.getBoundingClientRect();
  if (!menu) return;

  const onMouseMove = (e: MouseEvent) => {
    const newX = e.clientX - menu.width / 2;
    const newY = e.clientY - menu.height / 2;

    // 边界限制
    x.value = Math.max(0, Math.min(newX, window.innerWidth - menu.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menu.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const onSelectChange = (
  selectedKeys: (string | number)[],
  selectedRows: any[]
) => {
  selectedRowKeys.value = selectedKeys.map(key => String(key));
};

const handleNodeClick = (selectedKeys: (string | number)[], info: any) => {
  if (selectedKeys.length > 0) {
    formData.siteId = String(selectedKeys[0]);
    search();
  }
};

const search = async () => {
  let yyyy = '';
  if (formData.year) {
    yyyy = dayjs.isDayjs(formData.year)
      ? formData.year.year().toString()
      : String(formData.year);
  }

  const searchForm = {
    year: yyyy,
    moduleType: activeName.value,
    monitorType: monitorType.value,
    siteId: formData.siteId,
    pointName: formData.pointName,
    monitorIndex: formData.monitorIndex,
    type: formData.monitorStatus,
    deptName: formData.deptName,
    userName: formData.userName,
    pageNum: formData.pageNum,
    pageSize: formData.pageSize,
    auditType: '',
    completeType: '',
  };

  if (activeName.value === '0' || activeName.value === '2') {
    await getMonitorResultDate(searchForm);
  }
  if (activeName.value === '1') {
    searchForm.monitorType = '';
    searchForm.auditType = auditType.value;
    await getAuditMonitorResultDate(searchForm);
  }
  if (activeName.value === '2') {
    searchForm.completeType = String(formData.completeType);
  }
  if (activeName.value === '3') {
    await Promise.all([
      getMonitorStatistics1(),
      getMonitorStatistics2(),
      getMonitorStatistics3(),
    ]);
  }
  if (activeName.value === '4') {
    await auditUserPage(searchForm);
  }
};

const getMonitorResultDate = async (searchForm: any) => {
  try {
    const res = await monitorResultPage(searchForm);
    monitorResultDate.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.error('获取监测结果数据失败:', error);
  }
};

const getAuditMonitorResultDate = async (searchForm: any) => {
  try {
    const res = await monitorResultPage(searchForm);
    auditMonitorResultDate.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.error('获取审批数据失败:', error);
  }
};

const handleClick = (key: string | number) => {
  const keyStr = String(key);
  resetFormData();
  activeName.value = keyStr;
  search();

  if (keyStr === '4') {
    isCollapsed.value = true;
    isShow.value = false;
  } else {
    isCollapsed.value = false;
    isShow.value = true;
  }
};

const handleClick1 = () => {
  resetFormData();
  search();
};

const handleClick2 = () => {
  resetFormData();
  search();
};

const resetFormData = () => {
  Object.assign(formData, {
    year: dayjs(),
    moduleType: activeName.value,
    monitorType: '',
    siteId: '',
    pointName: '',
    monitorIndex: '',
    monitorStatus: '',
    completeType: '',
    deptName: '',
    userName: '',
    pageNum: 1,
    pageSize: 20,
  });
};

const handleCurrentChange = (page: number, pageSize: number) => {
  formData.pageNum = page || 1;
  formData.pageSize = pageSize;
  search();
};

const resetForm = () => {
  // 重置查询表单
  formRef.value?.resetFields();
  resetFormData();
  search();
};

const resetAddForm = () => {
  // 重置新增表单
  Object.assign(form, {
    year: undefined,
    secondLevelId: '',
    secondLevelName: '',
    threeLevelId: '',
    threeLevelName: '',
    monitorType: 0,
    monitorPointType: '',
    stationType: '',
    wastewaterType: '',
    pointName: '',
    province: '',
    city: '',
    area: '',
    pointNameOut: '',
    provinceOut: '',
    cityOut: '',
    areaOut: '',
    monitorNumberOut: '',
    monitorIndexOut: '',
    monitorIndex: '',
    sealNumber: '',
    monitorNumber: '',
    monitorCycle: '',
    monitorWay: '',
    executiveStandard: '',
    minNum: 0,
    maxNum: 0,
    unit: '',
    fileUrl: '',
    fileName: '',
    emissionDestination: '',
    discharge: '',
    boilerType: '',
    boilerPower: '',
    boilerSize: '',
    dischargeMethodAndDestination: '',
    sensitiveSpot: '',
    distributionId: '',
    distributionNumber: '',
    monitorAnalysis: '',
    pointRecognition: '',
    isPeculiarSmell: '',
    yearDate: '',
    radioValue: '',
    auditContext: '',
  });
  fileList.value = [];
};

const getTreeNanfang = async () => {
  try {
    const res = await getTreeNode();
    treeData.value = res.data;
  } catch (error) {
    console.error('获取树形数据失败:', error);
  }
};

const exportXlsx = () => {
  // 导出逻辑
  message.success('导出成功');
};

const bindFileHandle = () => {
  if (selectedRowKeys.value.length === 0) {
    message.error('请选择绑定对象！');
  }
  // 绑定文件逻辑
};

const addHandle = () => {
  addDialog.value = true;
  title.value = '新增方案';
  resetAddForm();
  getAllDate(1, 0);
};

const updateHandle = (record: any) => {
  addDialog.value = true;
  title.value = '编辑方案';
  getAllDate(1, 0);
  getMonitorResultInfoHandle(record.id);
};

// 详情弹窗
const handleInfoClick = (record: any) => {
  resultDialog.value = true;
  getAllDate(1, 0);
  getMonitorResultInfoHandle(record.id);
};

const getMonitorResultInfoHandle = async (id: string, isAudit = false) => {
  try {
    const res = await getMonitorResultInfo(id);
    if (res.data) {
      Object.assign(form, res.data);
      const dateString = res.data.year + '-01-01T00:00:00Z';
      form.year = dayjs(dateString);
      await getThreeDeptList(form.secondLevelId);
      await cityList(2, form.province);
      await cityList(3, form.city);

      if (res.data.fileName) {
        fileList.value = [
          {
            uid: '1',
            name: res.data.fileName,
            url: res.data.fileUrl,
            status: 'done',
          },
        ];
      } else {
        fileList.value = [];
      }

      // 如果是审批弹窗，清空审批意见
      if (isAudit) {
        form.auditContext = '';
      }

      // 设置详情弹窗的表格数据
      if (resultDialog.value) {
        resultDate.value = res.data.qhMonitorResultChildList || [];
        tableData1.value = [
          {
            unfinishedReason: res.data.unfinishedReason,
            unfinishedDate: res.data.unfinishedDate,
          },
        ];
        tableData2.value = [
          {
            auditUserName: res.data.auditUserName,
            auditTime: res.data.auditTime,
            type: res.data.type,
            auditContext: res.data.auditContext,
          },
        ];
        tableData3.value = [
          {
            alterationReason: res.data.alterationReason,
            alterationFileName: res.data.alterationFileName,
            alterationFileUrl: res.data.alterationFileUrl,
            alterationMonitorNumber: res.data.alterationMonitorNumber,
            alterationMonitorCycle: res.data.alterationMonitorCycle,
            alterationTakeEffect: res.data.alterationTakeEffect,
          },
        ];
      }
    }
  } catch (error) {
    console.error('获取监测结果信息失败:', error);
  }
};

const deleteHandle = async (record: any) => {
  try {
    await new Promise((resolve, reject) => {
      Modal.confirm({
        title: '确认删除',
        content: '是否确定删除该条数据？',
        onOk: resolve,
        onCancel: reject,
      });
    });

    const res = await deleteMonitorResult(record.id);
    if (res.data) {
      message.success('删除成功');
      search();
    } else {
      message.error('删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }
};

const saveHandle = async (type: number) => {
  try {
    await addFormRef.value?.validate();
    const formData = { ...form } as any;
    if (formData.year) {
      formData.year = formData.year.year();
    }
    formData.type = type;
    const res = await addMonitorResult(formData);
    if (res.data) {
      message.success('保存成功');
      addDialog.value = false;
      search();
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  }
};

const alterationHandle = (record: any) => {
  alterationDialog.value = true;
  // 重置变更方案表单
  Object.assign(alterationForm, {
    id: record.id,
    alterationReason: '',
    alterationFileUrl: '',
    alterationFileName: '',
    alterationMonitorNumber: '',
    alterationMonitorCycle: '',
    alterationTakeEffect: undefined,
  });
  alterationFileList.value = [];
  // 获取监测周期字典数据
  dictListDate('monitor_cycle');
};

const handleReasonClick = (record: any) => {
  addReasonDialog.value = true;
  // 设置原因说明表单数据
  Object.assign(reasonForm, {
    id: record.id,
    reason: record.unfinishedReason || '',
  });
};

const handleFillClick = (record: any) => {
  addFillDialog.value = true;
  // 重置填报表单
  Object.assign(fillForm, {
    resultId: record.id,
    monitorResult: undefined,
    monitorUnit: '',
    monitorTime: undefined,
    monitorFileUrl: '',
    monitorFileName: '',
  });
  fileList3.value = [];
  // 获取单位字典数据
  dictListDate('unit');
};

const showFileHandle = (record: any) => {
  showFileDialog.value = true;
  // 显示文件逻辑
};

const addAuditUserHandle = (record: any) => {
  auditUserDialog.value = true;

  if (record === null) {
    // 新增模式
    userTitle.value = '新增';
    Object.assign(auditUserForm, {
      id: '',
      userId: '',
      auditDeptJson: [],
    });
  } else {
    // 编辑模式
    userTitle.value = '修改';
    Object.assign(auditUserForm, {
      id: record.id,
      userId: Number(record.userId),
      auditDeptJson: JSON.parse(record.auditDeptJson || '[]'),
    });
  }

  // 获取用户列表和部门树数据
  getUserList();
  getAllDate(1, 0);
};

// 审批弹窗处理
const auditHandle = (record: any) => {
  auditDialog.value = true;
  getAllDate(1, 0);
  getMonitorResultInfoHandle(record.id, true);
};

// 审批确认处理
const auditOkHandle = async () => {
  try {
    await auditFormRef.value?.validate();

    // 处理年份格式
    const formData = { ...form } as any;
    if (formData.year && typeof formData.year.getFullYear === 'function') {
      formData.year = formData.year.getFullYear();
    } else if (formData.year && formData.year.$y) {
      formData.year = formData.year.$y;
    }

    // 设置审批状态
    if (formData.radioValue === 1) {
      formData.type = 3; // 审批通过
    } else {
      formData.type = 2; // 审批驳回
    }

    const res = await auditMonitorResult(formData);
    if (res.data) {
      message.success('审批成功');
      auditDialog.value = false;
      search();
    } else {
      message.error('审批失败');
    }
  } catch (error) {
    console.error('审批失败:', error);
    message.error('审批失败');
  }
};

// 保存变更方案
const saveAlterationHandle = async () => {
  try {
    await alterationFormRef.value?.validate();
    const formData = { ...alterationForm } as any;
    if (formData.alterationTakeEffect) {
      formData.alterationTakeEffect =
        formData.alterationTakeEffect.format('YYYY-MM-DD');
    }
    const res = await updateMonitorPlan(formData);
    if (res.data) {
      message.success('保存成功');
      alterationDialog.value = false;
      search();
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    console.error('保存变更方案失败:', error);
    message.error('保存失败');
  }
};

// 变更方案文件上传处理
const handleAlterationUploadChange = (info: any) => {
  if (info.file.status === 'done') {
    alterationForm.alterationFileUrl = info.file.response.data.url;
    alterationForm.alterationFileName = info.file.response.data.name;
    message.success('文件上传成功');
  } else if (info.file.status === 'error') {
    message.error('文件上传失败');
  }
};

const handleAlterationRemove = () => {
  alterationForm.alterationFileUrl = '';
  alterationForm.alterationFileName = '';
};

// 填报文件上传处理
const handleFillUploadChange = (info: any) => {
  if (info.file.status === 'done') {
    fillForm.monitorFileUrl = info.file.response.data.url;
    fillForm.monitorFileName = info.file.response.data.name;
    message.success('文件上传成功');
  } else if (info.file.status === 'error') {
    message.error('文件上传失败');
  }
};

const handleFillRemove = () => {
  fillForm.monitorFileUrl = '';
  fillForm.monitorFileName = '';
};

// 获取用户列表
const getUserList = async () => {
  try {
    const res = await auditUserList();
    if (res.data) {
      userList.value = res.data;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

// 树形选择变化处理
const handleCheckChange = (checkedKeys: any) => {
  if (Array.isArray(checkedKeys)) {
    auditUserForm.auditDeptJson = checkedKeys.map(key => String(key));
  } else if (checkedKeys.checked) {
    auditUserForm.auditDeptJson = checkedKeys.checked.map((key: any) =>
      String(key)
    );
  }
};

// 保存审批用户
const saveAuditUserHandle = async () => {
  try {
    await auditUserFormRef.value?.validate();
    const formData = {
      ...auditUserForm,
      auditDeptJson: JSON.stringify(auditUserForm.auditDeptJson),
    };
    const res = await addOrUpdateAuditUser(formData);
    if (res.data) {
      message.success('保存成功');
      auditUserDialog.value = false;
      search();
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    console.error('保存审批用户失败:', error);
    message.error('保存失败');
  }
};

// 整改处理
const abarbeitungHandle = (record: any) => {
  abarbeitungDialog.value = true;
  // 重置整改表单
  Object.assign(abarbeitungForm, {
    id: record.id,
    abarbeitungReason: '',
    abarbeitungMeasure: '',
    lastIsStandards: undefined,
    lastMeasure: '',
  });
};

// 整改详情
const abarbeitungInfoHandle = async (record: any) => {
  abarbeitungInfoDialog.value = true;
  try {
    const res = await monitorResultChildInfo(record.id);
    if (res.data) {
      Object.assign(abarbeitungInfoForm, res.data);
    }
  } catch (error) {
    console.error('获取整改详情失败:', error);
  }
};

// 保存整改信息
const saveAbarbeitungHandle = async () => {
  try {
    await abarbeitungFormRef.value?.validate();
    const res = await monitorResultChildFill(abarbeitungForm);
    if (res.data) {
      message.success('保存成功');
      abarbeitungDialog.value = false;
      // 刷新详情弹窗的完成情况数据
      if (resultDialog.value) {
        const resultRes = await getMonitorResultInfo(abarbeitungForm.id);
        if (resultRes.data) {
          resultDate.value = resultRes.data.qhMonitorResultChildList || [];
        }
      }
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    console.error('保存整改信息失败:', error);
    message.error('保存失败');
  }
};

const getAllDate = (type: number, cityId: number) => {
  // 获取字典数据
  dictListDate('monitor_cycle');
  dictListDate('monitor_way');
  dictListDate('executive_standard');
  dictListDate('discharge_method_and_destination');
  dictListDate('wastewater_type');
  dictListDate('monitor_point_type');
  dictListDate('station_type');
  dictListDate('unit');
  dictListDate('boiler_power_type');
  dictListDate('monitor_methods_bases');
  dictListDate('monitor_index');
  cityList(type, cityId);
};

const dictListDate = async (dictType: string) => {
  try {
    const res = await getDictList(dictType);
    const data = res.data.data || res.data;

    switch (dictType) {
      case 'monitor_cycle': // eslint-disable-line
        monitorCycles.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'monitor_way': // eslint-disable-line
        monitorWays.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'discharge_method_and_destination': // eslint-disable-line
        dischargeMethodAndDestinations.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'wastewater_type': // eslint-disable-line
        wastewaterTypes.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'boiler_power_type': // eslint-disable-line
        boilerPowerTypes.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'monitor_point_type': // eslint-disable-line
        monitorPointTypes.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'monitor_index': // eslint-disable-line
        monitorIndexs.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'station_type': // eslint-disable-line
        stationTypes.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'unit': // eslint-disable-line
        unitTypes.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'executive_standard': // eslint-disable-line
        executiveStandards.value = data; // eslint-disable-line
        break; // eslint-disable-line
      case 'monitor_methods_bases': // eslint-disable-line
        monitorMethodsBases.value = data; // eslint-disable-line
        break; // eslint-disable-line
    }
  } catch (error) {
    console.error(`获取字典数据失败: ${dictType}`, error);
  }
};

const beforeUpload = (file: File) => {
  const isAllowedType = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
  ].includes(file.type);
  if (!isAllowedType) {
    message.error('只能上传Excel或CSV文件');
  }
  return isAllowedType;
};

const handleUploadChange = (info: any) => {
  if (info.file.status === 'done') {
    form.fileUrl = info.file.response?.data?.url || '';
    form.fileName = info.file.response?.data?.name || '';
  }
};

const handleRemove = () => {
  form.fileUrl = '';
  form.fileName = '';
};

const getTowDeptList = async () => {
  try {
    const res = await getTowLevelDept();
    towDeptList.value = res.data;
  } catch (error) {
    console.error('获取二级部门失败:', error);
  }
};

const getThreeDeptList = async (deptId: string) => {
  if (!deptId) {
    threeDeptList.value = [];
    return;
  }
  try {
    const res = await getDeptList(deptId);
    threeDeptList.value = res.data;
  } catch (error) {
    console.error('获取三级部门失败:', error);
  }
};

const cityList = async (type: number, cityId: string | number) => {
  try {
    const res = await getCityList({ type, cityId });
    if (type === 1) {
      provinces.value = res.data;
    } else if (type === 2) {
      citys.value = res.data;
    } else {
      areas.value = res.data;
    }
  } catch (error) {
    console.error('获取城市数据失败:', error);
  }
};

const auditUserPage = async (searchForm: any) => {
  try {
    const res = await getAuditUserPage(searchForm);
    auditUserData.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.error('获取审批用户数据失败:', error);
  }
};

const getMonitorStatistics1 = async () => {
  try {
    const res = await monitorStatistics1();
    if (res.data) {
      statisticsData1.value = res.data;
    }
  } catch (error) {
    console.error('获取统计数据1失败:', error);
  }
};

const getMonitorStatistics2 = async () => {
  try {
    const res = await monitorStatistics2();
    if (res.data) {
      statisticsData2.value = res.data;
    }
  } catch (error) {
    console.error('获取统计数据2失败:', error);
  }
};

const getMonitorStatistics3 = async () => {
  try {
    const res = await monitorStatistics3();
    if (res.data) {
      statisticsData3.value = res.data;
    }
  } catch (error) {
    console.error('获取统计数据3失败:', error);
  }
};

const auditPrivilege = async () => {
  try {
    const res = await getAuditPrivilege();
    isAuditShow.value = res.data;
  } catch (error) {
    console.error('获取审批权限失败:', error);
  }
};

const importXlsx = () => {
  importDialog.value = true;
  importFileList.value = [];
};

// 审批相关方法
const auditApprove = async () => {
  try {
    const res = await auditMonitorResult({
      id: form.id,
      auditStatus: 1, // 通过
      auditContext: form.auditContext,
    });
    if (res.data?.code === 200) {
      message.success('审批通过');
      auditDialog.value = false;
      search();
    }
  } catch (error) {
    console.error('审批失败:', error);
    message.error('审批失败');
  }
};

const auditReject = async () => {
  try {
    const res = await auditMonitorResult({
      id: form.id,
      auditStatus: 2, // 驳回
      auditContext: form.auditContext,
    });
    if (res.data?.code === 200) {
      message.success('审批驳回');
      auditDialog.value = false;
      search();
    }
  } catch (error) {
    console.error('审批失败:', error);
    message.error('审批失败');
  }
};

// 原因说明方法
const saveReason = async () => {
  try {
    await reasonFormRef.value?.validate();
    const res = await addUnfinishedReason({
      id: reasonForm.id,
      unfinishedReason: reasonForm.reason,
    });
    if (res.data) {
      message.success('保存成功');
      addReasonDialog.value = false;
      search();
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  }
};

// 填报数据方法
const fillHandle = async () => {
  try {
    await fillFormRef.value?.validate();
    const formData = { ...fillForm } as any;
    if (formData.monitorTime) {
      formData.monitorTime = formData.monitorTime.format('YYYY-MM-DD');
    }
    const res = await monitorResultChildFill(formData);
    if (res.data) {
      message.success('填报成功');
      addFillDialog.value = false;
      search();
    } else {
      message.error('填报失败');
    }
  } catch (error) {
    console.error('填报失败:', error);
    message.error('填报失败');
  }
};

// 文件绑定方法
const bindFile = async () => {
  try {
    const res = await bindData({
      fileId: bindForm.fileId,
      monitorIds: selectedRowKeys.value,
    });
    if (res.data?.code === 200) {
      message.success('绑定成功');
      bindDialog.value = false;
      search();
    }
  } catch (error) {
    console.error('绑定失败:', error);
    message.error('绑定失败');
  }
};

// 文件操作方法
const downloadFile = (record: any) => {
  window.open(record.fileUrl, '_blank');
};

const previewFile = (record: any) => {
  window.open(record.fileUrl, '_blank');
};

const downloadTemplate = () => {
  // 下载模板逻辑
  const templateUrl = '/api/template/monitor-template.xlsx';
  window.open(templateUrl, '_blank');
};

const handleImportChange = (info: any) => {
  if (info.file.status === 'done') {
    importFileList.value = [info.file];
  }
};

const confirmImport = async () => {
  if (importFileList.value.length === 0) {
    message.error('请选择要导入的文件');
    return;
  }

  try {
    const fileUrl = importFileList.value[0].response?.data?.url;
    const res = await importResult(fileUrl);
    if (res.data?.code === 200) {
      message.success('导入成功');
      importDialog.value = false;
      search();
    }
  } catch (error) {
    console.error('导入失败:', error);
    message.error('导入失败');
  }
};

// 监听过滤文本变化
watch(filterText, val => {
  // 树形过滤逻辑
});

// 组件挂载
onMounted(async () => {
  try {
    await Promise.all([
      search(),
      getTreeNanfang(),
      getTowDeptList(),
      auditPrivilege(),
    ]);

    dictListDate('monitor_index');

    // 获取用户信息和权限
    try {
      const res = await getInfo();
      spzBo.value = false;
      tbzBo.value = false;
      activeName.value = '0';
      isAdmin.value = false;

      if ((res as any).user?.userId === 2411) {
        spzBo.value = true;
        tbzBo.value = true;
        isAdmin.value = true;
        isAuditShow.value = true;
        activeName.value = '3';
        handleClick('3');
      } else {
        const permissions = (res as any).permissions || [];
        for (const permission of permissions) {
          if (permission === 'envControl:page:auditPage' && isAuditShow.value) {
            spzBo.value = true;
            activeName.value = '1';
          }
          if (permission === 'envControl:page:reportPage') {
            tbzBo.value = true;
            activeName.value = '0';
          }
        }
      }
    } catch (error) {
      // 默认权限
      spzBo.value = true;
      tbzBo.value = true;
      isAdmin.value = true;
      isAuditShow.value = true;
      activeName.value = '3';
      handleClick('3');
    }
  } catch (error) {
    console.error('初始化失败:', error);
  }
});
</script>

<style lang="less" scoped>
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}
// 基础变量
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@text-color: #ffffff;
@border-color: #00409a;
@background-color: rgba(0, 27, 64, 0.6);
@card-background: rgba(7, 58, 169, 0.8);

// 滚动条隐藏
div::-webkit-scrollbar {
  display: none;
}

// 主树形结构
.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  padding: 24px;
  transition: left 0.5s;

  &.main-tree-s {
    left: -390px;
  }
}

// 主体内容区域
.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  padding: 24px;
  transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;

  &.main-body-s {
    width: calc(100% - 50px);
    left: 25px;
  }
}

// 左侧按钮
.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  top: calc((100% - 89px) / 2);
  transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  opacity: 0.8;

  &:hover {
    opacity: 1;
  }

  &.left-img-s {
    left: 0;
  }
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  top: calc((100% - 50px) / 2);
  transform: rotate(180deg);
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  opacity: 0.8;

  &:hover {
    opacity: 1;
  }

  &.left-img-icon-s {
    left: -8px;
    transform: rotate(0deg);
  }
}

// 树形包装器
.tree-wrapper {
  height: calc(100% - 52px);
  overflow: auto;
  margin-top: 20px;
}

// 环境监控页面
.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  color: #ffffff;
  display: block;

  // 现代化卡片效果
  .ant-card {
    background: @card-background;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);

    .ant-card-head {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .ant-card-head-title {
        color: @text-color;
      }
    }

    .ant-card-body {
      color: @text-color;
    }
  }
}

// 查询表单样式
.demo-form-inline {
  margin-top: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

// 表格区域
.table {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}

// 气泡确认框样式
:deep(.ant-popover) {
  .ant-popover-content {
    .ant-popover-inner {
      background: @card-background;
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);

      .ant-popover-inner-content {
        color: @text-color;
      }
    }
  }

  .ant-popover-arrow {
    &::before {
      background: @card-background;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

// 工具类
.text-center {
  text-align: center;
}

.mb-16 {
  margin-bottom: 16px;
}

.mt-20 {
  margin-top: 20px;
}

.p-20 {
  padding: 20px;
}

.border-radius-12 {
  border-radius: 12px;
}

.backdrop-blur {
  backdrop-filter: blur(10px);
}
// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
