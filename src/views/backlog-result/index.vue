<template>
  <div class="environmental-alarm-page">
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <a-input
        v-model:value="filterText"
        class="search"
        placeholder="输入关键词"
        allow-clear
        size="small"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="tree-wrapper">
        <a-tree
          ref="monitorList"
          :tree-data="treeData as any"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'vldSiteId',
          }"
          :show-line="false"
          :show-icon="false"
          @select="handleNodeClick as any"
        >
        </a-tree>
      </div>
    </div>
    <img
      src="@/assets/images/shouqi.png"
      alt=""
      :class="['left-img', isCollapsed ? 'left-img-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <img
      src="@/assets/images/shouqiicon.png"
      alt=""
      :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <div
      ref="menu"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>
    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog></menuDialog>
    </div>
    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <a-tabs v-model:active-key="activeName" @change="handleClick as any">
        <a-tab-pane key="0" tab="超标数据推送" />
        <a-tab-pane key="1" tab="待办提醒推送" />
      </a-tabs>
      <a-form
        ref="formRef"
        layout="inline"
        :model="formData"
        class="demo-form-inline"
      >
        <a-form-item label="用户姓名：">
          <a-input
            v-model:value="formData.userName"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>
        <a-form-item v-if="name === '0'" label="超标指标:">
          <a-select
            v-model:value="formData.typeEvent"
            placeholder="请选择"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="NOX">NOX</a-select-option>
            <a-select-option value="COD">COD</a-select-option>
            <a-select-option value="氨氮">氨氮</a-select-option>
            <a-select-option value="SO2">SO₂</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="name === '1'" label="事件类型:">
          <a-select
            v-model:value="formData.typeEvent"
            placeholder="请选择"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="超标指标">超标指标</a-select-option>
            <a-select-option value="推送事件">推送事件</a-select-option>
            <a-select-option value="推送统计">推送统计</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="推送时间:">
          <a-range-picker
            v-model:value="formData.timeArr as any"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
          />
        </a-form-item>
        <a-form-item label="推送状态:">
          <a-select
            v-model:value="formData.pushState"
            placeholder="请选择"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="0">成功</a-select-option>
            <a-select-option value="1">失败</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item class="ant-form-item-bottom">
          <a-space>
            <a-button type="primary" @click="search">查询</a-button>
            <a-button @click="resetForm">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
      <div class="table">
        <div class="table-wrapper">
          <a-table
            v-if="name === '0'"
            :data-source="tableData1"
            :columns="pushColumns as any"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'pushState'">
                <span v-if="record.pushState === 1" style="color: red"
                  >失败</span
                >
                <span v-else style="color: greenyellow">成功</span>
              </template>
            </template>
          </a-table>
          <a-table
            v-if="name === '1'"
            :data-source="tableData2"
            :columns="reminderColumns as any"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'pushState'">
                <span v-if="record.pushState === 1" style="color: red"
                  >失败</span
                >
                <span v-else style="color: greenyellow">成功</span>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <a-pagination
            v-model:current="formData.pageNum"
            v-model:page-size="formData.pageSize"
            :total="total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :page-size-options="['20', '40', '60', '80', '100']"
            :show-total="total => `共 ${total} 条`"
            style="margin-top: 16px; text-align: right"
            @change="onPageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { pushResultPage } from '@/http/backlog';
import { getTreeNode } from '@/http/environmental-overview';
import menuDialog from '@/components/menu.vue';

// 接口定义
interface FormData {
  timeArr: [string, string] | null;
  userName: string;
  typeEvent: string;
  pushState: string;
  type: string;
  siteId: string;
  pageNum: number;
  pageSize: number;
}

interface TreeNode {
  id: string;
  siteShortName: string;
  children?: TreeNode[];
}

interface TableRecord {
  userName: string;
  userUnit: string;
  typeEvent: string;
  pushTime: string;
  pushState: number;
  [key: string]: any;
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const filterText = ref('');
const isCollapsed = ref(false);
const name = ref('0');
const activeName = ref('0');
const formData = ref<FormData>({
  timeArr: null,
  userName: '',
  typeEvent: '',
  pushState: '',
  type: '',
  siteId: '',
  pageNum: 1,
  pageSize: 20,
});

const total = ref(0);
const treeData = ref<TreeNode[]>([]);
const tableData1 = ref<TableRecord[]>([]);
const tableData2 = ref<TableRecord[]>([]);

// 分页器事件处理方法
const onPageChange = (page: number, pageSize: number) => {
  formData.value.pageNum = page || 1;
  formData.value.pageSize = pageSize;
  search();
};

// 表格列定义
const pushColumns = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
    align: 'center',
  },
  {
    title: '单位名称',
    dataIndex: 'userUnit',
    key: 'userUnit',
    align: 'center',
  },
  {
    title: '超标指标',
    dataIndex: 'typeEvent',
    key: 'typeEvent',
    width: 120,
    align: 'center',
  },
  {
    title: '推送时间',
    dataIndex: 'pushTime',
    key: 'pushTime',
    width: 150,
    align: 'center',
  },
  { title: '推送状态', key: 'pushState', width: 120, align: 'center' },
];

const reminderColumns = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
    align: 'center',
  },
  {
    title: '单位名称',
    dataIndex: 'userUnit',
    key: 'userUnit',
    align: 'center',
  },
  {
    title: '事件类型',
    dataIndex: 'typeEvent',
    key: 'typeEvent',
    width: 120,
    align: 'center',
  },
  {
    title: '推送时间',
    dataIndex: 'pushTime',
    key: 'pushTime',
    width: 150,
    align: 'center',
  },
  { title: '推送状态', key: 'pushState', width: 120, align: 'center' },
];

// 方法
const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const menu = document.querySelector('.draggable-menu') as HTMLElement;
  const menuRect = menu.getBoundingClientRect();

  const onMouseMove = (e: MouseEvent) => {
    const newX = e.clientX - menuRect.width / 2;
    const newY = e.clientY - menuRect.height / 2;

    x.value = Math.max(0, Math.min(newX, window.innerWidth - menuRect.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menuRect.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const handleNodeClick = (selectedKeys: string[], info: any) => {
  if (selectedKeys.length > 0) {
    formData.value.siteId = selectedKeys[0];
    search();
  }
};

const search = () => {
  const form = {
    userName: formData.value.userName,
    typeEvent: formData.value.typeEvent,
    pushState: formData.value.pushState,
    type: name.value,
    siteId: formData.value.siteId,
    pageNum: formData.value.pageNum,
    pageSize: formData.value.pageSize,
    startDate: '',
    endDate: '',
  };

  if (formData.value.timeArr) {
    form.startDate = dayjs(formData.value.timeArr[0]).format(
      'YYYY-MM-DD HH:mm:00'
    );
    form.endDate = dayjs(formData.value.timeArr[1]).format(
      'YYYY-MM-DD 23:59:59'
    );
  }

  if (name.value === '0') {
    getTableData1(form);
  } else if (name.value === '1') {
    getTableData2(form);
  }
};

const handleClick = (key: string) => {
  formData.value = {
    timeArr: null,
    userName: '',
    typeEvent: '',
    pushState: '',
    type: '',
    siteId: '',
    pageNum: 1,
    pageSize: 20,
  };
  name.value = key;
  search();
};

const getTableData1 = (form: any) => {
  pushResultPage(form).then((res: any) => {
    tableData1.value = res.data.records;
    total.value = res.data.total;
  });
};

const getTableData2 = (form: any) => {
  pushResultPage(form).then((res: any) => {
    tableData2.value = res.data.records;
    total.value = res.data.total;
  });
};

const resetForm = () => {
  formData.value = {
    timeArr: null,
    userName: '',
    typeEvent: '',
    pushState: '',
    type: '',
    siteId: '',
    pageNum: 1,
    pageSize: 20,
  };
  search();
};

const getTreeNanfang = async () => {
  const res = await getTreeNode();
  treeData.value = res.data;
};

// 生命周期
onMounted(() => {
  search();
  getTreeNanfang();
});

// 监听器
watch(filterText, val => {
  // 树过滤逻辑
});
</script>
<style lang="less" scoped>
//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

div::-webkit-scrollbar {
  display: none;
}

//---
.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  ::v-deep .el-input--small {
    border: 1px solid #00409a;
    border-radius: 5px;
  }

  ::v-deep .el-input__inner {
    box-shadow: 0 0 0.05rem transparent inset;
    background: rgba(0, 27, 64, 0.6);
  }
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;

  ::v-deep .el-input__inner {
    box-shadow: 0 0 0.05rem transparent inset;
    background: rgba(0, 27, 64, 0.6);
  }
}

.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .el-input__inner {
    color: #fff;
    background-color: #073aa9;
    border: 0;
    box-shadow: 0 0 0.05rem #86a5e7 inset;
  }
}

.main-body-s {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  .el-input__inner {
    color: #fff;
    background-color: #073aa9;
    border: 0;
    box-shadow: 0 0 0.05rem #86a5e7 inset;
  }
}

.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  transition: left 0.5s;
  top: calc((100% - 89px) / 2);
}

.left-img-s {
  left: 0px;
  transition: left 0.5s;
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  transform: rotate(180deg);
  top: calc((100% - 50px) / 2);
  transition: transform 0.5s, left 0.5s;
  cursor: pointer;
}

.left-img-icon-s {
  left: -8px;
  transform: rotate(0deg);
  transition: transform 0.5s, left 0.5s;
}

.tree-wrapper {
  height: calc(100% - 52px);
  overflow: auto;
  margin-top: 20px;
}

.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  //background: rgba(0, 0, 0, 0.3);
  color: #fff;

  .main {
    overflow: hidden;
    height: 100%;
  }

  .main:nth-child(1) {
    width: 350px;
  }

  .main:nth-child(2) {
    width: calc(100% - 366px);
  }
}

.demo-form-inline {
  margin-top: 16px;
}

.table {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;

  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}

// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
