<template>
  <div class="environmental-alarm-page">
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <a-input
        v-model:value="filterText"
        class="search"
        placeholder="输入关键词"
        allow-clear
        size="small"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="tree-wrapper">
        <a-tree
          ref="monitorList"
          :tree-data="treeData"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'id',
          }"
          :show-line="false"
          :show-icon="false"
          @select="handleNodeClick"
        />
      </div>
    </div>
    <img
      src="@/assets/images/shouqi.png"
      alt=""
      :class="['left-img', isCollapsed ? 'left-img-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <img
      src="@/assets/images/shouqiicon.png"
      alt=""
      :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <!-- 菜单图标 -->
    <div
      ref="menu"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>
    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog />
    </div>

    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <a-form
        ref="formRef"
        layout="inline"
        :model="formData"
        class="demo-form-inline"
      >
        <a-form-item name="year" label="年份:">
          <a-date-picker
            v-model:value="formData.year"
            picker="year"
            placeholder="选择年"
            :allow-clear="false"
          />
        </a-form-item>
        <a-form-item
          v-if="activeName === '0' || activeName === '1' || activeName === '2'"
          name="pointName"
          label="点位名称:"
        >
          <a-input
            v-model:value="formData.pointName"
            placeholder="请输入"
            allow-clear
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item class="ant-form-item-bottom">
          <a-space>
            <a-button style="margin-left: 100px" type="primary" @click="search">
              查询
            </a-button>
            <a-button @click="resetForm">重置</a-button>
            <a-button type="primary" @click="addHandle">新增</a-button>
            <a-button type="primary" @click="importXlsx">数据导入</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <div class="table">
        <div class="table-wrapper">
          <a-table
            v-if="name === '0' || name === '2'"
            :data-source="monitorPlanDate"
            :columns="columns"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'monitorType'">
                <span v-if="record.monitorType === 0">有组织废气</span>
                <span v-if="record.monitorType === 1">无组织废气</span>
                <span v-if="record.monitorType === 2">泄露监测</span>
                <span v-if="record.monitorType === 3">废水</span>
                <span v-if="record.monitorType === 4">噪声</span>
                <span v-if="record.monitorType === 5">地表水和地下水</span>
                <span v-if="record.monitorType === 6">土壤</span>
                <span v-if="record.monitorType === 7">环境空气</span>
                <span v-if="record.monitorType === 8">循环冷却水</span>
              </template>
              <template v-else-if="column.key === 'limitValue'">
                <span>{{ record.minNum }}-{{ record.maxNum }}</span>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="link"
                  size="small"
                  @click="handleInfoClick(record)"
                >
                  创建监测方案
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="updateHandle(record)"
                >
                  修改
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="deleteHandle(record)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
        <a-pagination
          v-model:current="formData.pageNum"
          v-model:page-size="formData.pageSize"
          :total="total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :page-size-options="['20', '40', '60', '80', '100']"
          :show-total="total => `共 ${total} 条`"
          style="margin-top: 16px; text-align: right"
          @change="onPageChange"
        />
      </div>
    </div>

    <!-- 新增方案 -->
    <a-modal v-model:open="addDialog" :title="title" width="70%" :footer="null">
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-col="{ style: { width: '150px' } }"
      >
        <a-row>
          <a-col :span="12">
            <a-form-item name="year" label="年份:">
              <a-date-picker
                v-model:value="form.year"
                picker="year"
                placeholder="选择年"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="secondLevelId" label="二级单位:">
              <a-select
                v-model:value="form.secondLevelId"
                placeholder="请选择单位"
                allow-clear
                show-search
                style="width: 100%"
                @change="(value: any) => getThreeDeptList(value)"
              >
                <a-select-option
                  v-for="(item, index) in towDeptList"
                  :key="index"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-item
              :name="threeDeptList.length > 0 ? 'threeLevelId' : ''"
              label="三级单位:"
            >
              <a-select
                v-model:value="form.threeLevelId"
                placeholder="请选择单位"
                allow-clear
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="(item, index) in threeDeptList"
                  :key="index"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="monitorType" label="监测类型:">
              <a-select
                v-model:value="form.monitorType"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option :value="0">有组织废气</a-select-option>
                <a-select-option :value="1">无组织废气</a-select-option>
                <a-select-option :value="2">泄露监测</a-select-option>
                <a-select-option :value="3">废水</a-select-option>
                <a-select-option :value="4">噪声</a-select-option>
                <a-select-option :value="5">地表水和地下水</a-select-option>
                <a-select-option :value="6">土壤</a-select-option>
                <a-select-option :value="7">环境空气</a-select-option>
                <a-select-option :value="8">循环冷却水</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 条件渲染的表单项 -->
        <a-row v-if="form.monitorType === 4">
          <a-col :span="12">
            <a-form-item name="monitorPointType" label="监测点位类型:">
              <a-select
                v-model:value="form.monitorPointType"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in monitorPointTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="stationType" label="场站类型:">
              <a-select
                v-model:value="form.stationType"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in stationTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row
          v-if="
            form.monitorType === 5 ||
            form.monitorType === 6 ||
            form.monitorType === 7
          "
        >
          <a-col :span="24">
            <a-form-item name="monitorPointType" label="监测点位类型:">
              <a-select
                v-model:value="form.monitorPointType"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in monitorPointTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-if="form.monitorType === 3 || form.monitorType === 8">
          <a-col :span="24">
            <a-form-item name="wastewaterType" label="废水类型:">
              <a-select
                v-model:value="form.wastewaterType"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in wastewaterTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-item
              name="pointName"
              :label="form.monitorType === 8 ? '点位名称(进口):' : '点位名称:'"
            >
              <a-input
                v-model:value="form.pointName"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              name="province"
              :label="form.monitorType === 8 ? '地理位置(进口):' : '地理位置:'"
            >
              <a-select
                v-model:value="form.province"
                placeholder="省"
                show-search
                style="width: 30%; margin-right: 5%"
                @change="(value: string) => cityList(2, value)"
              >
                <a-select-option
                  v-for="item in provinces"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
              <a-select
                v-model:value="form.city"
                placeholder="市"
                show-search
                style="width: 30%; margin-right: 5%"
                @change="(value: string) => cityList(3, value)"
              >
                <a-select-option
                  v-for="item in citys"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
              <a-select
                v-model:value="form.area"
                placeholder="区"
                show-search
                style="width: 30%"
              >
                <a-select-option
                  v-for="item in areas"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 循环冷却水的出口信息 -->
        <a-row v-if="form.monitorType === 8">
          <a-col :span="12">
            <a-form-item name="pointNameOut" label="点位名称(出口):">
              <a-input
                v-model:value="form.pointNameOut"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="provinceOut" label="地理位置(出口):">
              <a-space style="width: 100%">
                <a-select
                  v-model:value="form.provinceOut"
                  placeholder="省"
                  show-search
                  style="width: 30%; margin-right: 5%"
                  @change="(value: string) => cityList(2, value)"
                >
                  <a-select-option
                    v-for="item in provinces"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.cityOut"
                  placeholder="市"
                  show-search
                  style="width: 30%; margin-right: 5%"
                  @change="(value: string) => cityList(3, value)"
                >
                  <a-select-option
                    v-for="item in citys"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="form.areaOut"
                  placeholder="区"
                  show-search
                  style="width: 30%"
                >
                  <a-select-option
                    v-for="item in areas"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点数量 - 地表水和地下水、土壤、环境空气 -->
        <a-row
          v-if="
            form.monitorType === 5 ||
            form.monitorType === 6 ||
            form.monitorType === 7
          "
        >
          <a-col :span="24">
            <a-form-item name="distributionNumber" label="布点数量:">
              <a-input
                v-model:value="form.distributionNumber"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 布点编号 - 噪声 -->
        <a-row v-if="form.monitorType === 4">
          <a-col :span="24">
            <a-form-item name="distributionId" label="布点编号:">
              <a-input
                v-model:value="form.distributionId"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 废水相关字段 -->
        <a-row v-if="form.monitorType === 3">
          <a-col :span="12">
            <a-form-item name="emissionDestination" label="排放去向:">
              <a-input
                v-model:value="form.emissionDestination"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="discharge" label="排放量:">
              <a-input
                v-model:value="form.discharge"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 有组织废气相关字段 -->
        <a-row v-if="form.monitorType === 0">
          <a-col :span="12">
            <a-form-item name="boilerType" label="锅炉类型:">
              <a-input
                v-model:value="form.boilerType"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="boilerPower" label="功率:">
              <a-select
                v-model:value="form.boilerPower"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in boilerPowerTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-if="form.monitorType === 0">
          <a-col :span="12">
            <a-form-item name="boilerSize" label="规模:">
              <a-input
                v-model:value="form.boilerSize"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              name="dischargeMethodAndDestination"
              label="排放方式及去向:"
            >
              <a-select
                v-model:value="form.dischargeMethodAndDestination"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in dischargeMethodAndDestinations"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 循环冷却水的监测指标和次数 -->
        <div v-if="form.monitorType === 8">
          <a-row>
            <a-col :span="12">
              <a-form-item name="monitorIndexOut" label="监测指标(进口):">
                <a-input
                  v-model:value="form.monitorIndexOut"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumberOut" label="监测次数(进口):">
                <a-input
                  v-model:value="form.monitorNumberOut"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item name="monitorIndex" label="监测指标(出口):">
                <a-input
                  v-model:value="form.monitorIndex"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumber" label="监测次数(出口):">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 其他监测类型的监测指标和次数 -->
        <div v-else>
          <a-row v-if="form.monitorType !== 2 && form.monitorType !== 3">
            <a-col :span="12">
              <a-form-item name="monitorIndex" label="监测指标:">
                <a-select
                  v-model:value="form.monitorIndex"
                  placeholder="请选择"
                  show-search
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="item in monitorIndexs"
                    :key="item.dict_value"
                    :value="item.dict_value"
                  >
                    {{ item.dict_label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="monitorNumber" label="监测次数:">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-else>
            <a-col :span="8">
              <a-form-item name="monitorIndex" label="监测指标:">
                <a-select
                  v-model:value="form.monitorIndex"
                  placeholder="请选择"
                  show-search
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="item in monitorIndexs"
                    :key="item.dict_value"
                    :value="item.dict_value"
                  >
                    {{ item.dict_label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="sealNumber" label="密封点数:">
                <a-input
                  v-model:value="form.sealNumber"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="monitorNumber" label="监测次数:">
                <a-input
                  v-model:value="form.monitorNumber"
                  placeholder="请输入"
                  allow-clear
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 监测周期和方式 -->
        <a-row>
          <a-col :span="12">
            <a-form-item name="monitorCycle" label="监测周期:">
              <a-select
                v-model:value="form.monitorCycle"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in monitorCycles"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="monitorWay" label="每次监测方式:">
              <a-select
                v-model:value="form.monitorWay"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in monitorWays"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 执行标准 -->
        <a-row>
          <a-col :span="24">
            <a-form-item name="executiveStandard" label="执行标准:">
              <a-select
                v-model:value="form.executiveStandard"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in executiveStandards"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 限值 -->
        <a-row>
          <a-col :span="12">
            <a-form-item name="minNum" label="限值:">
              <a-input-number
                v-if="
                  form.monitorType === 8 ||
                  form.monitorType === 3 ||
                  form.monitorType === 4 ||
                  form.monitorType === 5
                "
                v-model:value="form.minNum"
                :min="0"
                :max="9999999999"
                style="width: 49%; margin-right: 2%"
                :placeholder="
                  form.monitorType === 4 ? '昼间限值最大值' : '请输入'
                "
              />
              <a-input-number
                v-model:value="form.maxNum"
                :min="0"
                :max="9999999999"
                style="width: 49%"
                :placeholder="
                  form.monitorType === 4 ? '夜间限值最大值' : '请输入'
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="unit" label="单位:">
              <a-select
                v-model:value="form.unit"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in unitTypes"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 监测分析方法及依据 和 点位确定监测依据 -->
        <a-row>
          <a-col :span="12">
            <a-form-item name="monitorAnalysis" label="监测分析方法及依据:">
              <a-select
                v-model:value="form.monitorAnalysis"
                placeholder="请选择"
                show-search
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in monitorMethodsBases"
                  :key="item.dict_value"
                  :value="item.dict_value"
                >
                  {{ item.dict_label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="form.monitorType !== 0 && form.monitorType !== 2"
            :span="12"
          >
            <a-form-item name="pointRecognition" label="点位确定监测依据:">
              <a-input
                v-model:value="form.pointRecognition"
                placeholder="请输入"
                allow-clear
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <div style="text-align: right; margin-top: 16px">
        <a-space>
          <a-button @click="addDialog = false">取 消</a-button>
          <a-button type="primary" @click="saveHandle">确 定</a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 数据导入 -->
    <a-modal
      v-model:open="importDialog"
      title="数据导入"
      width="20%"
      :footer="null"
    >
      <a-form style="margin-top: 20px">
        <a-form-item>
          <a
            href="http://11.89.104.129:9000/test/2025/04/18/c6418107-6ebc-4ac2-96f9-1ad1b8648647.xls"
            target="_blank"
            style="font-size: 15px"
          >
            下载模板
          </a>
        </a-form-item>
        <a-form-item>
          <a-upload
            action="api/file/upload"
            :before-upload="beforeAvatarUpload"
            :file-list="fileList"
            list-type="picture"
            :headers="{ Authorization: token }"
            :max-count="1"
            @change="handleUploadChange"
          >
            <a-button type="primary">点击上传附件</a-button>
          </a-upload>
        </a-form-item>
      </a-form>
      <div
        v-if="loading"
        style="color: red; font-size: 20px; width: 100%; text-align: center"
      >
        正在导入数据中...
      </div>
      <div style="text-align: right; margin-top: 16px">
        <a-space>
          <a-button @click="importDialog = false">取 消</a-button>
          <a-button type="primary" @click="submitFileForm">确 定</a-button>
        </a-space>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, computed } from 'vue';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import {
  getTreeNodesWithoutOutlets as getTreeNode,
  getMonitorPlanPage,
  addMonitorPlan,
  deleteMonitorPlan,
  getDictList,
  getTowLevelDept,
  getDeptList,
  monitorPlanInfo,
  importPlan,
} from '@/http/monitor';
import { getCityList } from '@/http/city';
import menuDialog from '@/components/menu.vue';

// 接口定义
interface FormData {
  year: Dayjs | undefined;
  pointName: string;
  deptId: string;
  pageNum: number;
  pageSize: number;
}

interface Form {
  id?: string;
  year: Dayjs | undefined;
  secondLevelId: string;
  secondLevelName: string;
  threeLevelId: string;
  threeLevelName: string;
  monitorType: number | undefined;
  monitorPointType: string;
  stationType: string;
  wastewaterType: string;
  pointName: string;
  province: string;
  city: string;
  area: string;
  pointNameOut: string;
  provinceOut: string;
  cityOut: string;
  areaOut: string;
  monitorNumberOut: string;
  monitorIndexOut: string;
  monitorIndex: string;
  sealNumber: string;
  monitorNumber: string;
  monitorCycle: string;
  monitorWay: string;
  executiveStandard: string;
  minNum: number;
  maxNum: number;
  unit: string;
  fileUrl: string;
  fileName: string;
  emissionDestination: string;
  discharge: string;
  boilerType: string;
  boilerPower: string;
  boilerSize: string;
  dischargeMethodAndDestination: string;
  sensitiveSpot: string;
  distributionId: string;
  distributionNumber: string;
  monitorAnalysis: string;
  pointRecognition: string;
}

interface TreeNode {
  id: string;
  key: string;
  siteShortName: string;
  children?: TreeNode[];
}

interface DictItem {
  dict_value: string;
  dict_label: string;
}

interface CityItem {
  id: string;
  name: string;
}

interface DeptItem {
  id: string;
  deptName: string;
}

interface UploadFileItem {
  uid: string;
  name: string;
  url: string;
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const loading = ref(false);
const fileUrl = ref('');
const fileList = ref<UploadFileItem[]>([]);
const importDialog = ref(false);
const filterText = ref('');
const isCollapsed = ref(false);
const title = ref('');
const name = ref('0');
const activeName = ref('0');
const formRef = ref<FormInstance>();

const formData = ref<FormData>({
  year: dayjs(),
  pointName: '',
  deptId: '',
  pageNum: 1,
  pageSize: 20,
});

const monitorPlanDate = ref([]);
const total = ref(0);
const treeData = ref<TreeNode[]>([]);
const addDialog = ref(false);

// 表单数据
const form = ref<Form>({
  year: undefined,
  secondLevelId: '',
  secondLevelName: '',
  threeLevelId: '',
  threeLevelName: '',
  monitorType: undefined,
  monitorPointType: '',
  stationType: '',
  wastewaterType: '',
  pointName: '',
  province: '',
  city: '',
  area: '',
  pointNameOut: '',
  provinceOut: '',
  cityOut: '',
  areaOut: '',
  monitorNumberOut: '',
  monitorIndexOut: '',
  monitorIndex: '',
  sealNumber: '',
  monitorNumber: '',
  monitorCycle: '',
  monitorWay: '',
  executiveStandard: '',
  minNum: 0,
  maxNum: 0,
  unit: '',
  fileUrl: '',
  fileName: '',
  emissionDestination: '',
  discharge: '',
  boilerType: '',
  boilerPower: '',
  boilerSize: '',
  dischargeMethodAndDestination: '',
  sensitiveSpot: '',
  distributionId: '',
  distributionNumber: '',
  monitorAnalysis: '',
  pointRecognition: '',
});

// 表单校验规则
const rules = {
  year: [{ required: true, message: '请选择年份', trigger: 'blur' as const }],
  secondLevelId: [
    { required: true, message: '请选择二级单位', trigger: 'blur' as const },
  ],
  threeLevelId: [
    { required: true, message: '请选择三级单位', trigger: 'blur' as const },
  ],
  monitorType: [
    { required: true, message: '请选择监测类型', trigger: 'blur' as const },
  ],
  pointName: [
    { required: true, message: '请输入点位名称', trigger: 'blur' as const },
  ],
  province: [
    { required: true, message: '请选择地理位置', trigger: 'blur' as const },
  ],
  monitorIndex: [
    { required: true, message: '请输入监测指标', trigger: 'blur' as const },
  ],
  monitorNumber: [
    { required: true, message: '请输入监测次数', trigger: 'blur' as const },
  ],
  pointNameOut: [
    { required: true, message: '请输入点位名称', trigger: 'blur' as const },
  ],
  provinceOut: [
    { required: true, message: '请选择地理位置', trigger: 'blur' as const },
  ],
  monitorIndexOut: [
    { required: true, message: '请输入监测指标', trigger: 'blur' as const },
  ],
  monitorNumberOut: [
    { required: true, message: '请输入监测次数', trigger: 'blur' as const },
  ],
  monitorCycle: [
    { required: true, message: '请选择监测周期', trigger: 'blur' as const },
  ],
  monitorWay: [
    { required: true, message: '请选择监测方式', trigger: 'blur' as const },
  ],
  executiveStandard: [
    { required: true, message: '请选择执行标准', trigger: 'blur' as const },
  ],
  minNum: [
    { required: true, message: '请输入限值最小值', trigger: 'blur' as const },
  ],
  maxNum: [
    { required: true, message: '请输入限值最大值', trigger: 'blur' as const },
  ],
  unit: [{ required: true, message: '请选择单位', trigger: 'blur' as const }],
  boilerPower: [
    { required: true, message: '请选择功率', trigger: 'blur' as const },
  ],
  dischargeMethodAndDestination: [
    {
      required: true,
      message: '请选择排放方式及去向',
      trigger: 'blur' as const,
    },
  ],
  monitorAnalysis: [
    {
      required: true,
      message: '请输入监测分析方法及依据',
      trigger: 'blur' as const,
    },
  ],
  pointRecognition: [
    {
      required: true,
      message: '请输入点位确认监测依据',
      trigger: 'blur' as const,
    },
  ],
  sealNumber: [
    { required: true, message: '请输入密封点数', trigger: 'blur' as const },
  ],
  wastewaterType: [
    { required: true, message: '请选择废水类型', trigger: 'blur' as const },
  ],
  emissionDestination: [
    { required: true, message: '请输入排放去向', trigger: 'blur' as const },
  ],
  discharge: [
    { required: true, message: '请输入排放量', trigger: 'blur' as const },
  ],
  monitorPointType: [
    { required: true, message: '请选择监测点位类型', trigger: 'blur' as const },
  ],
  stationType: [
    { required: true, message: '请选择场站类型', trigger: 'blur' as const },
  ],
  sensitiveSpot: [
    { required: true, message: '请选择厂界/敏感点', trigger: 'blur' as const },
  ],
  area: [
    { required: true, message: '请选择地理位置', trigger: 'blur' as const },
  ],
};

// 上传token
const token = ref('Bearer e0739356-87f8-48a9-b533-6243f895286f');

// 各种字典数据
const monitorCycles = ref<DictItem[]>([]);
const monitorWays = ref<DictItem[]>([]);
const executiveStandards = ref<DictItem[]>([]);
const monitorMethodsBases = ref<DictItem[]>([]);
const dischargeMethodAndDestinations = ref<DictItem[]>([]);
const boilerPowerTypes = ref<DictItem[]>([]);
const wastewaterTypes = ref<DictItem[]>([]);
const monitorPointTypes = ref<DictItem[]>([]);
const monitorIndexs = ref<DictItem[]>([]);
const stationTypes = ref<DictItem[]>([]);
const unitTypes = ref<DictItem[]>([]);
const provinces = ref<CityItem[]>([]);
const citys = ref<CityItem[]>([]);
const areas = ref<CityItem[]>([]);
const towDeptList = ref<DeptItem[]>([]);
const threeDeptList = ref<DeptItem[]>([]);

// 计算属性
const onPageChange = (page: number, size: number) => {
  formData.value.pageNum = page || 1;
  formData.value.pageSize = size;
  search();
};

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center' as const,
    fixed: 'left' as const,
  },
  {
    title: '年份',
    dataIndex: 'year',
    key: 'year',
    width: 120,
    align: 'center' as const,
  },
  {
    title: '二级单位',
    dataIndex: 'secondLevelName',
    key: 'secondLevelName',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '三级单位',
    dataIndex: 'threeLevelName',
    key: 'threeLevelName',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '监测类型',
    key: 'monitorType',
    width: 120,
    align: 'center' as const,
  },
  {
    title: '点位名称',
    dataIndex: 'pointName',
    key: 'pointName',
    align: 'center' as const,
    width: 180,
  },
  {
    title: '监测指标',
    dataIndex: 'monitorIndex',
    key: 'monitorIndex',
    align: 'center' as const,
    width: 180,
  },
  {
    title: '监测频次',
    dataIndex: 'monitorCycle',
    key: 'monitorCycle',
    align: 'center' as const,
    width: 180,
  },
  { title: '限值', key: 'limitValue', align: 'center' as const, width: 180 },
  {
    title: '限值单位',
    dataIndex: 'unit',
    key: 'unit',
    align: 'center' as const,
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 240,
    align: 'center' as const,
    fixed: 'right' as const,
  },
];

// 方法
const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const menu = document.querySelector('.draggable-menu') as HTMLElement;
  const menuRect = menu.getBoundingClientRect();

  const onMouseMove = (e: MouseEvent) => {
    const newX = e.clientX - menuRect.width / 2;
    const newY = e.clientY - menuRect.height / 2;

    x.value = Math.max(0, Math.min(newX, window.innerWidth - menuRect.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menuRect.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const handleNodeClick = (selectedKeys: any[], info: any) => {
  if (selectedKeys.length > 0) {
    formData.value.deptId = selectedKeys[0].toString();
    search();
  }
};

const search = () => {
  let yyyy = '';
  if (formData.value.year) {
    yyyy = formData.value.year.year().toString();
  }
  const form = {
    year: yyyy,
    deptId: formData.value.deptId,
    pointName: formData.value.pointName,
    pageNum: formData.value.pageNum,
    pageSize: formData.value.pageSize,
  };
  getMonitorPlanDate(form);
};

const getMonitorPlanDate = async (form: any) => {
  const res = await getMonitorPlanPage(form);
  monitorPlanDate.value = res.data.records;
  total.value = res.data.total;
};

const resetForm = () => {
  formData.value = {
    year: dayjs(),
    pointName: '',
    deptId: '',
    pageNum: 1,
    pageSize: 20,
  };
  search();
};

const getTreeNanfang = async () => {
  const res = await getTreeNode();
  // 转换数据格式以添加 key 属性
  const convertTreeData = (nodes: any[]): TreeNode[] => {
    return nodes.map(node => ({
      ...node,
      key: node.id,
      children: node.children ? convertTreeData(node.children) : undefined,
    }));
  };
  treeData.value = convertTreeData(res.data);
};

const updateHandle = (row: any) => {
  addDialog.value = true;
  title.value = '修改基础信息';
  monitorPlanInfoHandle(row.id);
  getData();
};

const monitorPlanInfoHandle = (id: string) => {
  monitorPlanInfo(id).then((res: any) => {
    if (res.code === 200) {
      form.value = res.data;
      if (form.value.year) {
        form.value.year = dayjs(form.value.year.toString() + '-01-01');
      }
      getThreeDeptList(form.value.secondLevelId);
      cityList(2, form.value.province);
      cityList(3, form.value.city);
    }
  });
};

const addHandle = () => {
  addDialog.value = true;
  title.value = '新增基础信息';
  form.value = {
    year: undefined,
    secondLevelId: '',
    secondLevelName: '',
    threeLevelId: '',
    threeLevelName: '',
    monitorType: undefined,
    monitorPointType: '',
    stationType: '',
    wastewaterType: '',
    pointName: '',
    province: '',
    city: '',
    area: '',
    pointNameOut: '',
    provinceOut: '',
    cityOut: '',
    areaOut: '',
    monitorNumberOut: '',
    monitorIndexOut: '',
    monitorIndex: '',
    sealNumber: '',
    monitorNumber: '',
    monitorCycle: '',
    monitorWay: '',
    executiveStandard: '',
    minNum: 0,
    maxNum: 0,
    unit: '',
    fileUrl: '',
    fileName: '',
    emissionDestination: '',
    discharge: '',
    boilerType: '',
    boilerPower: '',
    boilerSize: '',
    dischargeMethodAndDestination: '',
    sensitiveSpot: '',
    distributionId: '',
    distributionNumber: '',
    monitorAnalysis: '',
    pointRecognition: '',
  };
  getData();
};

const getData = () => {
  dictListDate('monitor_cycle');
  dictListDate('monitor_way');
  dictListDate('executive_standard');
  dictListDate('discharge_method_and_destination');
  dictListDate('wastewater_type');
  dictListDate('monitor_point_type');
  dictListDate('station_type');
  dictListDate('unit');
  dictListDate('boiler_power_type');
  dictListDate('monitor_methods_bases');
  dictListDate('monitor_index');
};

const importXlsx = () => {
  importDialog.value = true;
  fileList.value = [];
  fileUrl.value = '';
};

const submitFileForm = () => {
  if (fileUrl.value === '') {
    message.error('请上传文件');
    return;
  }
  loading.value = true;
  importPlan(fileUrl.value)
    .then((res: any) => {
      if (res.code === 200) {
        message.success(res.msg);
        loading.value = false;
        importDialog.value = false;
        search();
      }
    })
    .catch(() => {
      message.error('导入失败');
      loading.value = false;
    });
};

const saveHandle = () => {
  formRef.value
    ?.validateFields()
    .then(() => {
      if (form.value.minNum > form.value.maxNum) {
        message.error('限制最小值不能大于最大值');
        return;
      }

      const submitForm = { ...form.value };
      if (submitForm.year) {
        submitForm.year = submitForm.year.year() as any;
      }

      addMonitorPlan(submitForm).then((res: any) => {
        if (res.code === 200) {
          message.success(res.msg);
        }
        addDialog.value = false;
        search();
      });
    })
    .catch(error => {
      console.log('表单验证失败:', error);
    });
};

const dictListDate = (dictType: any) => {
  getDictList(dictType).then(res => {
    switch (dictType) {
      case 'monitor_cycle': // eslint-disable-line indent
        monitorCycles.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'monitor_way': // eslint-disable-line indent
        monitorWays.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'discharge_method_and_destination': // eslint-disable-line indent
        dischargeMethodAndDestinations.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'wastewater_type': // eslint-disable-line indent
        wastewaterTypes.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'boiler_power_type': // eslint-disable-line indent
        boilerPowerTypes.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'monitor_point_type': // eslint-disable-line indent
        monitorPointTypes.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'monitor_index': // eslint-disable-line indent
        monitorIndexs.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'station_type': // eslint-disable-line indent
        stationTypes.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'unit': // eslint-disable-line indent
        unitTypes.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'executive_standard': // eslint-disable-line indent
        executiveStandards.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
      case 'monitor_methods_bases': // eslint-disable-line indent
        monitorMethodsBases.value = res.data.data; // eslint-disable-line indent
        break; // eslint-disable-line indent
    }
  });
};

const getTowDeptList = () => {
  getTowLevelDept().then(res => {
    towDeptList.value = res.data;
  });
};

const getThreeDeptList = (deptId: string | undefined) => {
  if (!deptId) {
    threeDeptList.value = [];
    return;
  }
  getDeptList(deptId).then(res => {
    threeDeptList.value = res.data;
  });
};

const handleInfoClick = (row: any) => {
  // router.push({
  //   path: "/environmental/monitor",
  //   query: row,
  // })
};

const deleteHandle = (row: any) => {
  deleteMonitorPlan(row.id).then((res: any) => {
    if (res.code === 200) {
      message.success(res.msg);
      search();
    }
  });
};

const cityList = (type: number, cityId: string) => {
  getCityList({ type, cityId }).then(res => {
    if (type === 1) {
      provinces.value = res.data;
    } else if (type === 2) {
      citys.value = res.data;
    } else {
      areas.value = res.data;
    }
  });
};

const beforeAvatarUpload = (file: File) => {
  const isAllowedType = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
  ].includes(file.type);
  if (!isAllowedType) {
    message.error('只能上传Excel或CSV文件');
  }
  return isAllowedType;
};

const handleUploadChange = (info: any) => {
  if (info.file.status === 'done') {
    const response = info.file.response;
    if (response && response.data) {
      const uploadFile: UploadFileItem = {
        uid: info.file.uid,
        name: response.data.name,
        url: response.data.url,
      };
      fileList.value = [uploadFile];
      fileUrl.value = response.data.url;
    }
  }
};

// 监听器
watch(filterText, val => {
  // Tree filter logic
});

// 生命周期
onMounted(() => {
  search();
  getTreeNanfang();
  getTowDeptList();
  cityList(1, '0');
});
</script>

<style lang="less" scoped>
//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}
.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;
}

.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-body-s {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  transition: left 0.5s;
  top: calc((100% - 89px) / 2);
}

.left-img-s {
  left: 0px;
  transition: left 0.5s;
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  transform: rotate(180deg);
  top: calc((100% - 50px) / 2);
  transition: transform 0.5s, left 0.5s;
  cursor: pointer;
}

.left-img-icon-s {
  left: -8px;
  transform: rotate(0deg);
  transition: transform 0.5s, left 0.5s;
}

.tree-wrapper {
  height: calc(100% - 52px);
  overflow: auto;
  margin-top: 20px;
}

.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  //background: rgba(0, 0, 0, 0.3);
  color: #fff;

  .main {
    overflow: hidden;
    height: 100%;
  }

  .main:nth-child(1) {
    width: 350px;
  }

  .main:nth-child(2) {
    width: calc(100% - 366px);
  }
}

.demo-form-inline {
  margin-top: 16px;
}

.table {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}

// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
