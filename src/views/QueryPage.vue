<template>
  <pro-table :columns="columns" :request="getData">
    <template #extra>
      <a-button type="primary">新增</a-button>
      <a-button>下载</a-button>
    </template>
    <template #action>
      <a>删除</a>
    </template>
  </pro-table>
</template>
<script lang="ts" setup>
import { Column } from '@/types/ProTable';

const columns: Column[] = [
  {
    title: '姓名',
    field: 'name',
    fixed: 'left',
  },
  {
    title: '年龄',
    field: 'age',
  },
  {
    title: '性别',
    field: 'sex',
    valueType: 'Select',
    valueEnum: {
      0: { value: 0, label: '男' },
      1: { value: 1, label: '女' },
    },
  },
  {
    title: '入职时间',
    field: 'joinTime',
    valueType: 'DatePicker',
  },
  {
    title: '小名',
    field: 'alias',
    showSearch: false,
  },
  {
    title: '操作',
    fixed: 'right',
    slots: { default: 'action' },
  },
];

const getData = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        total: 2,
        current: 1,
        records: [
          {
            name: '王大小',
            age: 18,
            sex: 1,
            joinTime: '2022-02-31',
            alias: '东东',
          },
          {
            name: '王二小',
            age: 25,
            sex: 0,
            alias: '西西',
            joinTime: '2022-01-01',
          },
        ],
      });
    }, 300);
  });
};
</script>
