<template>
  <div class="environmental-alarm-page">
    <div
      v-if="isMenu"
      :class="'main-tree'"
      style="background-color: rgba(11, 32, 87, 0.6); z-index: 99998"
    >
      <menuDialog></menuDialog>
    </div>
    <!-- 菜单图标 -->
    <div
      ref="menuRef"
      class="draggable-menu"
      :style="{
        left: `${menuPosition.x}px`,
        top: `${menuPosition.y}px`,
        'z-index': 99999,
      }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>
    <div :class="['main-body-s', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <a-row>
        <a-col :span="12">
          <div class="chart-container">
            <img
              src="@/assets/statistics/pic1.png"
              alt="统计"
              style="width: 70%; height: 60%; z-index: -1; object-fit: contain"
            />
            <!--LDAR-->
            <a href="http://139.224.246.62/permission/login" target="_blank">
              <div
                style="
                  position: absolute;
                  width: 120px;
                  height: 120px;
                  top: 80px;
                  left: 390px;
                "
              ></div>
            </a>
            <!--即时通讯-->
            <div
              style="
                position: absolute;
                width: 120px;
                height: 120px;
                top: 160px;
                left: 0;
              "
              @click="jumpHandle('/environmental/backlogResult')"
            ></div>
            <!--无异味企业-->
            <div
              style="
                position: absolute;
                width: 120px;
                height: 120px;
                top: 0px;
                left: 200px;
              "
              @click="jumpHandle('/environmental/company')"
            ></div>
          </div>
        </a-col>
        <a-col :span="12">
          <a-card class="box-card">
            <template #title>
              <div class="clearfix">
                <span @click="jumpHandle('/environmental/overview')"
                  >污染源分布图</span
                >
                <div>
                  <span style="color: #fff; margin-right: 5px">日期:</span>
                  <a-date-picker
                    v-model:value="environmentalData.day"
                    placeholder="选择日期"
                    format="YYYY-MM-DD"
                  />
                  <a-button
                    type="primary"
                    style="margin-left: 5px"
                    @click="queryHandle()"
                  >
                    查询
                  </a-button>
                </div>
              </div>
            </template>
            <div class="content-jy">
              <column-chart1
                :chart-data="columnDate"
                :height="180"
              ></column-chart1>
            </div>
          </a-card>
          <a-card class="box-card">
            <template #title>
              <div class="clearfix">
                <span @click="jumpHandle('/environmental/alarmQuery')"
                  >在线监测</span
                >
                <div>
                  <span style="color: #fff; margin-right: 5px">日期:</span>
                  <a-date-picker
                    v-model:value="environmentalData.day2"
                    placeholder="选择日期"
                    format="YYYY-MM-DD"
                  />
                  <a-button
                    type="primary"
                    style="margin-left: 5px"
                    @click="queryHandle1()"
                  >
                    查询
                  </a-button>
                </div>
              </div>
            </template>
            <a-row :gutter="20">
              <a-col :span="12">
                <column-chart2
                  :chart-data="columnADate"
                  :title="''"
                ></column-chart2>
              </a-col>
              <a-col :span="6">
                <div style="text-align: center">
                  <a-progress
                    type="circle"
                    :percent="pieWDate"
                    :stroke-color="colors"
                  />
                  <br />
                  <div style="margin-top: 2px">废水超标已处理</div>
                </div>
              </a-col>
              <a-col :span="6">
                <div style="text-align: center">
                  <a-progress
                    type="circle"
                    :percent="pieADate"
                    :stroke-color="colors"
                  />
                  <br />
                  <div style="margin-top: 2px">废气超标已处理</div>
                </div>
              </a-col>
            </a-row>
          </a-card>
          <a-card class="box-card">
            <template #title>
              <div class="clearfix">
                <span @click="jumpHandle('/environmental/monitor')"
                  >自行监测</span
                >
              </div>
            </template>
            <a-row :gutter="20">
              <a-col :span="6">
                <div class="status-card" style="width: 100%; height: 110px">
                  <div
                    style="
                      color: #fff;
                      font-size: 28px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      width: 100%;
                      height: 100%;
                    "
                  >
                    {{ data.year }}年
                  </div>
                </div>
              </a-col>
              <a-col :span="18">
                <a-row :gutter="20">
                  <a-col :span="12">
                    <div class="status-card1" style="width: 100%; height: 50px">
                      <a-row :gutter="20">
                        <a-col :span="8" style="text-align: right">
                          <img
                            src="@/assets/hbgk-imgs/image4.png"
                            style="width: 30px; height: 30px; margin-top: 10px"
                          />
                        </a-col>
                        <a-col :span="16">
                          <div
                            style="
                              font-size: 13px;
                              color: #ffffff;
                              font-weight: 700;
                              margin-top: 10px;
                              text-align: center;
                            "
                          >
                            自行监测方案数量
                            <br />
                            <span
                              style="
                                color: #00ccff;
                                font-size: 11px;
                                font-weight: 700;
                                margin-top: 10px;
                              "
                            >
                              {{ data.summary }}
                            </span>
                          </div>
                        </a-col>
                      </a-row>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div class="status-card1" style="width: 100%; height: 50px">
                      <a-row :gutter="20">
                        <a-col :span="8" style="text-align: right">
                          <img
                            src="@/assets/hbgk-imgs/image1.png"
                            style="width: 30px; height: 30px; margin-top: 10px"
                          />
                        </a-col>
                        <a-col :span="16">
                          <div
                            style="
                              font-size: 13px;
                              color: #ffffff;
                              font-weight: 700;
                              margin-top: 10px;
                              text-align: center;
                            "
                          >
                            进行中监测方案
                            <br />
                            <span
                              style="
                                color: #00ccff;
                                font-size: 11px;
                                font-weight: 700;
                                margin-top: 10px;
                              "
                            >
                              {{ data.count1 }}
                            </span>
                          </div>
                        </a-col>
                      </a-row>
                    </div>
                  </a-col>
                </a-row>
                <a-row :gutter="20" style="margin-top: 10px">
                  <a-col :span="12">
                    <div class="status-card1" style="width: 100%; height: 50px">
                      <a-row :gutter="20">
                        <a-col :span="8" style="text-align: right">
                          <img
                            src="@/assets/hbgk-imgs/image3.png"
                            style="width: 30px; height: 30px; margin-top: 10px"
                          />
                        </a-col>
                        <a-col :span="16">
                          <div
                            style="
                              font-size: 13px;
                              color: #ffffff;
                              font-weight: 700;
                              margin-top: 10px;
                              text-align: center;
                            "
                          >
                            未完成监测方案
                            <br />
                            <span
                              style="
                                color: #00ccff;
                                font-size: 11px;
                                font-weight: 700;
                                margin-top: 10px;
                              "
                            >
                              {{ data.count2 }}
                            </span>
                          </div>
                        </a-col>
                      </a-row>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div class="status-card1" style="width: 100%; height: 50px">
                      <a-row :gutter="20">
                        <a-col :span="8" style="text-align: right">
                          <img
                            src="@/assets/hbgk-imgs/image2.png"
                            style="width: 30px; height: 30px; margin-top: 10px"
                          />
                        </a-col>
                        <a-col :span="16">
                          <div
                            style="
                              font-size: 13px;
                              color: #ffffff;
                              font-weight: 700;
                              margin-top: 10px;
                              text-align: center;
                            "
                          >
                            已完成监测方案
                            <br />
                            <span
                              style="
                                color: #00ccff;
                                font-size: 11px;
                                font-weight: 700;
                                margin-top: 10px;
                              "
                            >
                              {{ data.count3 }}
                            </span>
                          </div>
                        </a-col>
                      </a-row>
                    </div>
                  </a-col>
                </a-row>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { MenuOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import menuDialog from '@/components/menu.vue';
import ColumnChart1 from './components/ColumnChart1.vue';
import ColumnChart2 from './components/ColumnChart2.vue';
import { getMonitorStatistics } from '@/http/environmental-overview';
import { monitorResultChildCount } from '@/http/monitor';
import {
  overMonitorStatistics,
  overMonitorColumnStatistics,
} from '@/http/alarm-query';
import type {
  EnvironmentalData,
  MonitorStatisticsData,
  MenuPosition,
  ChartData,
  ApiResponse,
  OverMonitorParams,
} from './types';

// 路由
const router = useRouter();

// 响应式数据
const colors = ref('red');
const isDragging = ref(false);
const isMenu = ref(false);
const isCollapsed = ref(true);

// 菜单位置
const menuPosition = reactive<MenuPosition>({
  x: 20,
  y: 500,
});

// 环境数据
const environmentalData = reactive<EnvironmentalData>({
  day: dayjs(),
  day2: dayjs(),
});

// 监测数据
const data = reactive<MonitorStatisticsData>({
  year: new Date().getFullYear(),
  summary: 0,
  count1: 0,
  count2: 0,
  count3: 0,
});

// 图表数据
const columnDate = ref<ChartData>([]);
const pieADate = ref<number>(0);
const pieWDate = ref<number>(0);
const columnADate = ref<number[]>([]);

// DOM 引用
const menuRef = ref<HTMLDivElement>();
// 方法定义
const jumpHandle = (url: string) => {
  router.push({
    path: url,
  });
};

const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (_e: MouseEvent) => {
  isDragging.value = true;
  if (!menuRef.value) return;

  const menu = menuRef.value.getBoundingClientRect();

  const onMouseMove = (e: MouseEvent) => {
    const x = e.clientX - menu.width / 2;
    const y = e.clientY - menu.height / 2;

    // 边界限制
    menuPosition.x = Math.max(0, Math.min(x, window.innerWidth - menu.width));
    menuPosition.y = Math.max(0, Math.min(y, window.innerHeight - menu.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};
// 查询方法
const queryHandle = () => {
  let date = '';
  if (environmentalData.day) {
    date = environmentalData.day.format('YYYY-MM-DD');
  }
  getMonitorStatisticsData(date);
};

const queryHandle1 = async () => {
  columnADate.value = [];
  await overMonitorStatisticsData(10);
  await overMonitorStatisticsData(20);
  console.log(' columnADate:', columnADate.value);
};
// 监测点统计
const getMonitorStatisticsData = (date: string) => {
  getMonitorStatistics({ date }).then((res: ApiResponse) => {
    columnDate.value = res.data;
  });
};

const getMonitorResultChildCountData = () => {
  monitorResultChildCount().then((res: ApiResponse<MonitorStatisticsData>) => {
    Object.assign(data, res.data);
  });
};

// 超标统计
const overMonitorStatisticsData = async (monitorType: number) => {
  const form: OverMonitorParams = {
    startTime: '',
    endTime: '',
    vldSiteId: '',
    monitorType,
  };

  if (environmentalData.day2) {
    form.startTime = environmentalData.day2.format('YYYY-MM-DD 00:00:00');
    form.endTime = environmentalData.day2.format('YYYY-MM-DD 23:59:59');
  }

  overMonitorStatistics(form).then((response: ApiResponse) => {
    if (monitorType === 10) {
      const num1 = response.data[0].value;
      const num2 = response.data[1].value;
      if (num1 + num2 === 0) {
        pieWDate.value = 0;
      } else {
        pieWDate.value = Number(((num1 / (num1 + num2)) * 100).toFixed(2));
      }
    }
    if (monitorType === 20) {
      const num1 = response.data[0].value;
      const num2 = response.data[1].value;
      if (num1 + num2 === 0) {
        pieADate.value = 0;
      } else {
        pieADate.value = Number(((num1 / (num1 + num2)) * 100).toFixed(2));
      }
    }
  });

  overMonitorColumnStatistics(form).then((response: ApiResponse) => {
    console.log(response);
    if (monitorType === 10) {
      const data1 = response.data[1];
      columnADate.value.push(data1[0]);
      columnADate.value.push(data1[1]);
    }
    if (monitorType === 20) {
      const data1 = response.data[1];
      columnADate.value.push(data1[0]);
      columnADate.value.push(data1[1]);
    }
  });
};

// 生命周期
onMounted(() => {
  queryHandle();
  getMonitorResultChildCountData();
  queryHandle1();
});
</script>

<style lang="less" scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%; /* 根据实际需要调整 */
}

//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

//---
div::-webkit-scrollbar {
  display: none;
}

.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  ::v-deep .el-input--small {
    border: 1px solid #00409a;
    border-radius: 5px;
  }

  ::v-deep .el-input__inner {
    box-shadow: 0 0 0.05rem transparent inset;
    background: rgba(0, 27, 64, 0.6);
  }
}

.main-body {
  height: calc(100% - 85px);
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  :deep(.ant-input) {
    color: #fff;
    background-color: #073aa9;
    border: 0;
    box-shadow: 0 0 0.05rem #86a5e7 inset;
  }
}

.main-body-s {
  height: calc(100% - 85px);
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 0px 24px;
  transition: left 0.5s;

  :deep(.ant-input) {
    color: #fff;
    background-color: #073aa9;
    border: 0;
    box-shadow: 0 0 0.05rem #86a5e7 inset;
  }
}

.environmental-alarm-page {
  // background: url("~@/assets/hbgk-imgs/img.png");
  width: 100%;
  height: 100%;
  color: #ffffff;
  display: block;
  position: relative;
}

:deep(.ant-card) {
  background: transparent;
  border: 1px solid rgb(43 141 197);
  color: #ffffff;
  width: 100%;
  margin-top: 10px;

  .ant-card-head {
    border-color: rgb(43 141 197);
    background-color: rgba(7, 58, 169, 0.3);

    .clearfix:after {
      display: none;
    }

    .clearfix {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span:nth-child(1) {
        font-size: 14px;
      }

      span:nth-child(2) {
        font-size: 12px;
      }
    }
  }

  .ant-card-body {
    font-size: 12px;
    background-color: rgba(7, 58, 169, 0);
  }
}

.select-jy {
  position: static;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.select-jy-jy {
  justify-content: flex-start;
}

.content-jy {
  width: 100%;
  height: calc(100% - 51px);
  display: flex;

  #echartsJy {
    margin-right: 16px;
  }

  div {
    width: calc(50% - 8px);
  }
}

:deep(.ant-form-item-label) {
  color: #ffffff;
}

:deep(.ant-input) {
  color: #fff;
  background-color: #073aa9;
  border: 0;
  box-shadow: 0 0 0.05rem #86a5e7 inset;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.year {
  margin: 5px 0 20px;
  font-size: 16px;
  opacity: 0.8;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.status-card {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
}

.status-card1 {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.status-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-label {
  font-size: 14px;
}

/* 不同状态的样式 */
.completed .status-icon {
  background-color: #1976d2; /* 蓝色 */
}

.in-progress .status-icon {
  background-color: #4caf50; /* 绿色 */
}

.not-completed .status-icon {
  background-color: #8bc34a; /* 浅绿色 */
}

.pending .status-icon {
  background-color: #0d47a1; /* 深蓝色 */
}

:deep(.ant-progress-text) {
  color: #fff;
}
</style>
