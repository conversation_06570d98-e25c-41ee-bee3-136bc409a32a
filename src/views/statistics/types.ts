// 统计页面相关类型定义
import type { Dayjs } from 'dayjs';

// 环境数据接口
export interface EnvironmentalData {
  day: Dayjs | undefined;
  day2: Dayjs | undefined;
}

// 监测统计数据接口
export interface MonitorStatisticsData {
  year: number;
  summary: number;
  count1: number;
  count2: number;
  count3: number;
}

// 图表数据接口
export type ChartData = (number[] | string[])[];

// API 响应接口
export interface ApiResponse<T = any> {
  data: T;
  code?: number;
  message?: string;
  success?: boolean;
}

// 监测统计 API 参数
export interface MonitorStatisticsParams {
  date?: string;
}

// 超标监测 API 参数
export interface OverMonitorParams {
  startTime: string;
  endTime: string;
  vldSiteId: string;
  monitorType: number;
}

// 拖拽菜单位置
export interface MenuPosition {
  x: number;
  y: number;
}

// 超标统计响应数据
export interface OverMonitorResponse {
  data: Array<{
    name: string;
    value: number;
  }>;
}

// 柱状图统计响应数据
export interface ColumnStatisticsResponse {
  data: Array<number[]>;
}
