<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import * as echarts from 'echarts';

// 定义 Props 类型
interface Props {
  chartData?: number[];
  timeData?: string[];
  title?: string;
  height?: number | string;
}

// 定义 Props 默认值
const props = withDefaults(defineProps<Props>(), {
  chartData: () => [],
  timeData: () => [],
  title: '',
  height: '100%',
});

// 定义响应式变量
const chartRef = ref<HTMLDivElement>();
let myChart: echarts.ECharts | null = null;

// 计算属性：处理高度值
const computedHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`;
  }
  return props.height;
});

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return;

  // 销毁旧实例
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }

  await nextTick();

  // 创建新实例
  myChart = echarts.init(chartRef.value);

  // 配置项
  const option: echarts.EChartsOption = {
    title: {
      text: props.title,
      textStyle: {
        color: '#fff',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: props.title ? '15%' : '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        color: '#99b7d2',
      },
    },
    yAxis: {
      type: 'category',
      data: ['COD超标', '氨氮超标', 'SO2超标', 'NOX超标'],
      axisLabel: {
        color: '#99b7d2',
      },
    },
    series: [
      {
        name: '超标',
        type: 'bar',
        data: props.chartData,
        itemStyle: {
          color: 'rgba(83, 240, 233, 0.8)',
        },
        barWidth: '20%',
        label: {
          show: true,
          position: 'right',
          color: '#fff',
        },
      },
    ],
  };

  myChart.setOption(option);
  handleResize(); // 初始调整大小
};

// 处理窗口大小变化
const handleResize = () => {
  if (myChart) {
    myChart.resize();
  }
};

// 监听 chartData 变化
watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  console.log('ColumnChart2 component mounted.');
  initChart();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  console.log('ColumnChart2 component unmounted.');
  window.removeEventListener('resize', handleResize);
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: v-bind(computedHeight);
  min-height: 180px;
}
</style>
