<template>
  <div ref="chartRef" :style="'width: 100%; height: 100%;'"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

// 定义 Props 类型
interface Props {
  chartData?: (number[] | string[])[];
  timeData?: string[];
  title?: string;
}

// 定义 Props 默认值
const props = withDefaults(defineProps<Props>(), {
  chartData: () => [],
  timeData: () => [],
  title: '',
});

// 定义响应式变量
const chartRef = ref<HTMLDivElement>();
let myChart: echarts.ECharts | null = null;

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return;

  // 销毁旧实例
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }

  await nextTick();

  // 创建新实例
  myChart = echarts.init(chartRef.value);

  const option: echarts.EChartsOption = {
    backgroundColor: 'transparent',
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: {
      type: 'category',
      data: props.chartData[0] || [],
      axisLabel: {
        color: '#99b7d2',
        interval: 0,
        rotate: 25,
        fontSize: 12,
      },
      axisLine: {
        lineStyle: {
          color: '#2a3f6f',
        },
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      interval: 25,
      axisLabel: {
        color: '#99b7d2',
        formatter: (value: number) => value.toFixed(1),
      },
      axisLine: {
        lineStyle: {
          color: '#2a3f6f',
        },
      },
      splitLine: {
        lineStyle: {
          color: '#2a3f6f',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '废气超标量',
        type: 'bar',
        data: props.chartData[1] || [],
        itemStyle: {
          color: 'rgba(83, 240, 233, 0.8)',
          borderRadius: [2, 2, 0, 0],
        },
        barWidth: '20%',
        label: {
          show: true,
          position: 'top',
          color: '#fff',
        },
      },
      {
        name: '废水超标量',
        type: 'bar',
        data: props.chartData[2] || [],
        itemStyle: {
          color: 'rgba(255, 183, 77, 0.8)',
          borderRadius: [2, 2, 0, 0],
        },
        barWidth: '20%',
        label: {
          show: true,
          position: 'top',
          color: '#fff',
        },
      },
    ],
  };

  myChart.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (myChart) {
    myChart.resize();
  }
};

// 监听 chartData 变化
watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  console.log('ColumnChart1 component mounted.');
  initChart();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  console.log('ColumnChart1 component unmounted.');
  window.removeEventListener('resize', handleResize);
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style scoped>
/* 确保图表容器填满父容器 */
div {
  width: 100%;
  height: 100%;
  min-height: 150px; /* 设置最小高度防止内容过小 */
}
</style>
