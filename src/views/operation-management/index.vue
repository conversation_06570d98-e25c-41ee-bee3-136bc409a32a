<template>
  <div class="environmental-alarm-page">
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <a-input
        v-model:value="filterText"
        class="search"
        placeholder="输入关键词"
        allow-clear
        size="small"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="tree-wrapper">
        <a-tree
          ref="monitorList"
          :tree-data="treeData"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'vldSiteId',
          }"
          :show-line="false"
          :show-icon="false"
          @select="handleNodeClick"
        />
      </div>
    </div>

    <img
      src="@/assets/images/shouqi.png"
      alt=""
      :class="['left-img', isCollapsed ? 'left-img-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <img
      src="@/assets/images/shouqiicon.png"
      alt=""
      :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />

    <!-- 菜单图标 -->
    <div
      ref="menu"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>

    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog />
    </div>

    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <a-tabs v-model:active-key="activeName" @change="handleClick">
        <a-tab-pane key="0" tab="设备标定信息" />
        <a-tab-pane key="1" tab="对比监测信息" />
      </a-tabs>

      <a-form :model="formData" layout="inline" class="demo-form-inline">
        <a-form-item v-if="name === '0'" label="标定日期:">
          <a-range-picker
            v-model:value="formData.timeArr"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
          />
        </a-form-item>

        <a-form-item v-if="name === '0'" label="标定类型:">
          <a-select
            v-model:value="formData.calibrationType"
            placeholder="请选择"
            allow-clear
          >
            <a-select-option value="1">计划标定</a-select-option>
            <a-select-option value="2">非计划标定</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item v-if="name === '0'" label="监测项目名称：">
          <a-input
            v-model:value="formData.monitorProject"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>

        <a-form-item v-if="name === '1'" label="比对日期:">
          <a-range-picker
            v-model:value="formData.timeArr"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
          />
        </a-form-item>

        <a-form-item v-if="name === '1'" label="监测项目：">
          <a-input
            v-model:value="formData.paramName"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>

        <a-form-item v-if="name === '1'" label="比对结果:">
          <a-select
            v-model:value="formData.result"
            placeholder="请选择"
            allow-clear
          >
            <a-select-option value="0">合格</a-select-option>
            <a-select-option value="1">不合格</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item class="ant-form-item-bottom">
          <a-space>
            <a-button type="primary" @click="search">查询</a-button>
            <a-button @click="resetForm">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
      <div class="table-scrollbar">
        <div class="table-wrapper">
          <a-table
            v-if="name === '0'"
            :columns="calibrationColumns"
            :data-source="tableData1"
            :pagination="false"
            :scroll="{ x: 'max-content', y: 'max-content' }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-if="column.key === 'calibrationType'">
                <span v-if="record.calibrationType === '1'">计划标定</span>
                <span v-if="record.calibrationType === '2'">非计划标定</span>
              </template>
            </template>
          </a-table>

          <a-table
            v-if="name === '1'"
            :columns="compareColumns"
            :data-source="tableData2"
            :pagination="false"
            :scroll="{ x: 'max-content', y: 'max-content' }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-if="column.key === 'unit'">
                <span v-if="record.unit === 0">l/s</span>
                <span v-if="record.unit === 1">t</span>
                <span v-if="record.unit === 2">mg/l</span>
                <span v-if="record.unit === 3">m3/s</span>
                <span v-if="record.unit === 4">m3</span>
                <span v-if="record.unit === 5">mg/m3</span>
              </template>
              <template v-if="column.key === 'result'">
                <span v-if="record.result === 0">合格</span>
                <span v-if="record.result === 1">不合格</span>
              </template>
            </template>
          </a-table>
        </div>
        <a-pagination
          v-model:current="formData.pageNum"
          v-model:page-size="formData.pageSize"
          :total="total"
          :show-size-changer="true"
          :page-size-options="['20', '40', '60', '80', '100']"
          show-quick-jumper
          style="text-align: right; margin-top: 16px"
          @change="handleCurrentChange"
          @show-size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import type { TableColumnsType } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';
import { calibrationPage, comparePage } from '@/http/operation-management';
import { getTreeNode } from '@/http/environmental-overview';
import menuDialog from '@/components/menu.vue';

// 类型定义
interface TreeNode {
  id: string;
  vldSiteId: string;
  siteShortName: string;
  children?: TreeNode[];
}

interface FormData {
  timeArr: [Dayjs, Dayjs] | null;
  monitorProject: string;
  calibrationType: string;
  paramName: string;
  result: string;
  siteId: string;
  pageNum: number;
  pageSize: number;
}

interface CalibrationData {
  vldSiteId: string;
  startDate: string;
  endDate: string;
  mBasicMonitorTbId: string;
  calibrationType: string;
  monitorProject: string;
}

interface CompareData {
  vldSiteId: string;
  mBasicMonitorTbId: string;
  startDate: string;
  endDate: string;
  paramName: string;
  unit: number;
  numberValue: number;
  personValue: number;
  result: number;
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const filterText = ref('');
const isCollapsed = ref(false);
const name = ref('0');
const activeName = ref('0');
const total = ref(0);
const monitorList = ref();

const formData = reactive<FormData>({
  timeArr: null,
  monitorProject: '',
  calibrationType: '',
  paramName: '',
  result: '',
  siteId: '',
  pageNum: 1,
  pageSize: 20,
});

const treeData = ref<TreeNode[]>([]);
const tableData1 = ref<CalibrationData[]>([]); // 设备标定信息
const tableData2 = ref<CompareData[]>([]); // 对比监测信息

// 表格列定义
const calibrationColumns: TableColumnsType = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  { title: '单位', dataIndex: 'vldSiteId', key: 'vldSiteId', align: 'center' },
  {
    title: '开始日期',
    dataIndex: 'startDate',
    key: 'startDate',
    align: 'center',
  },
  { title: '截止日期', dataIndex: 'endDate', key: 'endDate', align: 'center' },
  {
    title: '监测点',
    dataIndex: 'mBasicMonitorTbId',
    key: 'mBasicMonitorTbId',
    align: 'center',
  },
  { title: '标定类型', key: 'calibrationType', align: 'center' },
  {
    title: '监测项目名称',
    dataIndex: 'monitorProject',
    key: 'monitorProject',
    align: 'center',
  },
];

const compareColumns: TableColumnsType = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  { title: '单位', dataIndex: 'vldSiteId', key: 'vldSiteId', align: 'center' },
  {
    title: '监测点',
    dataIndex: 'mBasicMonitorTbId',
    key: 'mBasicMonitorTbId',
    align: 'center',
  },
  {
    title: '比对开始日期',
    dataIndex: 'startDate',
    key: 'startDate',
    align: 'center',
  },
  {
    title: '比对截止日期',
    dataIndex: 'endDate',
    key: 'endDate',
    align: 'center',
  },
  {
    title: '监测项目',
    dataIndex: 'paramName',
    key: 'paramName',
    align: 'center',
  },
  { title: '计量单位', key: 'unit', align: 'center' },
  {
    title: '监测值',
    dataIndex: 'numberValue',
    key: 'numberValue',
    align: 'center',
  },
  {
    title: '人工检测值',
    dataIndex: 'personValue',
    key: 'personValue',
    align: 'center',
  },
  { title: '比对结果', key: 'result', align: 'center' },
];

// 方法定义
const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const menu = (e.target as HTMLElement)
    ?.closest('.draggable-menu')
    ?.getBoundingClientRect();
  if (!menu) return;

  const onMouseMove = (e: MouseEvent) => {
    const newX = e.clientX - menu.width / 2;
    const newY = e.clientY - menu.height / 2;

    // 边界限制
    x.value = Math.max(0, Math.min(newX, window.innerWidth - menu.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menu.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

// 处理子组件触发的事件
const handleNodeClick = (selectedKeys: string[], info: any) => {
  if (selectedKeys.length > 0) {
    formData.siteId = selectedKeys[0];
    // 调用接口
    search();
  }
};

// 查询
const search = () => {
  const form: any = {
    startTime: '',
    endTime: '',
    monitorProject: formData.monitorProject,
    calibrationType: formData.calibrationType,
    paramName: formData.paramName,
    result: formData.result,
    siteId: formData.siteId,
    pageNum: formData.pageNum,
    pageSize: formData.pageSize,
  };

  if (formData.timeArr) {
    form.startDate = formData.timeArr[0].format('YYYY-MM-DD HH:mm:00');
    form.endDate = formData.timeArr[1].format('YYYY-MM-DD 23:59:59');
  }

  if (name.value === '0') {
    getTableData1(form);
  } else if (name.value === '1') {
    getTableData2(form);
  }
};

// tabs切换
const handleClick = (key: string) => {
  Object.assign(formData, {
    timeArr: null,
    monitorProject: '',
    calibrationType: '',
    paramName: '',
    result: '',
    siteId: '',
    pageNum: 1,
    pageSize: 20,
  });
  name.value = key;
  search();
};

// 设备标定信息
const getTableData1 = async (form: any) => {
  try {
    const res = await calibrationPage(form);
    tableData1.value = res.rows;
    total.value = res.total;
  } catch (error) {
    message.error('获取标定信息失败');
  }
};

// 对比监测信息
const getTableData2 = async (form: any) => {
  try {
    const res = await comparePage(form);
    tableData2.value = res.rows;
    total.value = res.total;
  } catch (error) {
    message.error('获取对比信息失败');
  }
};

// 分页
const handleSizeChange = (current: number, size: number) => {
  formData.pageSize = size;
  search();
};

const handleCurrentChange = (page: number) => {
  formData.pageNum = page;
  search();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    timeArr: null,
    monitorProject: '',
    calibrationType: '',
    paramName: '',
    result: '',
    siteId: '',
    pageNum: 1,
    pageSize: 20,
  });
  search();
};

// 获取组织机构数据-南方
const getTreeNanfang = async () => {
  try {
    const res = await getTreeNode();
    treeData.value = res.data;
  } catch (error) {
    message.error('获取组织机构数据失败');
  }
};

// 监听器
watch(filterText, val => {
  // 这里需要实现树的过滤功能
  console.log('Filter text:', val);
});

onMounted(() => {
  search();
  getTreeNanfang();
});
</script>
<style lang="less" scoped>
div::-webkit-scrollbar {
  display: none;
}
//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  ::v-deep .el-input--small {
    border: 1px solid #00409a;
    border-radius: 5px;
  }

  ::v-deep .el-input__inner {
    box-shadow: 0 0 0.05rem transparent inset;
    background: rgba(0, 27, 64, 0.6);
  }
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;

  ::v-deep .el-input__inner {
    box-shadow: 0 0 0.05rem transparent inset;
    background: rgba(0, 27, 64, 0.6);
  }
}

.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .el-input__inner {
    color: #fff;
    background-color: #073aa9;
    border: 0;
    box-shadow: 0 0 0.05rem #86a5e7 inset;
  }
}

.main-body-s {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  transition: left 0.5s;
  top: calc((100% - 89px) / 2);
}

.left-img-s {
  left: 0px;
  transition: left 0.5s;
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  transform: rotate(180deg);
  top: calc((100% - 50px) / 2);
  transition: transform 0.5s, left 0.5s;
  cursor: pointer;
}

.left-img-icon-s {
  left: -8px;
  transform: rotate(0deg);
  transition: transform 0.5s, left 0.5s;
}

.tree-wrapper {
  height: calc(100% - 52px);
  overflow: auto;
  margin-top: 20px;
}

.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  color: #ffffff;
  display: block;

  .main {
    overflow: hidden;
    height: 100%;
  }

  .main:nth-child(1) {
    width: 350px;
  }

  .main:nth-child(2) {
    width: calc(100% - 366px);
  }

  .unit {
    ::v-deep .el-tree {
      height: 500px;
      overflow: auto;
      background-color: transparent;
      color: #ffffff;

      .el-tree-node__content:hover,
      .el-tree-node__content:focus,
      .el-tree-node__content:active {
        background-color: transparent !important;
        color: #0bc4ff;
      }

      .el-tree-node .el-tree-node__content,
      .is-current .el-tree-node__content,
      .is-focusable .el-tree-node__content {
        background-color: transparent !important;
      }

      .el-tree--highlight-current
        .el-tree-node.is-current
        > .el-tree-node__content {
        color: #0bc4ff !important;
      }
    }
  }
}

.demo-form-inline {
  margin-top: 16px;
}

::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 16px;
}

::v-deep .number {
  letter-spacing: 0px;
}

.table-scrollbar {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}
// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
