<template>
  <div ref="chartRef" :style="`width: 100%; height: ${height}px;`"></div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';

// 定义数据接口
interface ChartDataItem {
  name: string;
  value: number;
}

interface Props {
  chartData: ChartDataItem[];
  title?: string;
  height?: number;
}

// 定义props
const props = withDefaults(defineProps<Props>(), {
  chartData: () => [],
  title: '',
  height: 300,
});

// 响应式引用
const chartRef = ref<HTMLDivElement>();
let myChart: echarts.ECharts | null = null;

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return;

  await nextTick();

  // 销毁已存在的图表
  if (myChart) {
    myChart.dispose();
  }

  // 基于准备好的dom，初始化echarts实例
  myChart = echarts.init(chartRef.value);

  // 指定图表的配置项和数据
  const datas = [props.chartData];

  const option: echarts.EChartsOption = {
    title: {
      text: props.title,
      textStyle: {
        color: '#fff',
      },
    },
    series: datas.map(function (data, idx) {
      return {
        type: 'pie',
        radius: [20, 60],
        top: 30 + '%',
        height: '33.33%',
        left: 'center',
        width: 500,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
          // 设置渐变色
          color: function (params: any) {
            // 定义渐变色
            const colors = [
              { start: '#011b37', end: '#024387' }, // 蓝色渐变
              { start: '#f60505', end: 'rgba(57,2,2,0.81)' }, // 红色渐变
            ];
            const colorIndex = params.dataIndex % colors.length; // 循环使用渐变色
            return {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: colors[colorIndex].start }, // 渐变起始颜色
                { offset: 1, color: colors[colorIndex].end }, // 渐变结束颜色
              ],
            };
          },
        },
        label: {
          alignTo: 'edge',
          formatter: (params: any) => {
            const total = (params.data.value / params.percent) * 100; // 计算总和
            const percent = ((params.data.value / total) * 100).toFixed(2); // 计算百分比
            return `{name|${params.name}}\n{time|${
              percent === 'NaN' ? 0 : percent
            } % (${params.data.value})}`;
          },
          minMargin: 5,
          edgeDistance: 10,
          lineHeight: 15,
          rich: {
            time: {
              fontSize: 10,
              color: '#999',
            },
          },
        },
        labelLine: {
          length: 15,
          length2: 0,
          maxSurfaceAngle: 80,
        },
        labelLayout: function (params: any) {
          if (!myChart) return {};
          const isLeft = params.labelRect.x < myChart.getWidth() / 2;
          const points = params.labelLinePoints;
          // Update the end point.
          points[2][0] = isLeft
            ? params.labelRect.x
            : params.labelRect.x + params.labelRect.width;
          return {
            labelLinePoints: points,
          };
        },
        data,
      };
    }),
  };

  // 使用刚指定的配置项和数据显示图表
  myChart.setOption(option);
};

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

// 组件挂载后初始化图表
onMounted(() => {
  initChart();
});

// 组件卸载时清理
onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style scoped>
/* 图表容器样式 */
</style>
