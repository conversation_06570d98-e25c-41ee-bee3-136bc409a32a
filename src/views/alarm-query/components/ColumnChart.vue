<template>
  <div ref="chartRef" :style="`width: 100%; height: ${height}px;`"></div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';

// 定义接口
interface Props {
  chartData: number[][];
  timeData?: string[];
  title?: string;
  height?: number;
}

// 定义props
const props = withDefaults(defineProps<Props>(), {
  chartData: () => [],
  timeData: () => [],
  title: '',
  height: 300,
});

// 响应式引用
const chartRef = ref<HTMLDivElement>();
let myChart: echarts.ECharts | null = null;

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return;

  await nextTick();

  // 销毁已存在的图表
  if (myChart) {
    myChart.dispose();
  }

  // 基于准备好的dom，初始化echarts实例
  myChart = echarts.init(chartRef.value);

  // 指定图表的配置项和数据
  const option: echarts.EChartsOption = {
    title: {
      text: props.title,
      textStyle: {
        color: '#fff',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow', // 鼠标悬停时显示阴影
      },
    },
    legend: {
      data: ['超标', '未超标'], // 根据实际系列名称修改
      bottom: '5%',
      left: 'center',
      icon: 'circle',
      textStyle: {
        color: '#fff',
      },
    },
    xAxis: {
      type: 'category',
      data: props.chartData[0] || [], // x轴数据
      axisLabel: {
        color: '#99b7d2', // x轴字体颜色
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#99b7d2', // y轴字体颜色
      },
    },
    series: [
      {
        name: '超标', // 系列名称
        type: 'bar', // 柱状图
        data: props.chartData[1] || [], // 系列1的数据
        itemStyle: {
          color: 'rgba(83, 240, 233, 0.8)', // 系列1的颜色
        },
        barWidth: '20%', // 柱状图宽度
      },
      {
        name: '未超标', // 系列名称
        type: 'bar', // 柱状图
        data: props.chartData[2] || [], // 系列2的数据
        itemStyle: {
          color: 'rgba(255, 183, 77, 0.8)', // 系列2的颜色
        },
        barWidth: '20%', // 柱状图宽度
      },
    ],
  };

  // 使用刚指定的配置项和数据显示图表
  myChart.setOption(option);
};

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    initChart();
  },
  { deep: true }
);

// 组件挂载后初始化图表
onMounted(() => {
  initChart();
});

// 组件卸载时清理
onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style scoped>
/* 图表容器样式 */
</style>
