<!-- 废气超标数据查询页面 -->
<template>
  <div class="environmental-alarm-page">
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <a-input
        v-model:value="filterText"
        class="search"
        placeholder="输入关键词"
        allow-clear
        size="small"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="tree-wrapper">
        <a-tree
          ref="monitorList"
          v-model:selected-keys="selectedTreeKeys"
          :tree-data="filteredTreeData"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'vldSiteId',
          }"
          :show-line="false"
          :show-icon="false"
          @select="handleNodeClick"
        >
        </a-tree>
      </div>
    </div>
    <img
      src="@/assets/images/shouqi.png"
      alt=""
      :class="['left-img', isCollapsed ? 'left-img-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <img
      src="@/assets/images/shouqiicon.png"
      alt=""
      :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <!-- 菜单图标 -->
    <div
      ref="menu"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>
    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog></menuDialog>
    </div>

    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <a-tabs v-model:active-key="activeName" @change="handleClick">
        <a-tab-pane key="0" tab="废水超标数据" />
        <a-tab-pane key="1" tab="废气超标数据" />
        <a-tab-pane key="2" tab="超标数据统计分析" />
      </a-tabs>
      <a-form
        ref="formRef"
        layout="inline"
        :model="formData"
        class="demo-form-inline"
      >
        <a-form-item label="监测时间:">
          <a-range-picker
            v-model:value="formData.timeArr"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
          />
        </a-form-item>
        <a-form-item v-if="name != '2'" label="处置情况:">
          <a-select
            v-model:value="formData.handlingFlag"
            placeholder="请选择"
            allow-clear
            style="width: 120px"
          >
            <a-select-option :value="0">未完成</a-select-option>
            <a-select-option :value="1">已完成</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item class="ant-form-item-bottom">
          <a-space>
            <a-button style="margin-left: 100px" type="primary" @click="search">
              查询
            </a-button>
            <a-button @click="resetForm">重置</a-button>
            <a-button v-if="name != '2'" type="primary" @click="exportXlsx">
              数据导出
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
      <div v-if="name != '2'" class="table">
        <div class="table-wrapper">
          <a-table
            v-show="name === '0'"
            :data-source="tableData1"
            :columns="wasteWaterColumns"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'cumulativeDischarge'">
                {{
                  sumWithTwoDecimals(record.cwater011All, record.cwater060All)
                }}
              </template>
              <template v-else-if="column.key === 'handlingFlag'">
                <span v-if="record.handlingFlag === '0'" style="color: red">
                  未完成
                </span>
                <span v-if="record.handlingFlag === '1'"> 已完成 </span>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="link"
                  size="small"
                  :disabled="record.handlingFlag === '1'"
                  @click="dialogHandle(record as TableRecord)"
                  >处置
                </a-button>
              </template>
            </template>
          </a-table>
          <a-table
            v-show="name === '1'"
            :data-source="tableData2"
            :columns="wasteGasColumns"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'so2Range'">
                {{
                  record.so2Range.split('/')[0] === undefined
                    ? 0
                    : record.so2Range.split('/')[0]
                }}
              </template>
              <template v-else-if="column.key === 'noxRange'">
                {{
                  record.noxRange.split('/')[0] === undefined
                    ? 0
                    : record.noxRange.split('/')[0]
                }}
              </template>
              <template v-else-if="column.key === 'handlingFlag'">
                <span v-if="record.handlingFlag === '0'" style="color: red">
                  未完成
                </span>
                <span v-if="record.handlingFlag === '1'"> 已完成 </span>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="link"
                  size="small"
                  :disabled="record.handlingFlag === '1'"
                  @click="dialogHandle(record as TableRecord)"
                  >处置
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
        <a-pagination
          v-if="name !== '2'"
          v-model:current="formData.pageNum"
          v-model:page-size="formData.pageSize"
          :total="total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :page-size-options="['20', '40', '60', '80', '100']"
          :show-total="total => `共 ${total} 条`"
          style="margin-top: 16px; text-align: right"
          @change="onPageChange"
        />
      </div>
      <div v-if="name === '2'">
        <a-row>
          <a-col :span="12">
            <pie-chart
              :chart-data="pieWDate"
              :title="'废水处置情况'"
            ></pie-chart>
          </a-col>
          <a-col :span="12">
            <pie-chart
              :chart-data="pieADate"
              :title="'废气处置情况'"
            ></pie-chart>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <column-chart
              :chart-data="columnWDate"
              :title="'废水超标情况'"
            ></column-chart>
          </a-col>
          <a-col :span="12">
            <column-chart
              :chart-data="columnADate"
              :title="'废气超标情况'"
            ></column-chart>
          </a-col>
        </a-row>
      </div>
    </div>
    <!-- 模态框部分 - 添加调试和兼容性改进 -->
    <a-modal
      v-model:open="dialogVisible"
      title="报警处置"
      :footer="null"
      :z-index="9999"
      :mask-closable="false"
      :destroy-on-close="true"
      :get-container="false"
      :width="500"
      :centered="true"
      class="disposal-modal"
    >
      <a-form ref="form" :model="form" :label-col="{ span: 6 }">
        <a-form-item label="处理结果：">
          <a-textarea
            v-model:value="form.reason"
            :rows="4"
            placeholder="请输入处理结果"
          />
        </a-form-item>
      </a-form>
      <div style="text-align: right; margin-top: 16px">
        <a-button @click="handleCancel">取 消</a-button>
        <a-button type="primary" style="margin-left: 8px" @click="okHandle"
          >确 定</a-button
        >
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed } from 'vue';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { TableColumnProps, TreeProps } from 'ant-design-vue';
import type { Key } from 'ant-design-vue/es/_util/type';
import dayjs, { type Dayjs } from 'dayjs';
import {
  overMonitorPage,
  handleNew,
  overMonitorStatistics,
  overMonitorColumnStatistics,
} from '@/http/alarm-query';
import { exportJsonToExcel } from '@/excel/Export2Excel';
import PieChart from './components/PieChart.vue';
import ColumnChart from './components/ColumnChart.vue';
import { getTreeNode } from '@/http/environmental-overview';
import menuDialog from '@/components/menu.vue';

// 接口定义
interface FormData {
  timeArr: [Dayjs, Dayjs] | undefined;
  vldSiteId: string;
  monitorType: string;
  handlingFlag: string | number;
  pageNum: number;
  pageSize: number;
}

interface DialogForm {
  id: string;
  monitorType: string;
  reason: string;
}

interface TreeNode {
  key: string;
  title: string;
  siteShortName: string;
  vldSiteId: string;
  children?: TreeNode[];
}

interface TableRecord {
  id: string;
  site: string;
  monitorName: string;
  mTime: string;
  cwater011All: number;
  cwater060All: number;
  cwater011: number;
  cwater011Upper: number;
  cwater060: number;
  cwater060Upper: number;
  cwater001: number;
  flag: string;
  handlingFlag: string;
  content: string;
  reason: string;
  so2Range: string;
  noxRange: string;
  cair02R: number;
  cair02: number;
  cair03R: number;
  cair03: number;
  cair03All: number;
  cairS03: number;
  cairS08: number;
  cairS05: number;
  cairS01: number;
  [key: string]: any;
}

interface ChartDataItem {
  name: string;
  value: number;
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const filterText = ref('');
const isCollapsed = ref(false);
const dialogVisible = ref(false);
const form = ref<DialogForm>({
  id: '',
  monitorType: '',
  reason: '',
});

const name = ref('0');
const activeName = ref('0');
const formData = ref<FormData>({
  timeArr: undefined,
  vldSiteId: '',
  monitorType: '',
  handlingFlag: '',
  pageNum: 1,
  pageSize: 20,
});

const total = ref(0);
const treeData = ref<TreeNode[]>([]);
const tableData1 = ref<TableRecord[]>([]);
const tableData2 = ref<TableRecord[]>([]);
const pieADate = ref<ChartDataItem[]>([]);
const pieWDate = ref<ChartDataItem[]>([]);
const columnADate = ref<number[][]>([]);
const columnWDate = ref<number[][]>([]);
const selectedTreeKeys = ref<Key[]>([]); // 新增：用于控制树形选择的高亮

const onPageChange = (page: number, size: number) => {
  formData.value.pageNum = page || 1;
  formData.value.pageSize = size;
  search();
};

const filteredTreeData = computed(() => {
  if (!filterText.value) {
    return treeData.value;
  }
  return filterTree(treeData.value, filterText.value);
});

// Recursive filter function
const filterTree = (tree: TreeNode[], filterText: string): TreeNode[] => {
  return tree.reduce((acc: TreeNode[], node: TreeNode) => {
    const matches = node.siteShortName.includes(filterText);
    const children = node.children ? filterTree(node.children, filterText) : [];

    if (matches || children.length) {
      acc.push({ ...node, children: children.length ? children : undefined });
    }
    return acc;
  }, []);
};

// 表格列定义
const wasteWaterColumns: TableColumnProps[] = [
  { title: '序号', key: 'index', width: 120, align: 'center', fixed: 'left' },
  {
    title: '单位',
    dataIndex: 'site',
    key: 'site',
    width: 180,
    align: 'center',
  },
  {
    title: '监测点',
    dataIndex: 'monitorName',
    key: 'monitorName',
    width: 180,
    align: 'center',
  },
  {
    title: '监测时间',
    dataIndex: 'mTime',
    key: 'mTime',
    width: 180,
    align: 'center',
  },
  {
    title: '累计排放量',
    key: 'cumulativeDischarge',
    width: 120,
    align: 'center',
  },
  {
    title: 'COD',
    dataIndex: 'cwater011',
    key: 'cwater011',
    align: 'center',
    width: 120,
  },
  {
    title: 'COD排放量',
    dataIndex: 'cwater011All',
    key: 'cwater011All',
    width: 120,
    align: 'center',
  },
  {
    title: 'COD报警阈值',
    dataIndex: 'cwater011Upper',
    key: 'cwater011Upper',
    width: 120,
    align: 'center',
  },
  {
    title: '氨氮',
    dataIndex: 'cwater060',
    key: 'cwater060',
    align: 'center',
    width: 120,
  },
  {
    title: '氨氮排放量',
    dataIndex: 'cwater060All',
    key: 'cwater060All',
    width: 120,
    align: 'center',
  },
  {
    title: '氨氮报警阈值',
    dataIndex: 'cwater060Upper',
    key: 'cwater060Upper',
    width: 120,
    align: 'center',
  },
  {
    title: 'PH',
    dataIndex: 'cwater001',
    key: 'cwater001',
    align: 'center',
    width: 120,
  },
  {
    title: '数据状态',
    dataIndex: 'flag',
    key: 'flag',
    align: 'center',
    width: 120,
  },
  { title: '处置情况', key: 'handlingFlag', align: 'center', width: 120 },
  {
    title: '初步原因判定',
    dataIndex: 'content',
    key: 'content',
    width: 120,
    align: 'center',
  },
  {
    title: '超标原因',
    dataIndex: 'reason',
    key: 'reason',
    align: 'center',
    width: 120,
  },
  { title: '操作', key: 'action', width: 100, align: 'center', fixed: 'right' },
];

const wasteGasColumns: TableColumnProps[] = [
  { title: '序号', key: 'index', width: 120, align: 'center', fixed: 'left' },
  {
    title: '单位',
    dataIndex: 'site',
    key: 'site',
    width: 180,
    align: 'center',
  },
  {
    title: '监测点',
    dataIndex: 'monitorName',
    key: 'monitorName',
    width: 180,
    align: 'center',
  },
  {
    title: '监测时间',
    dataIndex: 'mTime',
    key: 'mTime',
    width: 180,
    align: 'center',
  },
  {
    title: 'SO₂实测',
    dataIndex: 'cair02R',
    key: 'cair02R',
    align: 'center',
    width: 120,
  },
  {
    title: 'SO₂折算',
    dataIndex: 'cair02',
    key: 'cair02',
    align: 'center',
    width: 120,
  },
  { title: 'SO₂报警阈值', key: 'so2Range', width: 120, align: 'center' },
  {
    title: 'NOX实测',
    dataIndex: 'cair03R',
    key: 'cair03R',
    align: 'center',
    width: 120,
  },
  {
    title: 'NOX折算',
    dataIndex: 'cair03',
    key: 'cair03',
    align: 'center',
    width: 120,
  },
  {
    title: 'NOX排放量',
    dataIndex: 'cair03All',
    key: 'cair03All',
    width: 120,
    align: 'center',
  },
  { title: 'Nox报警阈值', key: 'noxRange', width: 120, align: 'center' },
  {
    title: '温度',
    dataIndex: 'cairS03',
    key: 'cairS03',
    align: 'center',
    width: 120,
  },
  {
    title: '压力',
    dataIndex: 'cairS08',
    key: 'cairS08',
    align: 'center',
    width: 120,
  },
  {
    title: '湿度',
    dataIndex: 'cairS05',
    key: 'cairS05',
    align: 'center',
    width: 120,
  },
  {
    title: '氧含量',
    dataIndex: 'cairS01',
    key: 'cairS01',
    align: 'center',
    width: 120,
  },
  { title: '处置情况', key: 'handlingFlag', align: 'center', width: 120 },
  {
    title: '初步原因判定',
    dataIndex: 'content',
    key: 'content',
    width: 120,
    align: 'center',
  },
  {
    title: '超标原因',
    dataIndex: 'reason',
    key: 'reason',
    align: 'center',
    width: 120,
  },
  { title: '操作', key: 'action', width: 100, align: 'center', fixed: 'right' },
];

// 方法
const iconHandle = (): void => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent): void => {
  isDragging.value = true;
  const menu = document.querySelector('.draggable-menu') as HTMLElement;
  const menuRect = menu.getBoundingClientRect();

  const onMouseMove = (e: MouseEvent): void => {
    const newX = e.clientX - menuRect.width / 2;
    const newY = e.clientY - menuRect.height / 2;

    x.value = Math.max(0, Math.min(newX, window.innerWidth - menuRect.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menuRect.height));
  };

  const onMouseUp = (): void => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const sumWithTwoDecimals = (value1: number, value2: number): string => {
  const num1 = parseFloat(String(value1)) || 0;
  const num2 = parseFloat(String(value2)) || 0;
  const sum = num1 + num2;
  return sum.toFixed(2);
};

const handleNodeClick: TreeProps['onSelect'] = (selectedKeys, info): void => {
  if (selectedKeys.length > 0) {
    formData.value.vldSiteId = String(selectedKeys[0]);
    selectedTreeKeys.value = selectedKeys; // 更新选中的key
    search();
  } else {
    formData.value.vldSiteId = '';
    selectedTreeKeys.value = []; // 清除选中的key
    search();
  }
};

const search = (): void => {
  const form = {
    startTime: '',
    endTime: '',
    vldSiteId: formData.value.vldSiteId,
    handlingFlag: formData.value.handlingFlag,
    pageNum: formData.value.pageNum,
    pageSize: formData.value.pageSize,
    monitorType: name.value === '0' ? '10' : '20',
  };

  if (formData.value.timeArr) {
    const startTime = (formData.value.timeArr[0] as Dayjs).format(
      'YYYY-MM-DD HH:mm:00'
    );
    const endTime = (formData.value.timeArr[1] as Dayjs).format(
      'YYYY-MM-DD 23:59:59'
    );
    form.startTime = startTime;
    form.endTime = endTime;
  }

  if (name.value === '0') {
    getTableData1(form);
  } else if (name.value === '1') {
    getTableData2(form);
  } else if (name.value === '2') {
    loadStatisticsData();
  }
};

const handleClick = (key: Key): void => {
  formData.value = {
    timeArr: undefined,
    vldSiteId: '',
    monitorType: '',
    handlingFlag: '',
    pageNum: 1,
    pageSize: 20,
  };
  name.value = String(key);
  selectedTreeKeys.value = []; // 切换tab时清空树形选择
  if (name.value === '2') {
    loadStatisticsData();
  } else {
    search();
  }
};

const loadStatisticsData = (): void => {
  loadOverMonitorStatistics(10);
  loadOverMonitorStatistics(20);
  loadOverMonitorColumnStatistics(10);
  loadOverMonitorColumnStatistics(20);
};

const loadOverMonitorStatistics = (monitorType: number): void => {
  const formParam = {
    startTime: '',
    endTime: '',
    vldSiteId: formData.value.vldSiteId,
    monitorType,
  };
  if (formData.value.timeArr) {
    const startTime = (formData.value.timeArr[0] as Dayjs).format(
      'YYYY-MM-DD HH:mm:00'
    );
    const endTime = (formData.value.timeArr[1] as Dayjs).format(
      'YYYY-MM-DD 23:59:59'
    );
    formParam.startTime = startTime;
    formParam.endTime = endTime;
  }
  overMonitorStatistics(formParam).then((response: any) => {
    if (monitorType === 10) {
      pieWDate.value = response.data;
    }
    if (monitorType === 20) {
      pieADate.value = response.data;
    }
  });
};

const loadOverMonitorColumnStatistics = (monitorType: number): void => {
  const formParam = {
    startTime: '',
    endTime: '',
    vldSiteId: formData.value.vldSiteId,
    monitorType,
  };
  if (formData.value.timeArr) {
    const startTime = (formData.value.timeArr[0] as Dayjs).format(
      'YYYY-MM-DD HH:mm:00'
    );
    const endTime = (formData.value.timeArr[1] as Dayjs).format(
      'YYYY-MM-DD 23:59:59'
    );
    formParam.startTime = startTime;
    formParam.endTime = endTime;
  }
  overMonitorColumnStatistics(formParam).then((response: any) => {
    if (monitorType === 10) {
      columnWDate.value = response.data;
    }
    if (monitorType === 20) {
      columnADate.value = response.data;
    }
  });
};

const getTableData1 = (formParam: any): void => {
  overMonitorPage(formParam).then((res: any) => {
    tableData1.value = res.data.records;
    total.value = res.data.total;
  });
};

const getTableData2 = (formParam: any): void => {
  overMonitorPage(formParam).then((res: any) => {
    tableData2.value = res.data.records;
    total.value = res.data.total;
  });
};

const resetForm = (): void => {
  formData.value = {
    timeArr: undefined,
    vldSiteId: '',
    monitorType: '',
    handlingFlag: '',
    pageNum: 1,
    pageSize: 20,
  };
  search();
};

const dialogHandle = (row: TableRecord): void => {
  console.log('点击处置按钮，准备打开模态框', row);
  form.value = {
    id: row.id,
    monitorType: name.value === '0' ? '10' : '20',
    reason: '',
  };

  // 添加延迟以确保DOM更新
  setTimeout(() => {
    dialogVisible.value = true;
    console.log('模态框状态设置为:', dialogVisible.value);
  }, 50);
};

const handleCancel = (): void => {
  dialogVisible.value = false;
  form.value = {
    id: '',
    monitorType: '',
    reason: '',
  };
};

const okHandle = (): void => {
  if (!form.value.reason.trim()) {
    message.warning('请填写处理结果');
    return;
  }

  handleNew(form.value)
    .then((res: any) => {
      if (res.code === 200) {
        message.success(res.msg);
        handleCancel();
        search();
      } else {
        message.error(res.msg);
      }
    })
    .catch(() => {
      message.error('操作失败，请重试');
    });
};

const exportXlsx = (): void => {
  let fieldName: string[];
  let filterVal: string[];
  let dataList: any[];
  let fileTilte: string;

  if (name.value === '0') {
    dataList = [...tableData1.value];
    for (let i = 0; i < dataList.length; i++) {
      dataList[i].sumCount = sumWithTwoDecimals(
        dataList[i].cwater011All,
        dataList[i].cwater060All
      );
      dataList[i].handlingFlag =
        dataList[i].handlingFlag === '1' ? '已完成' : '未完成';
    }
    fileTilte = '废水列表';
    fieldName = [
      '单位',
      '监测点',
      '监测时间',
      '累计排放量',
      'COD',
      'COD排放量',
      'COD报警阈值',
      '氨氮',
      '氨氮排放量',
      '氨氮报警阈值',
      'PH',
      '数据状态',
      '处置情况',
      '超标原因',
    ];
    filterVal = [
      'site',
      'monitorName',
      'mTime',
      'sumCount',
      'cwater011',
      'cwater011All',
      'cwater011Upper',
      'cwater060',
      'cwater060All',
      'cwater060Upper',
      'cwater001',
      'flag',
      'handlingFlag',
      'reason',
    ];
  } else if (name.value === '1') {
    dataList = [...tableData2.value];
    for (let i = 0; i < dataList.length; i++) {
      dataList[i].handlingFlag =
        dataList[i].handlingFlag === '1' ? '已完成' : '未完成';
      dataList[i].so2Range =
        dataList[i].so2Range.split('/')[0] === undefined
          ? 0
          : dataList[i].so2Range.split('/')[0];
      dataList[i].noxRange =
        dataList[i].noxRange.split('/')[0] === undefined
          ? 0
          : dataList[i].noxRange.split('/')[0];
    }
    fileTilte = '废气列表';
    fieldName = [
      '单位',
      '监测点',
      '监测时间',
      'SO₂实测',
      'SO₂折算',
      'SO₂报警阈值',
      'NOX实测',
      'NOX折算',
      'NOX报警阈值',
      'NOX排放量',
      '温度',
      '压力',
      '湿度',
      '氧含量',
      '处置情况',
      '超标原因',
    ];
    filterVal = [
      'site',
      'monitorName',
      'mTime',
      'cair02R',
      'cair02',
      'so2Range',
      'cair03R',
      'cair03',
      'noxRange',
      'cair03All',
      'cairS03',
      'cairS08',
      'cairS05',
      'cairS01',
      'handlingFlag',
      'reason',
    ];
  } else {
    return;
  }

  const data = dataList.map(v => filterVal.map(j => v[j]));
  exportJsonToExcel(fieldName!, data, fileTilte!);
  console.log('导出数据:', { fieldName, data, fileTilte });
};

const getTreeNanfang = async (): Promise<void> => {
  try {
    const res = await getTreeNode();
    // 转换数据格式以匹配Ant Design Tree组件要求
    const transformTreeData = (nodes: any[]): TreeNode[] => {
      return nodes.map(node => ({
        key: node.vldSiteId || node.id,
        title: node.siteShortName,
        siteShortName: node.siteShortName,
        vldSiteId: node.vldSiteId || node.id,
        children: node.children ? transformTreeData(node.children) : undefined,
      }));
    };
    treeData.value = transformTreeData(res.data);
  } catch (error) {
    console.error('获取树形数据失败:', error);
  }
};

// 生命周期
onMounted(() => {
  search();
  getTreeNanfang();
});
</script>
<style lang="less" scoped>
//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;
}

.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
}

.main-body-s {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;
}

div::-webkit-scrollbar {
  display: none;
}

.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  transition: left 0.5s;
  top: calc((100% - 89px) / 2);
}

.left-img-s {
  left: 0px;
  transition: left 0.5s;
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  transform: rotate(180deg);
  top: calc((100% - 50px) / 2);
  transition: transform 0.5s, left 0.5s;
  cursor: pointer;
}

.left-img-icon-s {
  left: -8px;
  transform: rotate(0deg);
  transition: transform 0.5s, left 0.5s;
}

.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  //background: rgba(0, 0, 0, 0.3);
  color: #fff;

  .main {
    overflow: hidden;
    height: 100%;
  }

  .main:nth-child(1) {
    width: 350px;
  }

  .main:nth-child(2) {
    width: calc(100% - 366px);
  }
}

.demo-form-inline {
  margin-top: 16px;
}

.table {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}

::v-deep .number {
  letter-spacing: 0px;
}

// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
