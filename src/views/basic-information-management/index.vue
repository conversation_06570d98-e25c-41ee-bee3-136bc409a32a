<template>
  <div class="environmental-alarm-page">
    <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
      <a-input
        v-model:value="filterText"
        class="search"
        placeholder="输入关键词"
        allow-clear
        size="small"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="tree-wrapper">
        <a-tree
          ref="monitorList"
          :tree-data="treeData"
          :field-names="{
            children: 'children',
            title: 'siteShortName',
            key: 'id',
          }"
          :show-line="false"
          :show-icon="false"
          @select="handleNodeClick"
        />
      </div>
    </div>
    <img
      src="@/assets/images/shouqi.png"
      alt=""
      :class="['left-img', isCollapsed ? 'left-img-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <img
      src="@/assets/images/shouqiicon.png"
      alt=""
      :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
      @click="isCollapsed = !isCollapsed"
    />
    <!-- 菜单图标 -->
    <div
      ref="menu"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>
    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog />
    </div>

    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <a-tabs v-model:active-key="activeName" @change="handleClick as any">
        <a-tab-pane key="0" tab="监测点信息" />
        <a-tab-pane key="1" tab="监测项目信息" />
        <a-tab-pane key="2" tab="监测设备信息" />
        <a-tab-pane key="3" tab="数采仪信息" />
      </a-tabs>
      <a-form
        ref="formRef"
        layout="inline"
        :model="formData"
        class="demo-form-inline"
      >
        <a-form-item v-if="name === '0' || name === '1'" label="监测点：">
          <a-input
            v-model:value="formData.monitorName"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>
        <a-form-item v-if="name === '1'" label="监测项目：">
          <a-input
            v-model:value="formData.paramName"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>
        <a-form-item v-if="name === '0'" label="监测点类型:">
          <a-select
            v-model:value="formData.monitorType"
            placeholder="请选择"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="10">废水</a-select-option>
            <a-select-option value="20">废气</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="name === '2'" label="设备名称：">
          <a-input
            v-model:value="formData.equipmentName"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>
        <a-form-item v-if="name === '2'" label="监测类型:">
          <a-select
            v-model:value="formData.equipmentType"
            placeholder="请选择"
            allow-clear
            style="width: 120px"
          >
            <a-select-option :value="10">废水</a-select-option>
            <a-select-option :value="20">废气</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="name === '3'" label="数采仪名称：">
          <a-input
            v-model:value="formData.collectName"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>
        <a-form-item v-if="name === '3'" label="安装日期:">
          <a-range-picker
            v-model:value="formData.timeArr as any"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
          />
        </a-form-item>
        <a-form-item v-if="name === '3'" label="运维单位:">
          <a-select
            v-model:value="formData.amdId"
            placeholder="请选择"
            allow-clear
            style="width: 150px"
          >
            <a-select-option
              v-for="item in amdDate"
              :key="item.id"
              :value="item.id"
            >
              {{ item.amdName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item class="ant-form-item-bottom">
          <a-space>
            <a-button type="primary" @click="search">查询</a-button>
            <a-button @click="resetForm">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
      <div class="table">
        <div class="table-wrapper">
          <a-table
            v-if="name == '0'"
            :data-source="tableData1"
            :columns="monitorColumns as any"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'monitorType'">
                <span v-if="record.monitorType == '10'">废水</span>
                <span v-if="record.monitorType == '20'">废气</span>
              </template>
              <template v-else-if="column.key === 'monitorProperty'">
                <span v-if="record.monitorProperty == '10'">外排</span>
                <span v-if="record.monitorProperty == '20'">过程</span>
              </template>
            </template>
          </a-table>
          <a-table
            v-if="name == '1'"
            :data-source="tableData2"
            :columns="paramColumns"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'paramBaseUnit'">
                <span v-if="record.paramBaseUnit == 0">l/s</span>
                <span v-if="record.paramBaseUnit == 1">t</span>
                <span v-if="record.paramBaseUnit == 2">mg/l</span>
                <span v-if="record.paramBaseUnit == 3">m3/s</span>
                <span v-if="record.paramBaseUnit == 4">m3</span>
                <span v-if="record.paramBaseUnit == 5">mg/m3</span>
              </template>
            </template>
          </a-table>
          <a-table
            v-if="name == '2'"
            :data-source="tableData3"
            :columns="equipmentColumns"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'equipmentType'">
                <span v-if="record.equipmentType == '10'">废水</span>
                <span v-if="record.equipmentType == '20'">废气</span>
              </template>
              <template v-else-if="column.key === 'equipmentStatus'">
                <span v-if="record.equipmentStatus == '0'">在用</span>
                <span v-if="record.equipmentStatus == '1'">停用</span>
                <span v-if="record.equipmentStatus == '2'">报废</span>
              </template>
            </template>
          </a-table>
          <a-table
            v-if="name == '3'"
            :data-source="tableData4"
            :columns="collectColumns"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'collectStatus'">
                <span v-if="record.collectStatus == 0">在用</span>
                <span v-if="record.collectStatus == 1">停用</span>
                <span v-if="record.collectStatus == 2">报废</span>
              </template>
            </template>
          </a-table>
        </div>
        <div class="pagination-wrapper">
          <a-pagination
            v-model:current="formData.pageNum"
            v-model:page-size="formData.pageSize"
            :total="total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :page-size-options="['20', '40', '60', '80', '100']"
            :show-total="total => `共 ${total} 条`"
            style="margin-top: 16px; text-align: right"
            @change="onPageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed } from 'vue';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';
import {
  monitorPage,
  paramPage,
  equipmentPage,
  collectPage,
  getAmdAllList,
} from '@/http/basic-monitor';
import { getTreeNode } from '@/http/environmental-overview';
import menuDialog from '@/components/menu.vue';

// 接口定义
interface FormData {
  monitorName: string;
  monitorType: string;
  paramName: string;
  equipmentName: string;
  equipmentType: string;
  collectName: string;
  timeArr: [Dayjs, Dayjs] | null;
  amdId: string;
  siteId: string;
  pageNum: number;
  pageSize: number;
}

interface TreeNode {
  id: string;
  key: string;
  siteShortName: string;
  children?: TreeNode[];
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const filterText = ref('');
const isCollapsed = ref(false);
const name = ref('0');
const activeName = ref('0');
const formData = ref<FormData>({
  monitorName: '',
  monitorType: '',
  paramName: '',
  equipmentName: '',
  equipmentType: '',
  collectName: '',
  timeArr: null,
  amdId: '',
  siteId: '',
  pageNum: 1,
  pageSize: 20,
});

const total = ref(0);
const treeData = ref<TreeNode[]>([]);
const tableData1 = ref([]);
const tableData2 = ref([]);
const tableData3 = ref([]);
const tableData4 = ref([]);
const amdDate = ref<any[]>([]);

const onPageChange = (page: number, pageSize: number) => {
  formData.value.pageNum = page || 1;
  formData.value.pageSize = pageSize;
  search();
};
// 表格列定义
const monitorColumns = [
  { title: '序号', key: 'index', width: 80, align: 'center' as const },
  {
    title: '单位',
    dataIndex: 'siteName',
    key: 'siteName',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '监测点',
    dataIndex: 'monitorName',
    key: 'monitorName',
    width: 180,
    align: 'center' as const,
  },
  { title: '监测点类型', key: 'monitorType', align: 'center' as const },
  { title: '监测点性质', key: 'monitorProperty', align: 'center' as const },
  {
    title: '经度',
    dataIndex: 'latitude',
    key: 'latitude',
    align: 'center' as const,
  },
  {
    title: '纬度',
    dataIndex: 'longitude',
    key: 'longitude',
    align: 'center' as const,
  },
];

const paramColumns = [
  { title: '序号', key: 'index', width: 80, align: 'center' as const },
  {
    title: '单位',
    dataIndex: 'siteName',
    key: 'siteName',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '监测点',
    dataIndex: 'monitorName',
    key: 'monitorName',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '监测项目',
    dataIndex: 'paramName',
    key: 'paramName',
    align: 'center' as const,
  },
  {
    title: '标准值',
    dataIndex: 'stdValue',
    key: 'stdValue',
    align: 'center' as const,
  },
  { title: '计量单位', key: 'paramBaseUnit', align: 'center' as const },
  {
    title: '项目标识',
    dataIndex: 'paramMark',
    key: 'paramMark',
    align: 'center' as const,
  },
  {
    title: '标准号',
    dataIndex: 'paramCode',
    key: 'paramCode',
    align: 'center' as const,
  },
];

const equipmentColumns = [
  { title: '序号', key: 'index', width: 80, align: 'center' as const },
  {
    title: '单位',
    dataIndex: 'site',
    key: 'site',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '设备名称',
    dataIndex: 'equipmentName',
    key: 'equipmentName',
    align: 'center' as const,
  },
  { title: '监测类型', key: 'equipmentType', align: 'center' as const },
  {
    title: '设备型号',
    dataIndex: 'equipmentModel',
    key: 'equipmentModel',
    align: 'center' as const,
  },
  { title: '设备状态', key: 'equipmentStatus', align: 'center' as const },
  {
    title: '安装日期',
    dataIndex: 'setDate',
    key: 'setDate',
    align: 'center' as const,
  },
  {
    title: '运维单位',
    dataIndex: 'amdName',
    key: 'amdName',
    align: 'center' as const,
  },
];

const collectColumns = [
  { title: '序号', key: 'index', width: 80, align: 'center' as const },
  {
    title: '单位',
    dataIndex: 'vldSiteId',
    key: 'vldSiteId',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '数采仪名称',
    dataIndex: 'collectName',
    key: 'collectName',
    align: 'center' as const,
  },
  {
    title: '设备型号',
    dataIndex: 'collectMode',
    key: 'collectMode',
    align: 'center' as const,
  },
  { title: '设备状态', key: 'collectStatus', align: 'center' as const },
  {
    title: '安装日期',
    dataIndex: 'setDate',
    key: 'setDate',
    align: 'center' as const,
  },
  {
    title: '运维单位',
    dataIndex: 'mMonitorAmdTbId',
    key: 'mMonitorAmdTbId',
    align: 'center' as const,
  },
];

// 方法
const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const menu = document.querySelector('.draggable-menu') as HTMLElement;
  const menuRect = menu.getBoundingClientRect();

  const onMouseMove = (e: MouseEvent) => {
    const newX = e.clientX - menuRect.width / 2;
    const newY = e.clientY - menuRect.height / 2;

    x.value = Math.max(0, Math.min(newX, window.innerWidth - menuRect.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menuRect.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const handleNodeClick = (selectedKeys: any[]) => {
  if (selectedKeys.length > 0) {
    formData.value.siteId = selectedKeys[0].toString();
    search();
  }
};

const search = () => {
  const form: any = {
    monitorName: formData.value.monitorName,
    monitorType: formData.value.monitorType,
    siteId: formData.value.siteId,
    paramName: formData.value.paramName,
    equipmentName: formData.value.equipmentName,
    equipmentType: formData.value.equipmentType,
    collectName: formData.value.collectName,
    amdId: formData.value.amdId,
    pageNum: formData.value.pageNum,
    pageSize: formData.value.pageSize,
  };

  if (formData.value.timeArr) {
    form.startDate = dayjs(formData.value.timeArr[0]).format(
      'YYYY-MM-DD HH:mm:00'
    );
    form.endDate = dayjs(formData.value.timeArr[1]).format(
      'YYYY-MM-DD 23:59:59'
    );
  }

  if (name.value === '0') {
    getTableData1(form);
  } else if (name.value === '1') {
    getTableData2(form);
  } else if (name.value === '2') {
    getTableData3(form);
  } else if (name.value === '3') {
    getTableData4(form);
  }
};

const handleClick = (key: string) => {
  formData.value = {
    monitorName: '',
    monitorType: '',
    equipmentName: '',
    equipmentType: '',
    collectName: '',
    timeArr: null,
    amdId: '',
    siteId: '',
    paramName: '',
    pageNum: 1,
    pageSize: 20,
  } as any;
  name.value = key;
  search();
};

const getTableData1 = (form: any) => {
  monitorPage(form).then((res: any) => {
    tableData1.value = res.data.records;
    total.value = res.data.total;
  });
};

const getTableData2 = (form: any) => {
  paramPage(form).then((res: any) => {
    tableData2.value = res.data.records;
    total.value = res.data.total;
  });
};

const getTableData3 = (form: any) => {
  equipmentPage(form).then((res: any) => {
    tableData3.value = res.data.records;
    total.value = res.data.total;
  });
};

const getTableData4 = (form: any) => {
  collectPage(form).then((res: any) => {
    tableData4.value = res.data.records;
    total.value = res.data.total;
  });
};

const resetForm = () => {
  formData.value = {
    timeArr: null,
    unit: '',
    mBasicMonitorTbId: '',
    pageNum: 1,
    pageSize: 20,
  } as any;
  search();
};

const getAmdDate = () => {
  getAmdAllList().then((res: any) => {
    amdDate.value = res.data;
  });
};

const getTreeNanfang = async () => {
  const res = await getTreeNode();
  // 转换数据格式以添加 key 属性
  const convertTreeData = (nodes: any[]): TreeNode[] => {
    return nodes.map(node => ({
      ...node,
      key: node.id,
      children: node.children ? convertTreeData(node.children) : undefined,
    }));
  };
  treeData.value = convertTreeData(res.data);
};

// 生命周期
onMounted(() => {
  search();
  getAmdDate();
  getTreeNanfang();
});

// 监听器
watch(filterText, val => {
  // 树过滤逻辑
});
</script>

<style lang="less" scoped>
div::-webkit-scrollbar {
  display: none;
}

//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

//---
.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  padding: 24px;
  transition: left 0.5s;
}

.main-tree-s {
  left: -390px;
  transition: left 0.5s;
}

.main-body {
  height: 100%;
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-body-s {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 25px;
  padding: 24px;
  transition: left 0.5s;
}

.left-img {
  position: absolute;
  width: 25px;
  height: 89px;
  left: 390px;
  transition: left 0.5s;
  top: calc((100% - 89px) / 2);
}

.left-img-s {
  left: 0px;
  transition: left 0.5s;
}

.left-img-icon {
  position: absolute;
  width: 40px;
  height: 50px;
  left: 382px;
  transform: rotate(180deg);
  top: calc((100% - 50px) / 2);
  transition: transform 0.5s, left 0.5s;
  cursor: pointer;
}

.left-img-icon-s {
  left: -8px;
  transform: rotate(0deg);
  transition: transform 0.5s, left 0.5s;
}

.tree-wrapper {
  height: calc(100% - 52px);
  overflow: auto;
  margin-top: 20px;
}

.environmental-alarm-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  color: #fff;

  .main {
    overflow: hidden;
    height: 100%;
  }

  .main:nth-child(1) {
    width: 350px;
  }

  .main:nth-child(2) {
    width: calc(100% - 366px);
  }
}

.demo-form-inline {
  margin-top: 16px;
}

.table {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;

  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}

// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
