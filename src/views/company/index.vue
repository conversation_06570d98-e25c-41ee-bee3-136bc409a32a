<template>
  <div class="environmental-alarm-page">
    <!-- 菜单图标 -->
    <div
      ref="menu"
      class="draggable-menu"
      :style="{ left: `${x}px`, top: `${y}px` }"
      :class="{ active: isDragging }"
      @mousedown="startDrag"
      @click="iconHandle"
    >
      <div class="menu-icon">
        <MenuOutlined />
      </div>
    </div>
    <div
      v-if="isMenu"
      :class="['main-tree']"
      style="background-color: rgba(11, 32, 87, 0.6)"
    >
      <menuDialog />
    </div>
    <div :class="['main-body', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <div class="search">
        <div class="content1">
          <a-form
            ref="formRef"
            layout="inline"
            :model="formData"
            class="demo-form-inline"
          >
            <a-form-item label="单位名称：">
              <a-select
                v-model:value="formData.vldSiteId"
                style="width: 200px"
                placeholder="请选择单位"
                allow-clear
                show-search
                :filter-option="filterOption"
              >
                <a-select-option
                  v-for="(item, index) in towDeptList"
                  :key="index"
                  :value="item.id"
                >
                  {{ item.deptName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="厂（站）名称：">
              <a-input
                v-model:value="formData.stationName"
                placeholder="请输入厂（站）名称"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="是否通过无异味企业验收：">
              <a-select
                v-model:value="formData.isPeculiarSmell"
                placeholder="请选择是否通过无异味企业验收"
                allow-clear
                style="width: 200px"
              >
                <a-select-option value="0">否</a-select-option>
                <a-select-option value="1">是</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ant-form-item-bottom">
              <a-space>
                <a-button
                  style="margin-left: 100px"
                  type="primary"
                  @click="search"
                >
                  查询
                </a-button>
                <a-button @click="resetForm">重置</a-button>
                <a-button type="primary" @click="addUserHandle"
                  >添加企业</a-button
                >
                <a-button type="primary" @click="batchDeleteHandle"
                  >批量删除</a-button
                >
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </div>
      <div class="table-scrollbar">
        <div class="table-wrapper">
          <a-table
            :data-source="tableData1"
            :columns="columns"
            :row-selection="rowSelection"
            :pagination="false"
            :scroll="{
              x: 'max-content',
              y: 'max-content',
            }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (formData.pageNum - 1) * formData.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'isPeculiarSmell'">
                <a-switch
                  :checked="(record as TableRecord).isPeculiarSmell === 1"
                  checked-children="是"
                  un-checked-children="否"
                  @change="switchHandle(index, record as TableRecord)"
                />
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button type="link" @click="deleteHandle(record as any)">
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
        <a-pagination
          v-model:current="formData.pageNum"
          v-model:page-size="formData.pageSize"
          :total="total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :page-size-options="['20', '40', '60', '80', '100']"
          :show-total="total => `共 ${total} 条`"
          style="margin-top: 16px; text-align: right"
          @change="onPageChange"
        />
      </div>
    </div>
    <!--新增-->
    <a-modal
      v-model:open="addDialog"
      title="添加企业"
      :footer="null"
      width="50%"
    >
      <a-form
        ref="userForm"
        :model="userForm"
        :rules="userFormRules"
        :label-col="{ style: { width: '175px' } }"
      >
        <a-form-item name="vldSiteId" label="单位名称:">
          <a-select
            v-model:value="userForm.vldSiteId"
            style="width: 100%"
            placeholder="请选择单位"
            allow-clear
            show-search
            :filter-option="filterOption"
          >
            <a-select-option
              v-for="(item, index) in towDeptList"
              :key="index"
              :value="item.id"
            >
              {{ item.deptName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="stationName" label="厂（站）名称:">
          <a-input
            v-model:value="userForm.stationName"
            placeholder="请输入厂（站）名称"
            style="width: 100%"
            allow-clear
          />
        </a-form-item>
        <a-form-item name="isPeculiarSmell" label="是否通过无异味企业验收:">
          <a-select
            v-model:value="userForm.isPeculiarSmell"
            style="width: 100%"
            placeholder="请选择是否通过无异味企业验收"
            allow-clear
          >
            <a-select-option value="0">否</a-select-option>
            <a-select-option value="1">是</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
      <div style="text-align: right; margin-top: 16px; margin-right: 16px">
        <a-space>
          <a-button @click="addDialog = false">取 消</a-button>
          <a-button type="primary" @click="addOkHandle">确 定</a-button>
        </a-space>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { MenuOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import {
  companyPage,
  addOrUpdatePeculiarSmellCompany,
  deleteCompany,
  updateStartType,
} from '@/http/company.js';
import { getTreeNode } from '@/http/environmental-overview';
import { getTowLevelDept } from '@/http/monitor';
import menuDialog from '@/components/menu.vue';

// 接口定义
interface FormData {
  vldSiteId: string;
  stationName: string;
  isPeculiarSmell: string;
  pageNum: number;
  pageSize: number;
}

interface UserForm {
  vldSiteId: string;
  vldSiteName: string;
  stationName: string;
  isPeculiarSmell: string;
}

interface TableRecord {
  id: string;
  vldSiteName: string;
  stationName: string;
  isPeculiarSmell: number;
  [key: string]: any;
}

interface DeptItem {
  id: string;
  deptName: string;
}

interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 响应式数据
const x = ref(20);
const y = ref(500);
const isDragging = ref(false);
const isMenu = ref(false);
const isCollapsed = ref(true);
const ids = ref<string[]>([]);
const addDialog = ref(false);

const formData = ref<FormData>({
  vldSiteId: '',
  stationName: '',
  isPeculiarSmell: '',
  pageNum: 1,
  pageSize: 20,
});

const total = ref(0);
const userForm = ref<UserForm>({
  vldSiteId: '',
  vldSiteName: '',
  stationName: '',
  isPeculiarSmell: '',
});

const userFormRules = {
  vldSiteId: [
    { required: true, message: '请选择单位', trigger: 'change' as const },
  ],
  stationName: [
    { required: true, message: '请输入厂（站）名称', trigger: 'blur' as const },
  ],
  isPeculiarSmell: [
    {
      required: true,
      message: '请选择是否通过无异味企业验收',
      trigger: 'change' as const,
    },
  ],
};

const tableData1 = ref<TableRecord[]>([]);
const towDeptList = ref<DeptItem[]>([]);

const onPageChange = (page: number, pageSize: number) => {
  formData.value.pageNum = page || 1;
  formData.value.pageSize = pageSize;
  search();
};

const rowSelection = computed(() => ({
  selectedRowKeys: ids.value,
  onChange: (selectedRowKeys: any[]) => {
    ids.value = selectedRowKeys.map((key: any) => key.toString());
  },
}));

// 表格列定义
const columns = [
  { title: '序号', key: 'index', width: 80, align: 'center' as const },
  {
    title: '单位名称',
    dataIndex: 'vldSiteName',
    key: 'vldSiteName',
    align: 'center' as const,
  },
  {
    title: '厂（站）名称',
    dataIndex: 'stationName',
    key: 'stationName',
    align: 'center' as const,
  },
  {
    title: '是否通过无异味企业验收',
    key: 'isPeculiarSmell',
    align: 'center' as const,
    width: 180,
  },
  { title: '操作', key: 'action', align: 'center' as const, width: 80 },
];

// 方法
const iconHandle = () => {
  isMenu.value = !isMenu.value;
  isCollapsed.value = true;
};

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const menu = document.querySelector('.draggable-menu') as HTMLElement;
  const menuRect = menu.getBoundingClientRect();

  const onMouseMove = (e: MouseEvent) => {
    const newX = e.clientX - menuRect.width / 2;
    const newY = e.clientY - menuRect.height / 2;

    x.value = Math.max(0, Math.min(newX, window.innerWidth - menuRect.width));
    y.value = Math.max(0, Math.min(newY, window.innerHeight - menuRect.height));
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const addUserHandle = () => {
  addDialog.value = true;
  userForm.value = {
    vldSiteId: '',
    vldSiteName: '',
    stationName: '',
    isPeculiarSmell: '',
  };
};

const addOkHandle = () => {
  (userForm.value as any).validate().then(() => {
    addOrUpdatePeculiarSmellCompany(userForm.value).then((res: any) => {
      if (res.code === 200) {
        message.success('新增成功!');
        search();
        addDialog.value = false;
      } else {
        message.error(res.msg);
      }
    });
  });
};

const search = () => {
  const form = {
    vldSiteId: formData.value.vldSiteId,
    stationName: formData.value.stationName,
    isPeculiarSmell: formData.value.isPeculiarSmell,
    pageNum: formData.value.pageNum,
    pageSize: formData.value.pageSize,
  };
  getTableData1(form);
};

const getTableData1 = (form: any) => {
  companyPage(form).then((res: any) => {
    tableData1.value = res.data.records;
    total.value = res.data.total;
  });
};

const switchHandle = (editIndex: number, row: TableRecord) => {
  const newPushType = row.isPeculiarSmell === 1 ? 0 : 1;
  updateStartType({ id: row.id, isPeculiarSmell: newPushType }).then(
    (res: any) => {
      if (res.code === 200) {
        row.isPeculiarSmell = newPushType;
        tableData1.value.splice(editIndex, 1, row);
      }
    }
  );
};

const batchDeleteHandle = () => {
  if (ids.value.length === 0) {
    message.info('请选择删除对象');
    return;
  }
  deleteHandle({
    id: '',
    vldSiteName: '',
    stationName: '',
    isPeculiarSmell: 0,
  });
};

const deleteHandle = (row: TableRecord) => {
  const deleteIds = row.id || ids.value.join(',');
  Modal.confirm({
    title: '提示',
    content: '是否确认删除选中的数据项?',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      deleteCompany(deleteIds).then((res: any) => {
        if (res.code === 200) {
          message.success('删除成功!');
        }
        search();
      });
    },
    onCancel() {
      message.info('已取消删除');
    },
  });
};

const resetForm = () => {
  formData.value = {
    vldSiteId: '',
    stationName: '',
    isPeculiarSmell: '',
    pageNum: 1,
    pageSize: 20,
  };
  search();
};

const getTowDeptList = () => {
  getTowLevelDept().then((res: any) => {
    towDeptList.value = res.data;
  });
};

// 生命周期
onMounted(() => {
  search();
  getTowDeptList();
});
</script>

<style lang="less" scoped>
//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

//---
div::-webkit-scrollbar {
  display: none;
}

.main-tree {
  height: 100%;
  width: 390px;
  position: absolute;
  left: 0px;
  padding: 24px;
  transition: left 0.5s;
}

.main-body {
  height: 100%;
  width: calc(100% - 430px);
  position: absolute;
  left: 430px;
  padding: 24px;
  transition: left 0.5s;
  display: flex;
  flex-direction: column;
}

.main-body-s {
  height: 100%;
  width: calc(100% - 25px);
  position: absolute;
  left: 25px;
  padding: 24px;
  transition: left 0.5s;
}

.environmental-alarm-page {
  width: 100%;
  height: 100%;
  color: #ffffff;
  display: flex;
  position: relative;

  .search {
    width: 100%;

    .title {
      margin-bottom: 10px;
    }
  }
}

.table-scrollbar {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: block;
  margin-top: 16px;
  .table-wrapper {
    height: calc(100% - 48px);
    ::v-deep(.ant-table-wrapper) {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        .ant-table {
          flex: 1;
          height: 0;
        }
        .ant-pagination {
          flex-shrink: 0;
        }
      }
      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .ant-table-header {
          flex-shrink: 0;
        }
        > .ant-table-body {
          flex: 1;
          height: 0;
        }
      }
    }
  }
}
// 分页器样式
::v-deep .ant-pagination {
  .ant-pagination-total-text {
    color: #ffffff;
  }

  .ant-pagination-item {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;

    a {
      color: #ffffff;
    }

    &:hover {
      border-color: #0bc4ff;

      a {
        color: #0bc4ff;
      }
    }
  }

  .ant-pagination-item-active {
    background: #0bc4ff;
    border-color: #0bc4ff;

    a {
      color: #ffffff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    background: rgba(0, 27, 64, 0.6);
    border-color: #00409a;
    color: #ffffff;

    &:hover {
      border-color: #0bc4ff;
      color: #0bc4ff;
    }

    &.ant-pagination-disabled {
      background: rgba(0, 27, 64, 0.3);
      border-color: #00409a;
      color: rgba(255, 255, 255, 0.3);

      &:hover {
        border-color: #00409a;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #ffffff;

    &:hover {
      color: #0bc4ff;
    }
  }

  .ant-pagination-options {
    .ant-select {
      .ant-select-selector {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;
      }

      &:hover .ant-select-selector {
        border-color: #0bc4ff;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #0bc4ff;
        box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #ffffff;

      input {
        background: rgba(0, 27, 64, 0.6);
        border-color: #00409a;
        color: #ffffff;

        &:hover {
          border-color: #0bc4ff;
        }

        &:focus {
          border-color: #0bc4ff;
          box-shadow: 0 0 0 2px rgba(11, 196, 255, 0.1);
        }
      }
    }
  }
}
</style>
