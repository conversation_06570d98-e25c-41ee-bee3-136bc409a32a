<template>
  <div class="riskWrap">
    <div class="riskWrap-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';

const name = ref('riskOpera');

onMounted(() => {
  // console.log('Component mounted!');
});

onBeforeUnmount(() => {
  // console.log('Component will unmount!');
});
</script>
<style lang="less" scoped>
.riskWrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0 0 0.1rem 0;
  .riskWrap-content {
    width: 100%;
    flex: 1;
    overflow: hidden;
  }
}
</style>
