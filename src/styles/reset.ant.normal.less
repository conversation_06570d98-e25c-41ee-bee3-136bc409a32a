html[theme='normal'] {
  .ant-btn-primary:hover,
  .ant-btn-primary:focus {
    background-color: #4585ff;
    border-color: #4585ff;
  }
  .ant-btn-background-ghost.ant-btn-primary:hover,
  .ant-btn-background-ghost.ant-btn-primary:focus {
    background-color: #4585ff;
    border-color: #4585ff;
    color: #fff;
  }
  .ant-radio-inner {
    border-color: #c2c1c2;
  }
  .ant-picker {
    padding: 4px 12px 4px;
    border: 1px solid #e9edf0;
  }
  .ant-picker-input {
    color: #c1c2c1;
    font-size: 16px;
  }
  .ant-picker-suffix {
    font-size: 16px;
    color: #999;
  }
  .ant-alert-description {
    color: #666;
  }

  // .ant-message {
  //   min-height: 40px;
  //   box-shadow: 0 3px 6px -4px #000;
  //   padding: 0 16px;
  // }
  .ant-pagination-item {
    border-color: #e9edf0;
  }
  .ant-pagination-item-active a {
    color: #fff;
  }
  input::-webkit-input-placeholder {
    font-size: 16px;
  }
  .ant-checkbox-inner {
    border: 1px solid #c1c2c1;
  }
  .ant-checkbox-disabled .ant-checkbox-inner {
    border: 1px solid #c1c2c1;
    background-color: #e9edf0;
  }
  .ant-rate {
    color: #ff9900;
    font-size: 18px;
    &.large {
      font-size: 24px;
    }
    &.small {
      font-size: 16px;
    }
  }
  .ant-rate-star:not(:last-child) {
    margin-right: 4px;
  }
  .ant-rate-text {
    margin: 0 12px;
  }
  .ant-rate-star-zero {
    .anticon-star {
      color: rgba(0, 0, 0, 0.06);
    }
  }
  // 抽屉
  .ant-drawer-content {
    background-color: #fff;
    box-shadow: 0 12 48 16 #000/3%;
    .ant-drawer-header-title {
      height: 56px;
      .ant-drawer-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }
    .ant-drawer-body {
      padding-top: 30px;
      p {
        line-height: 24px;
        font-size: 14px;
        color: #666;
      }
    }
    .ant-drawer-footer {
      height: 56px;
    }
  }
  // 树选择
  .ant-select-selection-overflow {
    height: 24px;
  }
  .ant-select-selection-item {
    border: 1px solid #000/1%;
    background-color: #000/3%;
    border-radius: 2px;
    .ant-select-selection-item-content {
      color: #666;
      font-size: 12px;
    }
  }
  .ant-popover-inner-content {
    padding: 16px;
  }
  .ant-popover-message > .anticon {
    font-size: 16px;
    color: #ff9e0f;
  }
  .ant-transfer-list-content-item-text {
    font-size: 14px;
  }
  .ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {
    background-color: #2470ff21;
  }
  .ant-radio-disabled .ant-radio-inner {
    background-color: #e9edf0;
    border-color: #c1c2c1 !important;
  }

  .ant-radio-disabled .ant-radio-inner::after {
    background-color: #e9edf0;
  }

  // 下拉选择框
  .ant-select-selection-placeholder {
    color: #c1c2c1;
    font-size: 16px;
  }

  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector {
    background: #eff0f4;
  }

  // 卡片
  .ant-card-head {
    font-weight: bold;
    border-bottom: 1px solid #eff0f4;
  }

  .ant-card-body {
    padding: 30px 24px;
  }

  // Tag
  .ant-tag {
    height: 22px;
  }

  // Modal
  .ant-modal-header {
    // height: 52px;
    box-sizing: border-box;
    border-bottom: 1px solid #eff0f4;
  }

  .ant-modal-title {
    color: #333;
    font-weight: bold;
  }

  // .ant-modal-close-x {
  //   width: 52px;
  //   height: 52px;
  // }

  .ant-modal-body {
    padding: 30px 24px;
  }

  .ant-modal-footer {
    border-top: 1px solid #eff0f4;
    box-sizing: border-box;
    padding-top: 16px;
    margin: 0px;
  }

  // 进度条背景色
  .ant-progress-inner {
    background: #e9edf0;
  }

  .ant-progress-text {
    color: #999;
    font-size: 12px;
  }

  .ant-input-affix-wrapper-focused {
    .ant-input-prefix {
      color: #2470ff;
    }
  }
  .ant-input-affix-wrapper {
    height: 32px;
  }

  .ant-input {
    font-size: 16px;
    // padding: 4px 12px;
  }
  .ant-table {
    .ant-table-tbody > tr > td {
      font-size: 12px;
      border-bottom-color: #e9edf0;
    }
    .ant-table-row {
      &.table-striped {
        background-color: #fbfcfc;
      }
    }
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td,
    .ant-table tfoot > tr > th,
    .ant-table tfoot > tr > td {
      padding: 12px 16px;
    }
    .ant-table-tbody > tr.ant-table-row-selected > td {
      background: rgb(36 112 255 / 3%);
    }
  }
  .ant-collapse {
    border: 1px solid #d9d9d9;
  }
  .ant-collapse > .ant-collapse-item > .ant-collapse-header {
    font-size: 16px;
    padding: 12px 18px;
  }
  .ant-collapse
    > .ant-collapse-item
    > .ant-collapse-header
    .ant-collapse-arrow {
    margin-right: 16px;
  }
  // upload
  .ant-upload.ant-upload-select-picture-card {
    background-color: transparent;
    border-color: #d9d9d9;
  }

  .ant-upload-list-item-info .anticon-loading .anticon,
  .ant-upload-list-item-info .ant-upload-text-icon .anticon {
    font-size: 16px;
  }

  .ant-upload-list-text .ant-upload-list-item-name,
  .ant-upload-list-picture .ant-upload-list-item-name {
    font-size: 12px;
  }

  .ant-upload.ant-upload-drag {
    background: transparent;
    border: 1px dashed #d9d9d9;
  }

  .ant-upload.ant-upload-drag p.ant-upload-drag-icon {
    margin-bottom: 16px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-text {
    font-size: 14px;
    margin: 0px 0px 8px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-hint {
    color: #666;
    font-size: 12px;
  }

  // message消息提示框
  .ant-message-notice-content {
    padding: 9px 16px;
  }

  .ant-message-warning .anticon {
    color: #ff9900;
  }

  // tree树形组件
  .ant-tree-switcher .ant-tree-switcher-icon,
  .ant-tree-switcher .ant-select-tree-switcher-icon {
    font-size: 12px;
    color: #666;
  }

  .ant-tree .ant-tree-node-content-wrapper {
    color: #666;
    font-size: 14px;
  }

  .ant-tree-switcher {
    width: 28px;
  }

  .ant-tree-checkbox-inner {
    border-color: #c1c2c1;
  }

  .ant-tree .ant-tree-node-content-wrapper:hover {
    background-color: #eff0f4;
  }

  .ant-tree-treenode-checkbox-checked .ant-tree-node-content-wrapper:hover {
    background-color: rgba(36, 112, 255, 0.1);
  }

  .ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
    border-color: #c1c2c1 !important;
    background-color: #e9edf0;
  }

  .ant-tree-treenode-disabled .ant-tree-node-content-wrapper {
    color: #c1c2c1;
  }

  // tabs标签页
  .ant-tabs-top > .ant-tabs-nav::before,
  .ant-tabs-bottom > .ant-tabs-nav::before,
  .ant-tabs-top > div > .ant-tabs-nav::before,
  .ant-tabs-bottom > div > .ant-tabs-nav::before {
    border-color: #eff0f4;
  }

  .ant-tabs-tab + .ant-tabs-tab {
    margin: 0 0 0 24px;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-right > .ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }
  //timeline
  .ant-timeline-item-content {
    margin: 0px 0px 0px 16px;
  }
  //slider
  .ant-slider-handle {
    border: solid 2px #2470ff !important;
    background-color: #ffffff !important;
    z-index: 2;
    margin-top: calc(14px / 2px);
  }
  .ant-slider-track {
    background-color: #2470ff !important;
    z-index: 1;
  }

  .ant-slider-dot-active {
    border: solid 2px #2470ff !important;
    z-index: 2;
  }
  .ant-slider-step {
    background-color: #f5f5f5;
  }
  #test .ant-slider-step {
    background-color: #ffffff !important;
  }
  .ant-slider-mark-text {
    font-size: 14px !important;
  }
  #sliderinput {
    max-width: 70px;
    height: 30px;
    border-radius: 2px;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    margin-right: 5px;
  }

  #sliderinput::-webkit-input-placeholder {
    color: #d9d9d9;
    font-size: 14px;
    padding-left: 5px;
  }
  #sliderrange .ant-slider-step {
    border: 1px solid #969696;
    border-radius: 2px;
  }
  //descriptions
  .ant-descriptions-item-container {
    border-bottom: 1px solid #f9f9f9;
  }
  .ant-descriptions-item-label {
    width: 110px;
    background-color: #fafafa;
    min-height: 54px;
    line-height: 54px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #202020;
    font-size: 14px;
  }

  .ant-descriptions-item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-left: 15px;
    color: #595959;
    font-size: 14px;
    background-color: #ffffff;
  }
  .ant-descriptions-row > th,
  .ant-descriptions-row > td {
    padding-bottom: 0px !important;
  }
  .ant-descriptions-item-label::after {
    content: '';
  }
  // TreeSelect
  .ant-select-multiple .ant-select-selection-item {
    margin-top: 0;
    border: 1px solid rgba(0, 0, 0, 0.01);
    background: rgba(0, 0, 0, 0.05);
    font-size: 12px;
  }
  .ant-layout {
    background: transparent !important;
  }
  .ant-form-inline {
    .ant-form-item {
      margin-bottom: 8px;
    }
    .ant-form-item-bottom {
      margin-right: 0;
      margin-left: auto;
    }
  }
}
