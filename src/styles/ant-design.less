.klsz-baseui {

  // 单选框
  .ant-radio-inner {
    border-color: #c1c2c1;
  }

  .ant-radio-disabled .ant-radio-inner {
    background-color: #e9edf0;
    border-color: #c1c2c1 !important;
  }

  .ant-radio-disabled .ant-radio-inner::after {
    background-color: #e9edf0;
  }

  // 下拉选择框
  .ant-select-selection-placeholder {
    color: #c1c2c1;
    font-size: 16px;
  }

  .ant-select-selection-item {
    color: #666;
    font-size: 16px;
  }

  .ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    background: #eff0f4;
  }

  // 时间选择器
  .ant-picker {
    border-color: #e9edf0;
    height: 40px;
  }

  // 卡片
  .ant-card-head {
    font-weight: bold;
    border-bottom: 1px solid #EFF0F4;
  }

  .ant-card-body {
    padding: 30px 24px
  }

  // Tag
  .ant-tag {
    height: 22px;
  }

  // Modal
  .ant-modal-header {
    // height: 52px;
    box-sizing: border-box;
    border-bottom: 1px solid #EFF0F4;
  }

  .ant-modal-title {
    color: #333;
    font-weight: bold;
  }

  // .ant-modal-close-x {
  //   width: 52px;
  //   height: 52px;
  // }

  .ant-modal-body {
    padding: 30px 24px;
  }

  .ant-modal-footer {
    border-top: 1px solid #EFF0F4;
    box-sizing: border-box;
    padding-top: 16px;
    margin: 0px;
  }

  // 进度条背景色
  .ant-progress-inner {
    background: #e9edf0;
  }

  .ant-progress-text {
    color: #999;
    font-size: 12px;
  }

  .ant-input-affix-wrapper-focused {
    .ant-input-prefix {
      color: #2470ff;
    }
  }
  .ant-input-affix-wrapper {
    height: 32px;
  }

  // .ant-input {
  //   padding: 4px 12px;
  // }
.ant-table {
    .ant-table-tbody > tr > td {
      font-size: 12px;
      border-bottom-color: #e9edf0;
    }
    .ant-table-row {
      &.table-striped {
        background-color: #fbfcfc;
      }
    }
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td,
    .ant-table tfoot > tr > th,
    .ant-table tfoot > tr > td {
      padding: 12px 16px;
    }
    .ant-table-tbody > tr.ant-table-row-selected > td {
      background: rgb(36 112 255 / 3%);
    }
  }
  .ant-collapse {
    border: 1px solid #d9d9d9;
  }
  .ant-collapse > .ant-collapse-item > .ant-collapse-header {
    font-size: 16px;
    padding: 12px 18px;
  }
  .ant-collapse
    > .ant-collapse-item
    > .ant-collapse-header
    .ant-collapse-arrow {
    margin-right: 16px;
  }
  // upload
  .ant-upload.ant-upload-select-picture-card {
    background-color: transparent;
    border-color: #d9d9d9;
  }

  .ant-upload-list-item-info .anticon-loading .anticon,
  .ant-upload-list-item-info .ant-upload-text-icon .anticon {
    font-size: 16px;
  }

  .ant-upload-list-text .ant-upload-list-item-name,
  .ant-upload-list-picture .ant-upload-list-item-name {
    font-size: 12px;
  }

  .ant-upload.ant-upload-drag {
    background: transparent;
    border: 1px dashed #d9d9d9;
  }

  .ant-upload.ant-upload-drag p.ant-upload-drag-icon {
    margin-bottom: 16px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-text {
    font-size: 14px;
    margin: 0px 0px 8px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-hint {
    color: #666;
    font-size: 12px;
  }

  // message消息提示框
  .ant-message-notice-content {
    padding: 9px 16px;
  }

  .ant-message-warning .anticon {
    color: #ff9900;
  }

  // tree树形组件
  .ant-tree-switcher .ant-tree-switcher-icon,
  .ant-tree-switcher .ant-select-tree-switcher-icon {
    font-size: 12px;
    color: #666;
  }

  .ant-tree .ant-tree-node-content-wrapper {
    color: #666;
    font-size: 14px;
  }

  .ant-tree-switcher {
    width: 28px;
  }

  .ant-tree-checkbox-inner {
    border-color: #c1c2c1;
  }

  .ant-tree .ant-tree-node-content-wrapper:hover {
    background-color: #eff0f4;
  }

  .ant-tree-treenode-checkbox-checked .ant-tree-node-content-wrapper:hover {
    background-color: rgba(36, 112, 255, 0.1);
  }

  .ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
    border-color: #c1c2c1 !important;
    background-color: #e9edf0;
  }

  .ant-tree-treenode-disabled .ant-tree-node-content-wrapper {
    color: #c1c2c1;
  }

  // tabs标签页
  .ant-tabs-top>.ant-tabs-nav::before,
  .ant-tabs-bottom>.ant-tabs-nav::before,
  .ant-tabs-top>div>.ant-tabs-nav::before,
  .ant-tabs-bottom>div>.ant-tabs-nav::before {
    border-color: #eff0f4;
  }

  .ant-tabs-tab+.ant-tabs-tab {
    margin: 0 0 0 24px;
  }

  .ant-tabs-left>.ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-right>.ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-left>div>.ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-right>div>.ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }
  // .ant-form-item-control-input {
  //   max-height: 32px;
  // }
}