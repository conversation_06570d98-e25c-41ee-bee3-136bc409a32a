import * as path from 'path';
import { writeFileSync } from 'fs';
import { defineConfig, loadEnv, UserConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import eslintPlugin from 'vite-plugin-eslint';
import legacy from '@vitejs/plugin-legacy';
import { visualizer } from 'rollup-plugin-visualizer';
import AutoImport from 'unplugin-auto-import/vite';
import {
  createStyleImportPlugin,
  VxeTableResolve,
  AndDesignVueResolve,
} from 'vite-plugin-style-import';
import svgLoader from 'vite-svg-loader';
import { createHtmlPlugin } from 'vite-plugin-html';
// import Icons from 'unplugin-icons/vite';
// import IconsResolver from 'unplugin-icons/resolver';
import vueJsx from '@vitejs/plugin-vue-jsx';
import normalTheme from './theme/normal';
import darkTheme from './theme/dark';

export default defineConfig(({ command, mode }) => {
  const { VITE_APP_THEME, VITE_APP_TITLE, VITE_APP_MICRO_BASE } = loadEnv(
    mode,
    process.cwd()
  );
  const result: UserConfig = {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
      extensions: ['.vue', '.js', '.ts', '.tsx'],
    },
    plugins: [
      vue(),
      legacy({
        targets: ['defaults', 'not IE 11'],
      }),
      // eslintPlugin(),
      createStyleImportPlugin({
        resolves: [VxeTableResolve()],
      }),
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        resolvers: [AntDesignVueResolver()],
        dts: './src/auto-imports.d.ts',
        eslintrc: {
          enabled: true,
        },
      }),
      Components({
        dirs: ['src/components'],
        extensions: ['vue', 'tsx'],
        resolvers: [
          AntDesignVueResolver({
            importStyle: false, // 禁用自动导入样式，避免路径问题
            resolveIcons: true,
          }),
        ],
        dts: './src/components.d.ts',
      }),
      // Icons({
      //   compiler: 'vue3',
      //   autoInstall: false,
      // }),
      vueJsx(),
      svgLoader(),
      createHtmlPlugin({
        inject: {
          data: {
            title: VITE_APP_TITLE,
            theme: VITE_APP_THEME,
          },
        },
      }),
    ],
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: VITE_APP_THEME === 'dark' ? darkTheme : normalTheme,
          javascriptEnabled: true,
        },
        scss: {},
      },
    },
    server: {
      port: 3000,
      open: true,
      proxy: {
        // '^/equipment/.*': {
        //   target: 'http://***********:7001',
        //  // target: 'http://*************:7001',
        //   changeOrigin: true,
        // rewrite: path => path.replace(/\/api/, ''),
      },
    },
    esbuild: {
      jsxFactory: 'h',
      jsxFragment: 'Fragment',
      jsxInject: "import { h } from 'vue';",
    },
    build: {
      // 消除打包大小超过500kb警告
      chunkSizeWarningLimit: 2000,
      // 静态资源打包到dist下的不同目录
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        },
      },
    },
  };
  if (mode === 'micro') {
    result.base = VITE_APP_MICRO_BASE;
    result.build.assetsDir = 'static';
    result.plugins.push(
      (function () {
        let basePath = '';
        return {
          name: 'vite:micro-app',
          apply: 'build',
          configResolved(config) {
            basePath = `${config.base}${config.build.assetsDir}/js/`;
          },
          writeBundle(options, bundle) {
            for (const chunkName in bundle) {
              if (Object.prototype.hasOwnProperty.call(bundle, chunkName)) {
                const chunk = bundle[chunkName] as any;
                if (chunk.fileName && chunk.fileName.endsWith('.js')) {
                  chunk.code = chunk.code.replace(
                    /(from|import\()(\s*['"])(\.\.?\/)/g,
                    (all, $1, $2, $3) => {
                      return all.replace($3, new URL($3, basePath));
                    }
                  );
                  const fullPath = path.join(options.dir, chunk.fileName);
                  writeFileSync(fullPath, chunk.code);
                }
              }
            }
          },
        };
      })()
    );
  }
  if (mode === 'stats') {
    result.plugins.push(visualizer());
  }
  return result;
});
